services:
  meilisearch:
    image: getmeili/meilisearch:v1.5.1
    restart: always
    ports:
      - 7700:7700
    environment:
      - MEILI_MASTER_KEY=meili-master-key007
    volumes:
      - ./meili_data:/meili_data
  directus:
    container_name: directus
    image: directus/directus:11.9.3
    restart: always
    ports:
      - 8055:8055
    volumes:
      - ./extensions:/directus/extensions
      - ./uploads:/directus/uploads
    environment:
      KEY: "255d861b-5ea1-5996-9aa3-922530ec40b1"
      SECRET: "6116487b-cda1-52c2-b5b5-c8022c45e263"
      EXTENSIONS_AUTO_RELOAD: true
      ACCESS_TOKEN_TTL: "7d"
      CONTENT_SECURITY_POLICY_DIRECTIVES__FRAME_SRC: http://localhost:8055
      CONTENT_SECURITY_POLICY_directives__child-src: "https://docs.google.com https://beta.seekers.my http://localhost:3001"
      DB_CLIENT: "mysql"
      DB_HOST: "ls-39261ef25c59db3a81ca3a116454e0fa950aea98.cvegjij1qeln.ap-southeast-1.rds.amazonaws.com"
      DB_PORT: "3306"
      DB_DATABASE: "directusdb"
      DB_USER: "directus"
      DB_PASSWORD: "directusdb"
      FILES_MAX_UPLOAD_SIZE: "100mb"
      ADMIN_EMAIL: "<EMAIL>"
      ADMIN_PASSWORD: "directus"
      CORS_ENABLED: "true"
      CORS_ORIGIN: "https://seekers.my,https://www.seekers.my,http://localhost:8055,http://localhost:3001"
      CORS_METHODS: "GET,POST,PATCH,DELETE"
      CORS_ALLOWED_HEADERS: "Content-Type,Authorization,X-Requested-With"
      CORS_EXPOSED_HEADERS: "Content-Range,X-Content-Range"
      CACHE_ENABLED: false
      CACHE_TTL: "1m"
      CACHE_AUTO_PURGE: true
      EMAIL_VERIFY_SETUP: true
      EMAIL_FROM: "<EMAIL>"
      EMAIL_TRANSPORT: "mailgun"
      # EMAIL_MAILGUN_API_KEY: '**************************************************'
      EMAIL_MAILGUN_API_KEY: "**************************************************"
      EMAIL_MAILGUN_DOMAIN: "seekers.com.my"
      PUBLIC_URL: "https://directus.seekers.my"
      STORAGE_LOCATIONS: "AWS"
      STORAGE_AWS_DRIVER: "s3"
      STORAGE_AWS_KEY: "********************"
      STORAGE_AWS_SECRET: "/G+eZVBSP42MTXrrQDXejzC8aVjG61LrGHX9nVTS"
      STORAGE_AWS_BUCKET: "seekers-directus"
      STORAGE_AWS_REGION: "ap-southeast-1"
      STORAGE_AWS_SERVER_SIDE_ENCRYPTION: "false"
      AUTH_PROVIDERS: "google,linkedin,github"
      AUTH_GOOGLE_MODE: "json"
      AUTH_GOOGLE_DRIVER: "openid"
      AUTH_GOOGLE_CLIENT_ID: "***********-no1rvo0aqkals7glqf8th732ln698d3s.apps.googleusercontent.com"
      AUTH_GOOGLE_CLIENT_SECRET: "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"
      AUTH_GOOGLE_ISSUER_URL: "https://accounts.google.com/.well-known/openid-configuration"
      AUTH_GOOGLE_IDENTIFIER_KEY: "email"
      AUTH_GOOGLE_ALLOW_PUBLIC_REGISTRATION: "true"
      AUTH_GOOGLE_DEFAULT_ROLE_ID: "38d4fed7-ba46-44d3-a69c-617e121823cd"
      AUTH_GOOGLE_REDIRECT_ALLOW_LIST: "http://localhost:3001/webhooks/auth/google"
      AUTH_LINKEDIN_DRIVER: "oauth2"
      AUTH_LINKEDIN_CLIENT_ID: "77exp7mnr97w2c"
      AUTH_LINKEDIN_CLIENT_SECRET: "qMkAiC7LBKxYxmH0"
      AUTH_LINKEDIN_IDENTIFIER_KEY: "r_emailaddress"
      AUTH_LINKEDIN_AUTHORIZE_URL: "https://www.linkedin.com/oauth/v2/authorization"
      AUTH_LINKEDIN_ACCESS_URL: "https://www.linkedin.com/oauth/v2/accessToken"
      AUTH_LINKEDIN_PROFILE_URL: "https://api.linkedin.com/v2/me"
      AUTH_LINKEDIN_SCOPE: "r_emailaddress,r_liteprofile"
      AUTH_LINKEDIN_ALLOW_PUBLIC_REGISTRATION: "true"
      AUTH_LINKEDIN_DEFAULT_ROLE_ID: "38d4fed7-ba46-44d3-a69c-617e121823cd"
      AUTH_LINKEDIN_REDIRECT_ALLOW_LIST: "http://localhost:3001/webhooks/auth/linkedin"
      # AUTH_FACEBOOK_DRIVER: "oauth2"
      # AUTH_FACEBOOK_CLIENT_ID: "..."
      # AUTH_FACEBOOK_CLIENT_SECRET: "..."
      # AUTH_FACEBOOK_AUTHORIZE_URL: "https://www.facebook.com/dialog/oauth"
      # AUTH_FACEBOOK_ACCESS_URL: "https://graph.facebook.com/oauth/access_token"
      # AUTH_FACEBOOK_PROFILE_URL: "https://graph.facebook.com/me?fields=email"
      AUTH_GITHUB_DRIVER: "oauth2"
      AUTH_GITHUB_CLIENT_ID: "********************"
      AUTH_GITHUB_CLIENT_SECRET: "4c37b004346849fe68f35b9c280a53df15cb017b"
      AUTH_GITHUB_AUTHORIZE_URL: "https://github.com/login/oauth/authorize"
      AUTH_GITHUB_ACCESS_URL: "https://github.com/login/oauth/access_token"
      AUTH_GITHUB_PROFILE_URL: "https://api.github.com/user"
      AUTH_GITHUB_ALLOW_PUBLIC_REGISTRATION: "true"
      AUTH_GITHUB_DEFAULT_ROLE_ID: "38d4fed7-ba46-44d3-a69c-617e121823cd"
      AUTH_GITHUB_REDIRECT_ALLOW_LIST: "http://localhost:3001/webhooks/auth/github"
      REFRESH_TOKEN_COOKIE_DOMAIN: "directus.seekers.my"
      REFRESH_TOKEN_COOKIE_SECURE: "true"
      REFRESH_TOKEN_COOKIE_SAME_SITE: "None"
