// Estimatated execution time: 10 minutes

import { and, isNot<PERSON>ull, isNull, ne, sql } from "drizzle-orm";
import { consola } from "consola";
import {
  createItem,
  createItems,
  createUser,
  readItem,
  readItems,
  readUser,
  readUsers,
  updateItem,
  updateUser,
} from "@directus/sdk";
import { asc, desc, eq } from "drizzle-orm";

import * as schema from "./drizzle/schema.ts";
import { cleanup, sourceDb, targetDb, directus } from "./migrationUtil.ts";
import PQueue from "p-queue";

export const migrateTaggables = async () => {
  //@
  //@
  //@ Iteration START
  //@
  //@
  const queue = new PQueue({ concurrency: 3 });

  const tags = await sourceDb
    .select({ id: schema.tags.id, name: schema.tags.name })
    .from(schema.tags);

  // transform the {id , name} to {id: name}
  const tagMap = tags.reduce((acc, tag) => {
    // lowercase everything
    // skip those with name longer that 60 characters or shorter than 3
    if ((tag.name as any).en.length > 60 || (tag.name as any).en.length < 3)
      return acc;
    acc[tag.id] = (tag.name as any).en
      .toLowerCase()
      .replace("\\", "")
      .replace("_", " ");
    return acc;
  }, {});

  const taggables = await sourceDb
    .select({
      tagId: schema.taggables.tagId,
      taggableId: schema.taggables.taggableId,
    })
    .from(schema.taggables)
    .where(eq(schema.taggables.taggableType, "App\\Models\\Candidate"));

  consola.info(`Found ${taggables.length} taggables. Grouping.....`);

  // group taggables by taggableId into its own object. join the tagIds into an array
  const groupedTaggables: {
    [key: string]: { taggableId: string; tagId: string[] };
  } = taggables.reduce((acc, taggable) => {
    if (!acc[taggable.taggableId]) {
      acc[taggable.taggableId] = {
        taggableId: taggable.taggableId,
        tagId: [taggable.tagId],
      };
    } else {
      acc[taggable.taggableId].tagId.push(taggable.tagId);
    }
    return acc;
  }, {});
  delete groupedTaggables.tagId;
  delete groupedTaggables.taggableId;

  Object.values(groupedTaggables).forEach(async (group) => {
    await queue.add(async () => {
      try {
        let tags = group.tagId.map((tagId) => tagMap[tagId]);
        tags = tags.filter((element) => element !== undefined);
        // remove any undefined tags
        await directus.request(
          updateItem("candidates", group.taggableId, {
            tags,
          })
        );
        consola.success(`Migrated candidate: ${group.taggableId}`);
      } catch (error) {
        consola.error(error);
      }
    });
  });
};

await migrateTaggables();
await cleanup();
