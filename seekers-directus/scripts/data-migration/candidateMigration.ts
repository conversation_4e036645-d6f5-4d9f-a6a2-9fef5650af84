// Estimatated execution time: 1 hour

import { and, isNot<PERSON>ull, isNull, ne, sql } from "drizzle-orm";
import { consola } from "consola";
import {
  createItem,
  createItems,
  createUser,
  readItem,
  readUser,
  readUsers,
  updateUser,
} from "@directus/sdk";
import { asc, desc, eq } from "drizzle-orm";

import * as schema from "./drizzle/schema.ts";
import { cleanup, sourceDb, targetDb, directus } from "./migrationUtil.ts";
import PQueue from "p-queue";

export const migrateCandidates = async () => {
  //@
  //@
  //@ Iteration START
  //@
  //@
  const queue = new PQueue({ concurrency: 5 });

  const candidates = await sourceDb.select().from(schema.candidates);

  candidates.map(
    async (candidate) =>
      await queue.add(async () => {
        try {
          const newCandidateObject = {
            about_me: candidate.aboutMe ?? "",
            birth_year: candidate.birthYear ?? undefined,
            current_position: candidate.currentPosition ?? "",
            current_salary: candidate.currentSalary ?? undefined,
            cv_masking: candidate.cvMasking ?? "",
            cv_rating: candidate.cvRating ?? undefined,
            cv_report: candidate.cvReport ?? "",
            cv_updated_at: candidate.cvUpdatedAt ?? "",
            cv_verified: candidate.cvVerified ?? "",
            email: candidate.email.replace("mail.con", "mail.com") ?? "",
            expected_salary: candidate.expectedSalary ?? undefined,
            experience_data: (candidate.experienceData as any) ?? undefined,
            facebook: candidate.facebook ?? "",
            gender: candidate.gender ?? "male",
            github: candidate.github ?? "",
            got_visa: candidate.gotVisa ?? 0,
            has_work_permit: candidate.gotWorkPermit ?? 0,
            ic: candidate.ic ?? "",
            image: candidate.image ?? undefined,
            image_url: candidate.imageUrl ?? "",
            instagram: candidate.instagram ?? "",
            is_app: candidate.isApp ?? 0,
            is_blacklisted: candidate.isBlacklisted ?? 0,
            is_local: candidate.isLocal ?? 1,
            is_verified: candidate.isVerified ?? 0,
            languages:
              candidate.language?.split(",").map((lang) => `${lang}-native`) ??
              undefined,
            last_name: candidate.lastLogin ?? undefined,
            last_seen: candidate.lastSeen ?? undefined,
            level: candidate.level ?? undefined,
            linkedin: candidate.linkedin ?? undefined,
            location: candidate.location ?? undefined,
            mobile: candidate.mobile ?? "**********",
            name: candidate.name ?? "Missing Name",
            nationality: candidate.nationality ?? undefined,
            notice_period: candidate.noticePeriod ?? "1-months",
            offer_data: candidate.offerData ?? undefined,
            open_to_work: candidate.openToWork ?? 1,
            own_driver_license: candidate.ownDriverLicense ?? 0,
            own_transport: candidate.ownTransport ?? 0,
            portfolio: candidate.portfolio ?? undefined,
            race: candidate.race ?? undefined,
            remarks: candidate.remarks ?? undefined,
            skills: candidate.skills ?? undefined,
            state: candidate.state ?? undefined,
            status: "active",
            twitter: candidate.twitter ?? "",
            website: candidate.website ?? "",
            willing_to_travel: candidate.willingToTravel ?? 0,
          };
          consola.start(`Processing candidate: ${candidate.id}`);

          //
          //
          // CV
          //
          //

          // Create new directus_file with cv s3 details
          const cvId = crypto.randomUUID();
          if (candidate.cv) {
            const baseCVRecord = {
              id: cvId,
              storage: "AWS",
              filename_disk: candidate.cv,
              filename_download: candidate.cv?.replace("cv/", ""),
              title: candidate.cv?.replace("cv/", ""),
              type: "application/pdf",
              folder: "9128ef54-9070-449f-8904-3d03ff8c812b",
            };

            // insert new record to directus_files using drizzle sql
            const fileId = await targetDb.execute(
              sql`insert into directus_files (id, storage, filename_disk, filename_download, title, type, folder) values ( ${baseCVRecord.id} ,'AWS', ${baseCVRecord.filename_disk}, ${baseCVRecord.filename_download}, ${baseCVRecord.title}, ${baseCVRecord.type}, ${baseCVRecord.folder})`
            );

            consola.log(`CV file added: ${cvId}`);
          }

          // Create new candidate with the added CV record
          const newCandidate = await directus.request(
            createItem("candidates", {
              ...newCandidateObject,
              id: candidate.id.toString(),
              status: "active",
              cv: candidate.cv ? cvId : undefined,
              education_history: {},
            })
          );
          consola.log(`Candidate created: ${newCandidate.id}`);

          //
          //
          // ROLES (Add entries to candidate_roles table)
          //
          //

          // Add candidate roles relations
          if (candidate.roleId) {
            await directus.request(
              createItem("candidates_roles", {
                candidates_id: newCandidate.id,
                roles_id: `${candidate.roleId}`,
              })
            );
            consola.log(`Role added`);
          }

          if (candidate.roleIds && candidate.roleIds.length > 0) {
            await directus.request(
              createItems(
                "candidates_roles",
                candidate.roleIds!.split(",").map((roleId) => ({
                  candidates_id: newCandidate.id,
                  roles_id: roleId,
                }))
              )
            );
            consola.log(`RoleIds added`);
          }

          //
          //
          //  Check if directus_user with email exists, if not create one. If yes, update the candidate_profile
          //
          //

          // Check if user exists
          consola.log(`Checking user: ${candidate.email}`);
          const existingUser = await directus.request(
            readUsers({ filter: { email: { _eq: candidate.email } } })
          );
          if (existingUser.length > 0) {
            consola.log(`User found: ${existingUser[0].id}`);
            // Update user candidate_profile
            await directus.request(
              updateUser(existingUser[0].id, {
                candidate_profile: newCandidate.id ?? candidate.id,
              })
            );
          } else {
            await directus.request(
              createUser({
                first_name: candidate.name,
                email: candidate.email,
                candidate_profile: newCandidate.id ?? candidate.id,
                provider: candidate.googleToken ? "google" : "default",
                has_migrated_password: candidate.googleToken ? true : false,
              })
            );
          }
        } catch (err) {
          consola.warn(candidate);
          consola.error(err);
        }
      })
  );
};

await migrateCandidates();
// await cleanup();
