import { drizzle } from "drizzle-orm/mysql2";
import mysql from "mysql2/promise";
import {
  createDirectus,
  rest,
  staticToken,
} from "@directus/sdk";

import * as schema from "./drizzle/schema.ts";
import DirectusSchema from "./directusTypes.ts";

//
// CONFIGS
//
const url = "http://localhost:8055/";
const access_token = "qejTFYs7o_PhQl5Eb5xL5egXFICFHtnT";
const sourceConfig = {
  host: "127.0.0.1",
  user: "root",
  database: "seekers_production",
  port: 3306,
};
const targetConfig = {
  host: "127.0.0.1",
  user: "root",
  password: "",
  database: "seekers_production",
  port: 3308,
};

//
// INITIALIZATION
//

const sourceConnection = await mysql.createConnection(sourceConfig);
const targetConnection = await mysql.createConnection(targetConfig);

export const sourceDb = drizzle(sourceConnection, { schema, mode: "default" });
export const targetDb = drizzle(targetConnection, { schema, mode: "default" });

export const directus = createDirectus<DirectusSchema>(url)
  .with(staticToken(access_token))
  .with(rest());

export async function cleanup() {
  await sourceConnection.end();
  await targetConnection.end();
}