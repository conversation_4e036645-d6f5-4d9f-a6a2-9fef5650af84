// Estimatated execution time: 30seconds

import { and, isNot<PERSON>ull, isNull, ne, sql } from "drizzle-orm";
import { consola } from "consola";
import {
  createItem,
  createItems,
  createUser,
  readItem,
  readUser,
  readUsers,
  updateUser,
} from "@directus/sdk";
import { asc, desc, eq } from "drizzle-orm";

import * as schema from "./drizzle/schema.ts";
import { cleanup, sourceDb, targetDb, directus } from "./migrationUtil.ts";
import PQueue from "p-queue";

export const migrateTags = async () => {
  //@
  //@
  //@ Iteration START
  //@
  //@
  const queue = new PQueue({ concurrency: 10 });

  const tags = await sourceDb.select().from(schema.tags);

  consola.start(`Migrating ${tags.length} tags...`);

  tags.map(async (tag) => {
    await queue.add(async () => {
      try {
        const parsedTag = (tag.name as any).en
        const sluggedTag = parsedTag.toLowerCase().replace(/ /g, "-")
        if(parsedTag.length > 60) return;

        await directus.request(createItem("tags", {
          id: tag.id,
          name: parsedTag,
          slug: sluggedTag
        }))

        consola.success(`Migrated tag: ${parsedTag}`);
      } catch (error) {
        consola.error(`Failed to migrate tag: ${tag.id}`);
        consola.error(JSON.stringify(tag,null,2))
        consola.error(error);
      }
    })
  })
}

await migrateTags();
await cleanup();