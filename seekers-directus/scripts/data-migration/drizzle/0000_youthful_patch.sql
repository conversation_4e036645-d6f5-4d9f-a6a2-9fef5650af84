-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TABLE `action_events` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`batch_id` char(36) NOT NULL,
	`user_id` int unsigned NOT NULL,
	`name` varchar(191) NOT NULL,
	`actionable_type` varchar(191) NOT NULL,
	`actionable_id` int unsigned NOT NULL,
	`target_type` varchar(191) NOT NULL,
	`target_id` int unsigned NOT NULL,
	`model_type` varchar(191) NOT NULL,
	`model_id` int unsigned,
	`fields` text NOT NULL,
	`status` varchar(25) NOT NULL DEFAULT 'running',
	`exception` text NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	`original` text,
	`changes` text,
	CONSTRAINT `action_events_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `activities` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int unsigned,
	`applicant_id` int unsigned,
	`event_id` int unsigned NOT NULL,
	`amount` int,
	`point` int,
	`screening_answer` text,
	`cv_masking` varchar(191),
	`interview_no` tinyint DEFAULT 1,
	`interview_code` varchar(191),
	`interview_location` varchar(191),
	`interview_datetime` varchar(191),
	`interview_date_time` datetime,
	`interview_pic` varchar(191),
	`interview_pic_tel` varchar(191),
	`interview_remarks` text,
	`interview_pic_position` varchar(191),
	`interview_accept` tinyint,
	`interview_reschedule` varchar(191),
	`feedback` varchar(191),
	`remark` text,
	`remark_client` varchar(191),
	`failure_reason` varchar(191),
	`failure_reason_detail` text,
	`dev_note` text,
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `activities_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `announcements` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(191) NOT NULL,
	`description` varchar(191),
	`image_path` varchar(191),
	`link_to` varchar(191),
	`is_link_target_blank` tinyint NOT NULL DEFAULT 0,
	`type` varchar(191) NOT NULL DEFAULT 'general',
	`is_alert` tinyint NOT NULL DEFAULT 0,
	`status` varchar(191) NOT NULL DEFAULT 'draft',
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `announcements_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `app_version` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`version` varchar(191) NOT NULL,
	`device` enum('android','ios') DEFAULT 'android',
	`remark` text,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `app_version_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `applicants` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`job_id` int unsigned NOT NULL,
	`user_id` int unsigned DEFAULT 1,
	`current_event_id` int unsigned NOT NULL,
	`internal_event_id` int unsigned,
	`rating` int NOT NULL,
	`verification_code` varchar(191),
	`is_verified` tinyint,
	`notice_period` varchar(191),
	`reason_to_apply` text,
	`reason_to_leave` text,
	`screening_answer` text,
	`salary_current` int,
	`salary_expected` int,
	`salary_final` int,
	`auto_screen_pass` tinyint,
	`auto_screen_report` text,
	`note` text,
	`note_client` varchar(191),
	`report` varchar(191),
	`urgent` varchar(191),
	`is_suggested` tinyint NOT NULL DEFAULT 0,
	`matching_score` decimal(8,2),
	`matching_report` json,
	`scout_data` json,
	`apply_data` json,
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	`job_screening_answers` json,
	CONSTRAINT `applicants_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `assesments` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`question` varchar(191),
	`o1` varchar(191) NOT NULL,
	`o2` varchar(191) NOT NULL,
	`o3` varchar(191) NOT NULL,
	`o4` varchar(191) NOT NULL,
	`correct_answer` varchar(191) NOT NULL,
	`image` varchar(191),
	CONSTRAINT `assesments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `bill_items` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`bill_id` int unsigned NOT NULL,
	`product_id` int unsigned NOT NULL,
	`price` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `bill_items_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `bills` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`coupon_id` int unsigned,
	`status` enum('pending','paid','cancel'),
	`total_price` decimal(8,2) NOT NULL,
	`tax` int NOT NULL DEFAULT 6,
	`net_price` decimal(8,2) NOT NULL,
	`payment_ref` varchar(191),
	`payment_method` enum('card','banktransfer','cheque'),
	`expiry_date` timestamp,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `bills_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_certification` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`name` varchar(191),
	`description` varchar(191),
	`start` date,
	`end` date,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `candidate_certification_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_company` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`company_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_company_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_education` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`qualification` varchar(191),
	`title` varchar(191),
	`institute` varchar(191),
	`description` varchar(191),
	`start` date,
	`end` date,
	`result` varchar(191),
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `candidate_education_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_favourite` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_favourite_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_following` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_following_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_inbox` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`inboxable_type` varchar(191) NOT NULL,
	`inboxable_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_inbox_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_language` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`language` varchar(191) NOT NULL,
	`level` enum('Basic','Intermediate','Advance','Native') NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_language_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_logs` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	`job_id` int unsigned NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`event` varchar(191) NOT NULL,
	CONSTRAINT `candidate_logs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_onesignal` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`player_id` varchar(191) NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_onesignal_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_portfolio` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(191) NOT NULL,
	`description` varchar(191) NOT NULL,
	`link` varchar(191),
	`candidate_id` int unsigned NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp ON UPDATE CURRENT_TIMESTAMP,
	`file` varchar(191),
	CONSTRAINT `candidate_portfolio_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_scout_tags` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`is_active_looking` tinyint NOT NULL DEFAULT 0,
	`is_short_notice` tinyint NOT NULL DEFAULT 0,
	`is_ready` tinyint NOT NULL DEFAULT 0,
	`is_new` tinyint NOT NULL DEFAULT 0,
	`is_updated_cv` tinyint NOT NULL DEFAULT 0,
	`is_hot` tinyint NOT NULL DEFAULT 0,
	`is_fresh_graduate` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_scout_tags_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_skill` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`skill` varchar(191) NOT NULL,
	`rate` varchar(191) NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_skill_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidate_work` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`company_name` varchar(191) NOT NULL,
	`role` varchar(191) NOT NULL,
	`start` date,
	`end` date,
	`responsibilities` text,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `candidate_work_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `candidates` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(191) NOT NULL,
	`email` varchar(191) NOT NULL,
	`password` varchar(191),
	`mobile` varchar(191),
	`image` varchar(191),
	`ic` varchar(191),
	`birth_year` int,
	`gender` varchar(191),
	`location` varchar(191),
	`state` varchar(191),
	`language` varchar(191),
	`race` varchar(191),
	`linkedin` varchar(191) DEFAULT '',
	`portfolio` varchar(191),
	`cv` varchar(191),
	`cv_masking` varchar(191),
	`cv_rating` int,
	`cv_report` text,
	`cv_verified` tinyint NOT NULL DEFAULT 0,
	`is_blacklisted` tinyint,
	`is_local` tinyint,
	`own_transport` tinyint,
	`own_driver_license` tinyint,
	`willing_to_travel` tinyint,
	`got_work_permit` tinyint,
	`got_visa` tinyint,
	`is_verified` tinyint,
	`user_id` int,
	`created_at` timestamp,
	`updated_at` timestamp,
	`current_position` varchar(191),
	`level` enum('entry','junior','senior','manager','senior manager'),
	`role_id` int unsigned,
	`role_ids` varchar(191),
	`language_level` varchar(191),
	`followup` varchar(191),
	`remarks` varchar(191),
	`skills` json,
	`is_offer` tinyint NOT NULL DEFAULT 0,
	`offer_data` json,
	`experience_data` json,
	`is_app` tinyint NOT NULL DEFAULT 0,
	`is_primary` tinyint NOT NULL DEFAULT 0,
	`is_migrated` tinyint NOT NULL DEFAULT 0,
	`deleted_at` timestamp,
	`cv_updated_at` timestamp,
	`last_login` timestamp,
	`language_detailed` json,
	`education` varchar(191),
	`expected_salary` int,
	`education_level` varchar(191),
	`open_to_work` tinyint NOT NULL DEFAULT 1,
	`last_seen` timestamp,
	`current_salary` int,
	`notice_period` varchar(191),
	`google_id` int,
	`google_token` varchar(191),
	`google_refresh_token` varchar(191),
	`facebook_id` varchar(191),
	`linkedin_id` varchar(191),
	`about_me` text,
	`facebook` varchar(191) DEFAULT '',
	`github` varchar(191) DEFAULT '',
	`instagram` varchar(191) DEFAULT '',
	`twitter` varchar(191) DEFAULT '',
	`website` varchar(191) DEFAULT '',
	`nationality` varchar(191),
	`image_url` varchar(191),
	CONSTRAINT `candidates_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `challenges` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`event_id` int unsigned NOT NULL,
	`type` enum('special','daily','weekly','monthly') NOT NULL,
	`min_count` int unsigned NOT NULL,
	`max_count` int unsigned,
	`counting_method` text,
	`status` enum('active','inactive') NOT NULL DEFAULT 'active',
	`is_verified` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `challenges_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `chapters` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`heading` text NOT NULL,
	`subheading` text NOT NULL,
	`description` text,
	`reward` int,
	`image` text,
	`ranking` varchar(191),
	`sort_order` int,
	`status` enum('draft','open','closed') NOT NULL DEFAULT 'draft',
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `chapters_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `client_payments` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`uuid` char(36) NOT NULL,
	`client_id` int unsigned NOT NULL,
	`plan_id` int unsigned,
	`amount` decimal(8,2),
	`payment_method` json,
	`purchase_data` json,
	`invoice_data` json,
	`receipt_data` json,
	`status` varchar(191) NOT NULL DEFAULT 'pending',
	`status_data` json,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `client_payments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `client_scouts` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`client_id` int unsigned NOT NULL,
	`candidate_id` int unsigned NOT NULL,
	`job_id` int unsigned,
	`code` varchar(191) NOT NULL,
	`message` text,
	`deadline` date,
	`status` varchar(191),
	`extra_data` json,
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `client_scouts_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `client_subscriptions` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`client_id` int NOT NULL,
	`subscription_plan_id` int NOT NULL,
	`uuid` varchar(191),
	`old_expiry_date` date,
	`new_expiry_date` date,
	`is_expired` tinyint NOT NULL DEFAULT 0,
	`note` varchar(191),
	`json_data` json,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `client_subscriptions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `clients` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`name` varchar(191),
	`email` varchar(191) NOT NULL,
	`password` varchar(191) NOT NULL,
	`tel` varchar(191),
	`subscription_plan_id` int NOT NULL DEFAULT 1,
	`expiry_date` date,
	`job_quota` int NOT NULL DEFAULT 0,
	`scout_credit` int NOT NULL DEFAULT 0,
	`status` enum('pending','active','inactive') NOT NULL DEFAULT 'active',
	`is_verified` tinyint NOT NULL DEFAULT 0,
	`is_admin` tinyint NOT NULL DEFAULT 0,
	`last_login` timestamp,
	`stripe_id` varchar(191),
	`card_brand` varchar(191),
	`card_last_four` varchar(4),
	`trial_ends_at` timestamp,
	`remember_token` varchar(100),
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `clients_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `companies` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(191) NOT NULL,
	`slug` varchar(191),
	`reg_no` varchar(191),
	`building` varchar(191),
	`address` varchar(191),
	`postal_code` int,
	`city` varchar(191),
	`state` varchar(191),
	`location` varchar(191),
	`long` varchar(191),
	`lat` varchar(191),
	`industry_id` int unsigned NOT NULL,
	`size` varchar(191),
	`overview` text,
	`highlight` varchar(191),
	`logo` varchar(191),
	`is_generated_logo` tinyint NOT NULL DEFAULT 0,
	`background_image` varchar(191),
	`pic_email` varchar(191),
	`pic_phone` varchar(191),
	`allowances` varchar(191),
	`benefits` varchar(191),
	`annual_leave` varchar(191),
	`bonus` varchar(191),
	`culture_text` text,
	`culture_video_url` varchar(191),
	`culture_photo` varchar(191),
	`registration_data` json,
	`tnc_agree` tinyint NOT NULL DEFAULT 0,
	`is_highlight` tinyint NOT NULL DEFAULT 0,
	`is_top_employer` tinyint NOT NULL DEFAULT 0,
	`is_main` tinyint NOT NULL,
	`scoring_data` json,
	`score` decimal(8,2) NOT NULL DEFAULT '0.00',
	`is_srs_premium` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	`url` varchar(191),
	`video_url` varchar(191),
	`url_path` varchar(191),
	`corpcare_id` int unsigned,
	`deleted_at` timestamp,
	`remark_dev` varchar(191),
	CONSTRAINT `companies_id` PRIMARY KEY(`id`),
	CONSTRAINT `companies_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `company_attachment` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`attachment` varchar(191) NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `company_attachment_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_benefit` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`heading` varchar(191) NOT NULL,
	`description` varchar(191) NOT NULL,
	`icon` varchar(191),
	`text_color` varchar(191) DEFAULT '#000000',
	`background_color` varchar(191) DEFAULT '#F1F5F8',
	`order_no` smallint,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `company_benefit_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_competitor` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`name` varchar(191) NOT NULL,
	`link` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `company_competitor_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_image` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`type` enum('gallery','product'),
	`filename` varchar(191) NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	`caption` varchar(191),
	`priority` int,
	CONSTRAINT `company_image_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_interview_setting_jobs` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`interview_setting_id` int NOT NULL,
	`job_id` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `company_interview_setting_jobs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_interview_settings` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int NOT NULL,
	`is_general` tinyint NOT NULL,
	`location` varchar(191) NOT NULL,
	`pic_name` varchar(191) NOT NULL,
	`pic_phone` varchar(191) NOT NULL,
	`pic_position` varchar(191) NOT NULL,
	`default_remark` text,
	`status` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `company_interview_settings_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_story` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`company_id` int unsigned NOT NULL,
	`heading` varchar(191) NOT NULL,
	`content` text NOT NULL,
	`type` enum('text','video','image') NOT NULL DEFAULT 'text',
	`background_color` varchar(191) DEFAULT '#F1F5F8',
	`order_no` smallint,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `company_story_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `company_timeslots` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`interview_setting_id` int NOT NULL,
	`interview_id` int,
	`date` date NOT NULL,
	`time` time NOT NULL,
	`availability` tinyint NOT NULL DEFAULT 1,
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `company_timeslots_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `coupons` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`client_id` int,
	`code` varchar(191) NOT NULL,
	`fixed_discount` int,
	`percentage_discount` int,
	`status` enum('draft','active','disable','expired','used'),
	`expiry_date` timestamp,
	`extra_data` json,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `coupons_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `enquiries` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`subject` varchar(191) NOT NULL,
	`category` enum('Feedback','Bug','General','Employer') DEFAULT 'General',
	`content` text NOT NULL,
	`sender_name` varchar(191) NOT NULL,
	`sender_email` varchar(191) NOT NULL,
	`company` varchar(191),
	`phone` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `enquiries_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `events` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(191) NOT NULL,
	`alt_name` varchar(191),
	`code` varchar(191) NOT NULL,
	`amount_default` varchar(191) NOT NULL,
	`point_default` varchar(191) NOT NULL,
	`category` varchar(191) NOT NULL,
	`color` varchar(191),
	`icon` varchar(191),
	`priority` int,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `events_id` PRIMARY KEY(`id`),
	CONSTRAINT `events_code_unique` UNIQUE(`code`)
);
--> statement-breakpoint
CREATE TABLE `failed_jobs` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`connection` text NOT NULL,
	`queue` text NOT NULL,
	`payload` longtext NOT NULL,
	`exception` longtext NOT NULL,
	`failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `failed_jobs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `faqs` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`category` varchar(191) NOT NULL,
	`topic` varchar(191) NOT NULL,
	`question` varchar(191) NOT NULL,
	`answer` text NOT NULL,
	`status` enum('draft','open') NOT NULL,
	`sort_order` int,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `faqs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `getresponse_list` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`campaign_id` varchar(191) NOT NULL,
	`name` varchar(191) NOT NULL,
	`source` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `getresponse_list_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `industries` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`parent_id` int unsigned,
	`name` varchar(191) NOT NULL,
	`color` varchar(191),
	`icon` varchar(191),
	`image_path` varchar(191),
	`slug` varchar(191),
	`priority` int unsigned,
	`sort_order` int unsigned,
	`is_parent` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	`seo_title` varchar(191),
	`seo_description` text,
	`seo_image` varchar(191),
	CONSTRAINT `industries_id` PRIMARY KEY(`id`),
	CONSTRAINT `slug` UNIQUE(`slug`),
	CONSTRAINT `industries_slug_unique` UNIQUE(`slug`)
);
--> statement-breakpoint
CREATE TABLE `job_condition` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`working_days` varchar(191),
	`working_hour` varchar(191),
	`is_shift_work` tinyint NOT NULL DEFAULT 0,
	`allowances` varchar(191),
	`bonus` varchar(191),
	`annual_leave` varchar(191),
	`holiday` varchar(191),
	`probation` varchar(191),
	`benefits` varchar(191),
	`type` varchar(191),
	`remarks` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `job_condition_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_customs` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(191) NOT NULL,
	`slug` varchar(191) NOT NULL,
	`status` enum('show','hide') NOT NULL DEFAULT 'show',
	`remark` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `job_customs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_lists` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`job_custom_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `job_lists_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_requirement` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`age_min` int NOT NULL DEFAULT 18,
	`age_max` int,
	`gender` varchar(191),
	`race` varchar(191),
	`language` varchar(191),
	`is_local_only` tinyint,
	`own_transport` tinyint,
	`own_driver_license` tinyint,
	`willing_to_travel` tinyint,
	`got_work_permit` tinyint,
	`got_visa` tinyint,
	`got_portfolio` tinyint,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `job_requirement_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_revenue` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`amount` int NOT NULL,
	`status` enum('expected','actual') NOT NULL,
	`date` datetime NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `job_revenue_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_screening_questions` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	`job_id` int unsigned NOT NULL,
	`type` varchar(191) NOT NULL,
	`question` varchar(191),
	CONSTRAINT `job_screening_questions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_update` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`date_at` date NOT NULL,
	`note` varchar(191) NOT NULL,
	`tag` varchar(191),
	`tag_color` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `job_update_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `job_visibility` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int unsigned NOT NULL,
	`job_id` int unsigned NOT NULL,
	`show` tinyint NOT NULL,
	`hide` tinyint NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `job_visibility_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `jobs` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`package` enum('Premium','Pay-Per-Post','Free-Posting') DEFAULT 'Premium',
	`expiry_date` date,
	`status` enum('open','temporarily_closed','closed','draft'),
	`slug` varchar(191) NOT NULL DEFAULT '',
	`url_path_role` varchar(191),
	`is_different_location` tinyint DEFAULT 0,
	`city` varchar(191),
	`title` varchar(191) NOT NULL DEFAULT '',
	`description` text NOT NULL,
	`close_reason` varchar(191),
	`temp_close_reason` varchar(191),
	`industry_id` int unsigned,
	`company_id` int unsigned NOT NULL,
	`company_pic_name` varchar(191),
	`company_pic_email` varchar(191),
	`company_pic_phone` varchar(191),
	`sale_id` int unsigned,
	`consultant_id` int unsigned,
	`is_highlight` tinyint NOT NULL DEFAULT 0,
	`role_id` int unsigned NOT NULL,
	`level` varchar(191) NOT NULL,
	`location` varchar(191),
	`location_remark` varchar(191),
	`address` varchar(191),
	`building` varchar(191),
	`state` varchar(191),
	`postal_code` int,
	`long` varchar(191),
	`lat` varchar(191),
	`responsibility` text NOT NULL,
	`interview_method` varchar(191),
	`req_must_have` text,
	`req_others` text,
	`experience_min_years` varchar(191),
	`experience_field` varchar(191),
	`screening_question` text,
	`image` varchar(191),
	`is_processing` tinyint NOT NULL DEFAULT 0,
	`remark` text,
	`corporate_remark` text,
	`feature` varchar(191),
	`accept_fresh` tinyint NOT NULL DEFAULT 0,
	`accept_foreigner` tinyint NOT NULL DEFAULT 0,
	`salary_min` int NOT NULL,
	`salary_max` int NOT NULL,
	`reward_min` int,
	`reward_max` int,
	`sent_to_client_reward` int,
	`sent_to_client_limit` int,
	`shortlist_reward` int,
	`shortlist_limit` int NOT NULL,
	`is_reward_boost` tinyint NOT NULL DEFAULT 0,
	`boost_status` varchar(191),
	`amendment_note` text,
	`need_verification` tinyint NOT NULL DEFAULT 0,
	`video_url` varchar(191),
	`min_matching_rate` int unsigned NOT NULL DEFAULT 20,
	`indeed_data` json,
	`scoring_data` json,
	`score` decimal(12,8),
	`extra_data` json,
	`created_at` timestamp,
	`updated_at` timestamp,
	`revenue_model` varchar(191),
	`url_path_industry` varchar(191),
	`deleted_at` timestamp,
	`hide_from` json,
	`job_visibility_id` int unsigned,
	`hide_from_all_recruiter` tinyint DEFAULT 0,
	`is_staff_pick` tinyint DEFAULT 0,
	`show_to_recruiter_group` varchar(191) DEFAULT 'A,B',
	`publish_to_recruiters` tinyint DEFAULT 0,
	`is_remote` tinyint NOT NULL DEFAULT 0,
	CONSTRAINT `jobs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `jobs_queue` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`queue` varchar(191) NOT NULL,
	`payload` longtext NOT NULL,
	`attempts` tinyint NOT NULL,
	`reserved_at` int unsigned,
	`available_at` int unsigned NOT NULL,
	`created_at` int unsigned NOT NULL,
	CONSTRAINT `jobs_queue_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `master_lookups` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`value` varchar(191) NOT NULL,
	`type` varchar(191) NOT NULL,
	`json_data` json NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `master_lookups_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `migrate_attachments` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`disk_name` varchar(255) NOT NULL,
	`file_name` varchar(255) NOT NULL,
	`file_size` int NOT NULL,
	`content_type` varchar(255) NOT NULL,
	`title` varchar(255),
	`description` text,
	`field` varchar(255),
	`attachment_id` varchar(255),
	`attachment_type` varchar(255),
	`is_public` tinyint NOT NULL DEFAULT 1,
	`sort_order` int,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `migrate_attachments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `migrate_candidates` (
	`id` int AUTO_INCREMENT NOT NULL,
	`job_id` int NOT NULL,
	`referrer_id` int NOT NULL,
	`name` varchar(255) NOT NULL,
	`email` varchar(100) NOT NULL,
	`mobile` varchar(20) NOT NULL,
	`gender` enum('male','female'),
	`spoken_language` text,
	`citizenship` enum('malaysian','with permit','none'),
	`current_industry` varchar(255),
	`current_position` varchar(255),
	`current_salary` decimal(10,2),
	`expected_salary` decimal(10,2),
	`offered_salary` decimal(10,2) NOT NULL DEFAULT '0.00',
	`notice_period` varchar(255),
	`birth_year` year,
	`linkedin` varchar(255),
	`reward` decimal(10,2),
	`paid_amount` decimal(10,2) DEFAULT '0.00',
	`sent_status` tinyint,
	`interview_date` date,
	`interview_time` time,
	`interview_venue` text,
	`interviewer_name` varchar(255),
	`interview_status` tinyint DEFAULT 0,
	`commencement_date` datetime,
	`commencement_30_date` datetime,
	`remark` text,
	`offer_status` tinyint,
	`note` text,
	`screening_question` text,
	`status` tinyint NOT NULL,
	`blacklist` tinyint,
	`ip_address` varchar(20) NOT NULL,
	`created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_by` int DEFAULT 0,
	`updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `migrate_candidates_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `migrate_users` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(255),
	`email` varchar(255) NOT NULL,
	`contact_no` varchar(16) NOT NULL,
	`current_specialization` varchar(255),
	`recruiting_experience` int,
	`password` varchar(255) NOT NULL,
	`activation_code` varchar(255),
	`persist_code` varchar(255),
	`reset_password_code` varchar(255),
	`permissions` text,
	`is_activated` tinyint NOT NULL DEFAULT 0,
	`activated_at` datetime DEFAULT CURRENT_TIMESTAMP,
	`last_login` datetime DEFAULT CURRENT_TIMESTAMP,
	`created_at` datetime DEFAULT CURRENT_TIMESTAMP,
	`updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
	`username` varchar(255),
	`surname` varchar(255),
	`deleted_at` timestamp,
	`last_seen` datetime DEFAULT CURRENT_TIMESTAMP,
	`session_id` varchar(255),
	`affiliate_id` int,
	`is_affiliate` tinyint,
	`subscribe` tinyint NOT NULL DEFAULT 1,
	CONSTRAINT `migrate_users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_email_unique` UNIQUE(`email`),
	CONSTRAINT `users_login_unique` UNIQUE(`username`)
);
--> statement-breakpoint
CREATE TABLE `migrations` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`migration` varchar(191) NOT NULL,
	`batch` int NOT NULL,
	CONSTRAINT `migrations_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `milestone_events` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`milestone_id` int NOT NULL,
	`event_id` int NOT NULL,
	`count_method` varchar(191) NOT NULL DEFAULT 'count_activity',
	`min_count` int NOT NULL DEFAULT 1,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `milestone_events_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `milestones` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`rank_id` int unsigned NOT NULL,
	`next_rank_id` int unsigned NOT NULL,
	`name` varchar(191) NOT NULL,
	`description` text,
	`completion_reward` int NOT NULL,
	`order` int,
	`logo` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `milestones_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `nc_evolutions` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(255) NOT NULL,
	`titleDown` varchar(255),
	`description` varchar(255),
	`batch` int,
	`checksum` varchar(255),
	`status` int,
	`created` datetime,
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `nc_evolutions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `news` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(191) NOT NULL,
	`slug` varchar(191) NOT NULL,
	`content` text,
	`upload` varchar(191),
	`image` varchar(191),
	`type` enum('app','desktop','both') NOT NULL DEFAULT 'both',
	`is_featured` tinyint,
	`category` enum('promo','general','update') NOT NULL DEFAULT 'general',
	`active` tinyint NOT NULL DEFAULT 1,
	`push_notification` tinyint NOT NULL DEFAULT 0,
	`segment` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	`module` enum('jobfinder','recruiter','employer') DEFAULT 'recruiter',
	CONSTRAINT `news_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `notifications` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`heading` varchar(191) NOT NULL,
	`subheading` varchar(191) NOT NULL,
	`content` text NOT NULL,
	`type` enum('news','campaign','update','activity') NOT NULL DEFAULT 'news',
	`module` enum('recruiter','jobfinder','employer') DEFAULT 'recruiter',
	`url` varchar(191),
	`image` varchar(191),
	`segments` varchar(191),
	`player_ids` varchar(191),
	`created_by_activity` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `notifications_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `oauth_access_tokens` (
	`id` varchar(100) NOT NULL,
	`user_id` int,
	`client_id` int NOT NULL,
	`name` varchar(191),
	`scopes` text,
	`revoked` tinyint NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	`expires_at` datetime,
	CONSTRAINT `oauth_access_tokens_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `oauth_auth_codes` (
	`id` varchar(100) NOT NULL,
	`user_id` int NOT NULL,
	`client_id` int NOT NULL,
	`scopes` text,
	`revoked` tinyint NOT NULL,
	`expires_at` datetime,
	CONSTRAINT `oauth_auth_codes_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `oauth_clients` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int,
	`name` varchar(191) NOT NULL,
	`secret` varchar(100) NOT NULL,
	`redirect` text NOT NULL,
	`personal_access_client` tinyint NOT NULL,
	`password_client` tinyint NOT NULL,
	`revoked` tinyint NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `oauth_clients_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `oauth_personal_access_clients` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`client_id` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `oauth_personal_access_clients_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `oauth_refresh_tokens` (
	`id` varchar(100) NOT NULL,
	`access_token_id` varchar(100) NOT NULL,
	`revoked` tinyint NOT NULL,
	`expires_at` datetime,
	CONSTRAINT `oauth_refresh_tokens_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `password_resets` (
	`email` varchar(191) NOT NULL,
	`token` varchar(191) NOT NULL,
	`created_at` timestamp
);
--> statement-breakpoint
CREATE TABLE `popups` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`image` varchar(191) NOT NULL,
	`mode` enum('internal','external') NOT NULL,
	`page_type` enum('job','inbox'),
	`page_id` int,
	`external_url` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `popups_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `products` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(191) NOT NULL,
	`code` varchar(191) NOT NULL,
	`description` varchar(191),
	`status` enum('draft','active','disable') NOT NULL,
	`type` enum('package','addon') NOT NULL,
	`remark` varchar(191),
	`price` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `products_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `questions` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`industry_id` int unsigned,
	`text` varchar(191) NOT NULL,
	`tags` varchar(191),
	`is_endorsement` tinyint NOT NULL DEFAULT 0,
	`priority` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `questions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `quiz_option` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`quiz_id` bigint unsigned NOT NULL,
	`option_no` int NOT NULL,
	`text` varchar(191) NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `quiz_option_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `quizzes` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`chapter_id` bigint unsigned NOT NULL,
	`question` varchar(191) NOT NULL,
	`answer` int NOT NULL,
	`image` varchar(191),
	`description` text,
	`sort_order` int,
	`status` enum('draft','open','closed') NOT NULL DEFAULT 'draft',
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `quizzes_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `recommendations` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int NOT NULL,
	`type` varchar(191) NOT NULL DEFAULT 'jd',
	`data` json,
	`status` varchar(191) NOT NULL DEFAULT 'show',
	`snoozed_until` datetime,
	`created_at` timestamp,
	`updated_at` timestamp,
	`deleted_at` timestamp,
	CONSTRAINT `recommendations_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `roles` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`parent_id` int unsigned,
	`name` varchar(191) NOT NULL,
	`slug` varchar(191),
	`image` varchar(191),
	`is_parent` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	`icon` varchar(191),
	`color` varchar(191),
	`seo_title` varchar(191),
	`seo_description` text,
	`seo_image` varchar(191),
	`is_digital` tinyint NOT NULL DEFAULT 0,
	CONSTRAINT `roles_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `scout_templates` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(191) NOT NULL,
	`message` text NOT NULL,
	`status` varchar(191) NOT NULL DEFAULT 'draft',
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `scout_templates_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `screening_answers` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`applicant_id` int unsigned NOT NULL,
	`question_id` int unsigned NOT NULL,
	`answer` text NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `screening_answers_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `screening_questions` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`job_id` int unsigned NOT NULL,
	`industry_id` int unsigned,
	`question_id` int unsigned NOT NULL,
	`priority` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `screening_questions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `sessions` (
	`id` varchar(191) NOT NULL,
	`user_id` int unsigned,
	`ip_address` varchar(45),
	`user_agent` text,
	`payload` text NOT NULL,
	`last_activity` int NOT NULL,
	CONSTRAINT `sessions_id_unique` UNIQUE(`id`)
);
--> statement-breakpoint
CREATE TABLE `settings` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(191) NOT NULL,
	`type` varchar(191) NOT NULL,
	`value` varchar(191) NOT NULL,
	`extra` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `settings_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `site_alerts` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`batch_uuid` char(36),
	`type` varchar(191) NOT NULL DEFAULT 'Frontend',
	`level` varchar(191) NOT NULL DEFAULT 'error',
	`url` varchar(191),
	`user_data` json,
	`alert_data` json,
	`status` varchar(191) NOT NULL DEFAULT 'reported',
	`comments` text,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `site_alerts_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `skills` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` varchar(191) NOT NULL,
	`slug` int,
	`category` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `skills_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `srs_feedbacks` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`client_id` int NOT NULL,
	`attractive_feature` text,
	`improvement` text,
	`rating` int NOT NULL DEFAULT 1,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `srs_feedbacks_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `subscription_items` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`subscription_id` bigint unsigned NOT NULL,
	`stripe_id` varchar(191) NOT NULL,
	`stripe_plan` varchar(191) NOT NULL,
	`quantity` int NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `subscription_items_id` PRIMARY KEY(`id`),
	CONSTRAINT `subscription_items_subscription_id_stripe_plan_unique` UNIQUE(`subscription_id`,`stripe_plan`)
);
--> statement-breakpoint
CREATE TABLE `subscriptions` (
	`id` bigint unsigned AUTO_INCREMENT NOT NULL,
	`client_id` bigint unsigned NOT NULL,
	`name` varchar(191) NOT NULL,
	`stripe_id` varchar(191) NOT NULL,
	`stripe_status` varchar(191) NOT NULL,
	`stripe_plan` varchar(191),
	`quantity` int,
	`trial_ends_at` timestamp,
	`ends_at` timestamp,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `subscriptions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `taggables` (
	`tag_id` int unsigned NOT NULL,
	`taggable_type` varchar(191) NOT NULL,
	`taggable_id` bigint unsigned NOT NULL,
	`score` double(8,2) NOT NULL
);
--> statement-breakpoint
CREATE TABLE `tags` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`name` json NOT NULL,
	`slug` json NOT NULL,
	`subtags` text,
	`categories` text,
	`type` varchar(191),
	`order_column` int,
	`is_verified` tinyint NOT NULL DEFAULT 0,
	`created_at` timestamp,
	`updated_at` timestamp,
	`is_popular` tinyint NOT NULL DEFAULT 0,
	CONSTRAINT `tags_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user_chapter` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int unsigned NOT NULL,
	`chapter_id` int unsigned NOT NULL,
	`is_passed` tinyint NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `user_chapter_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user_favourite` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int unsigned NOT NULL,
	`job_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `user_favourite_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user_onesignal` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int unsigned NOT NULL,
	`player_id` varchar(191) NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `user_onesignal_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user_verifications` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`token` varchar(191) NOT NULL,
	`user_id` int unsigned,
	`candidate_id` int unsigned,
	`client_id` int unsigned,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `user_verifications_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user_withdraw` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`user_id` int unsigned NOT NULL,
	`amount` int NOT NULL,
	`status` enum('requested','completed','rejected') NOT NULL,
	`transaction_ref` varchar(191),
	`transaction_date` datetime,
	`note` varchar(191),
	`created_at` timestamp,
	`updated_at` timestamp,
	`bank_name` varchar(191),
	`bank_no` varchar(191),
	CONSTRAINT `user_withdraw_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`rank_id` int,
	`name` varchar(191) NOT NULL,
	`is_admin` tinyint NOT NULL DEFAULT 0,
	`is_sale` tinyint NOT NULL DEFAULT 0,
	`is_consultant` tinyint NOT NULL DEFAULT 0,
	`is_corpcare` tinyint NOT NULL DEFAULT 0,
	`email` varchar(191) NOT NULL,
	`password` varchar(191) NOT NULL,
	`mobile` varchar(191) NOT NULL,
	`ic` varchar(191) DEFAULT '',
	`experience_years` int,
	`occupation` varchar(191),
	`image` varchar(191),
	`bank_no` varchar(191),
	`bank_name` varchar(191),
	`location` varchar(191),
	`state` varchar(191),
	`specialization` varchar(191) DEFAULT '',
	`app_first_login` datetime,
	`last_login` datetime,
	`last_login_ip` varchar(191),
	`registration_ip` varchar(191),
	`is_verified` tinyint NOT NULL DEFAULT 0,
	`is_banned` tinyint NOT NULL DEFAULT 0,
	`is_migrated` tinyint NOT NULL DEFAULT 0,
	`is_passed` tinyint,
	`remember_token` varchar(100),
	`created_at` timestamp,
	`updated_at` timestamp,
	`remark` varchar(191),
	`mailchimp_id` varchar(191),
	`saved_jobs` json NOT NULL,
	`recruiter_group` varchar(191) NOT NULL DEFAULT 'B',
	`company_name` varchar(191),
	`age` int,
	`designation` varchar(191),
	`facebook` varchar(191),
	`twitter` varchar(191),
	`instagram` varchar(191),
	`linkedin` varchar(191),
	`languages` varchar(191),
	`committed_hours_daily` int,
	`sourcing_method` varchar(191),
	`is_technical_recruiter` tinyint DEFAULT 0,
	`is_working_fulltime` tinyint DEFAULT 0,
	`last_seen` timestamp,
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_email_unique` UNIQUE(`email`)
);
--> statement-breakpoint
CREATE TABLE `wanted_job` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`wanted_id` int unsigned NOT NULL,
	`job_id` int unsigned NOT NULL,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `wanted_job_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `wanteds` (
	`id` int unsigned AUTO_INCREMENT NOT NULL,
	`title` varchar(191) NOT NULL,
	`image` varchar(191) NOT NULL,
	`is_active` tinyint NOT NULL DEFAULT 1,
	`priority` int,
	`created_at` timestamp,
	`updated_at` timestamp,
	CONSTRAINT `wanteds_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE INDEX `action_events_actionable_type_actionable_id_index` ON `action_events` (`actionable_type`,`actionable_id`);--> statement-breakpoint
CREATE INDEX `action_events_batch_id_model_type_model_id_index` ON `action_events` (`batch_id`,`model_type`,`model_id`);--> statement-breakpoint
CREATE INDEX `action_events_user_id_index` ON `action_events` (`user_id`);--> statement-breakpoint
CREATE INDEX `candidate_language_candidate_id_foreign` ON `candidate_language` (`candidate_id`);--> statement-breakpoint
CREATE INDEX `candidate_onesignal_player_id_index` ON `candidate_onesignal` (`player_id`);--> statement-breakpoint
CREATE INDEX `candidates_name_index` ON `candidates` (`name`);--> statement-breakpoint
CREATE INDEX `candidates_email_index` ON `candidates` (`email`);--> statement-breakpoint
CREATE INDEX `candidates_user_id_index` ON `candidates` (`user_id`);--> statement-breakpoint
CREATE INDEX `clients_stripe_id_index` ON `clients` (`stripe_id`);--> statement-breakpoint
CREATE INDEX `events_name_index` ON `events` (`name`);--> statement-breakpoint
CREATE INDEX `idx_slug` ON `jobs` (`slug`);--> statement-breakpoint
CREATE INDEX `idx_status` ON `jobs` (`status`);--> statement-breakpoint
CREATE INDEX `idx_package` ON `jobs` (`package`);--> statement-breakpoint
CREATE INDEX `jobs_queue_queue_index` ON `jobs_queue` (`queue`);--> statement-breakpoint
CREATE INDEX `system_files_field_index` ON `migrate_attachments` (`field`);--> statement-breakpoint
CREATE INDEX `system_files_attachment_id_index` ON `migrate_attachments` (`attachment_id`);--> statement-breakpoint
CREATE INDEX `system_files_attachment_type_index` ON `migrate_attachments` (`attachment_type`);--> statement-breakpoint
CREATE INDEX `applications_job_id_index` ON `migrate_candidates` (`job_id`);--> statement-breakpoint
CREATE INDEX `users_activation_code_index` ON `migrate_users` (`activation_code`);--> statement-breakpoint
CREATE INDEX `users_reset_password_code_index` ON `migrate_users` (`reset_password_code`);--> statement-breakpoint
CREATE INDEX `users_login_index` ON `migrate_users` (`username`);--> statement-breakpoint
CREATE INDEX `oauth_access_tokens_user_id_index` ON `oauth_access_tokens` (`user_id`);--> statement-breakpoint
CREATE INDEX `oauth_clients_user_id_index` ON `oauth_clients` (`user_id`);--> statement-breakpoint
CREATE INDEX `oauth_personal_access_clients_client_id_index` ON `oauth_personal_access_clients` (`client_id`);--> statement-breakpoint
CREATE INDEX `oauth_refresh_tokens_access_token_id_index` ON `oauth_refresh_tokens` (`access_token_id`);--> statement-breakpoint
CREATE INDEX `password_resets_email_index` ON `password_resets` (`email`);--> statement-breakpoint
CREATE INDEX `job_id_index` ON `recommendations` (`job_id`);--> statement-breakpoint
CREATE INDEX `type_index` ON `recommendations` (`type`);--> statement-breakpoint
CREATE INDEX `status_index` ON `recommendations` (`status`);--> statement-breakpoint
CREATE INDEX `subscription_items_stripe_id_index` ON `subscription_items` (`stripe_id`);--> statement-breakpoint
CREATE INDEX `subscriptions_client_id_stripe_status_index` ON `subscriptions` (`client_id`,`stripe_status`);--> statement-breakpoint
CREATE INDEX `taggables_taggable_type_taggable_id_index` ON `taggables` (`taggable_type`,`taggable_id`);--> statement-breakpoint
CREATE INDEX `idx_taggable_id` ON `taggables` (`taggable_id`);--> statement-breakpoint
CREATE INDEX `idx_id` ON `tags` (`id`);--> statement-breakpoint
ALTER TABLE `activities` ADD CONSTRAINT `activities_applicant_id_foreign` FOREIGN KEY (`applicant_id`) REFERENCES `applicants`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `activities` ADD CONSTRAINT `activities_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `events`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `activities` ADD CONSTRAINT `activities_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `applicants` ADD CONSTRAINT `applicants_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `applicants` ADD CONSTRAINT `applicants_current_event_id_foreign` FOREIGN KEY (`current_event_id`) REFERENCES `events`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `applicants` ADD CONSTRAINT `applicants_internal_event_id_foreign` FOREIGN KEY (`internal_event_id`) REFERENCES `events`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `applicants` ADD CONSTRAINT `applicants_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `applicants` ADD CONSTRAINT `applicants_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `bill_items` ADD CONSTRAINT `bill_items_bill_id_foreign` FOREIGN KEY (`bill_id`) REFERENCES `bills`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `bill_items` ADD CONSTRAINT `bill_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `bills` ADD CONSTRAINT `bills_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `bills` ADD CONSTRAINT `bills_coupon_id_foreign` FOREIGN KEY (`coupon_id`) REFERENCES `coupons`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_certification` ADD CONSTRAINT `candidate_certification_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_company` ADD CONSTRAINT `candidate_company_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_company` ADD CONSTRAINT `candidate_company_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_education` ADD CONSTRAINT `candidate_education_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_favourite` ADD CONSTRAINT `candidate_favourite_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_favourite` ADD CONSTRAINT `candidate_favourite_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_following` ADD CONSTRAINT `candidate_following_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_following` ADD CONSTRAINT `candidate_following_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_inbox` ADD CONSTRAINT `candidate_inbox_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_logs` ADD CONSTRAINT `candidate_logs_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_logs` ADD CONSTRAINT `candidate_logs_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_onesignal` ADD CONSTRAINT `candidate_onesignal_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_portfolio` ADD CONSTRAINT `candidate_portfolio_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_scout_tags` ADD CONSTRAINT `candidate_scout_tags_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_skill` ADD CONSTRAINT `candidate_skill_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidate_work` ADD CONSTRAINT `candidate_work_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `candidates` ADD CONSTRAINT `candidates_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `challenges` ADD CONSTRAINT `challenges_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `events`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `client_payments` ADD CONSTRAINT `client_payments_client_id_foreign` FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `client_scouts` ADD CONSTRAINT `client_scouts_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `client_scouts` ADD CONSTRAINT `client_scouts_client_id_foreign` FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `client_scouts` ADD CONSTRAINT `client_scouts_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `clients` ADD CONSTRAINT `clients_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `companies` ADD CONSTRAINT `companies_corpcare_id_foreign` FOREIGN KEY (`corpcare_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `companies` ADD CONSTRAINT `companies_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `company_attachment` ADD CONSTRAINT `company_attachment_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `company_benefit` ADD CONSTRAINT `company_benefit_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `company_competitor` ADD CONSTRAINT `company_competitor_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `company_image` ADD CONSTRAINT `company_image_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `company_story` ADD CONSTRAINT `company_story_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_condition` ADD CONSTRAINT `job_condition_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_lists` ADD CONSTRAINT `job_lists_job_custom_id_foreign` FOREIGN KEY (`job_custom_id`) REFERENCES `job_customs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_lists` ADD CONSTRAINT `job_lists_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_requirement` ADD CONSTRAINT `job_requirement_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_revenue` ADD CONSTRAINT `job_revenue_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_screening_questions` ADD CONSTRAINT `job_screening_questions_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_update` ADD CONSTRAINT `job_update_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_visibility` ADD CONSTRAINT `job_visibility_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `job_visibility` ADD CONSTRAINT `job_visibility_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `jobs` ADD CONSTRAINT `jobs_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `jobs` ADD CONSTRAINT `jobs_consultant_id_foreign` FOREIGN KEY (`consultant_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `jobs` ADD CONSTRAINT `jobs_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `jobs` ADD CONSTRAINT `jobs_job_visibility_id_foreign` FOREIGN KEY (`job_visibility_id`) REFERENCES `job_visibility`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `jobs` ADD CONSTRAINT `jobs_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `jobs` ADD CONSTRAINT `jobs_sale_id_foreign` FOREIGN KEY (`sale_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `milestones` ADD CONSTRAINT `milestones_next_rank_id_foreign` FOREIGN KEY (`next_rank_id`) REFERENCES `events`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `milestones` ADD CONSTRAINT `milestones_rank_id_foreign` FOREIGN KEY (`rank_id`) REFERENCES `events`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `quiz_option` ADD CONSTRAINT `quiz_option_quiz_id_foreign` FOREIGN KEY (`quiz_id`) REFERENCES `quizzes`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `quizzes` ADD CONSTRAINT `quizzes_chapter_id_foreign` FOREIGN KEY (`chapter_id`) REFERENCES `chapters`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `screening_answers` ADD CONSTRAINT `screening_answers_applicant_id_foreign` FOREIGN KEY (`applicant_id`) REFERENCES `applicants`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `screening_answers` ADD CONSTRAINT `screening_answers_question_id_foreign` FOREIGN KEY (`question_id`) REFERENCES `questions`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `screening_questions` ADD CONSTRAINT `screening_questions_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `screening_questions` ADD CONSTRAINT `screening_questions_question_id_foreign` FOREIGN KEY (`question_id`) REFERENCES `questions`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `taggables` ADD CONSTRAINT `taggables_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_chapter` ADD CONSTRAINT `user_chapter_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_favourite` ADD CONSTRAINT `user_favourite_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_favourite` ADD CONSTRAINT `user_favourite_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_onesignal` ADD CONSTRAINT `user_onesignal_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_verifications` ADD CONSTRAINT `user_verifications_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_verifications` ADD CONSTRAINT `user_verifications_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `user_withdraw` ADD CONSTRAINT `user_withdraw_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `wanted_job` ADD CONSTRAINT `wanted_job_job_id_foreign` FOREIGN KEY (`job_id`) REFERENCES `jobs`(`id`) ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `wanted_job` ADD CONSTRAINT `wanted_job_wanted_id_foreign` FOREIGN KEY (`wanted_id`) REFERENCES `wanteds`(`id`) ON DELETE cascade ON UPDATE no action;
*/