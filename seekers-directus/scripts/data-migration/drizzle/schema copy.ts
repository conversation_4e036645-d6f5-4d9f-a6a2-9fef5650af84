import { mysqlTable, mysqlSchema, double, AnyMySqlColumn, index, bigint, primaryKey, char, varchar, text, timestamp, foreignKey, int, tinyint, datetime, mysqlEnum, decimal, json, date, unique, smallint, time, longtext, year } from "drizzle-orm/mysql-core"
import { sql } from "drizzle-orm"


export const actionEvents = mysqlTable("action_events", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	batchId: char("batch_id", { length: 36 }).notNull(),
	userId: int("user_id", { unsigned: true }).notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	actionableType: varchar("actionable_type", { length: 191 }).notNull(),
	actionableId: int("actionable_id", { unsigned: true }).notNull(),
	targetType: varchar("target_type", { length: 191 }).notNull(),
	targetId: int("target_id", { unsigned: true }).notNull(),
	modelType: varchar("model_type", { length: 191 }).notNull(),
	modelId: int("model_id", { unsigned: true }),
	fields: text("fields").notNull(),
	status: varchar("status", { length: 25 }).default('running').notNull(),
	exception: text("exception").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	original: text("original"),
	changes: text("changes"),
},
(table) => {
	return {
		actionableTypeActionableIdIdx: index().on(table.actionableType, table.actionableId),
		batchIdModelTypeModelIdIdx: index().on(table.batchId, table.modelType, table.modelId),
		userIdIdx: index().on(table.userId),
		actionEventsId: primaryKey({ columns: [table.id], name: "action_events_id"}),
	}
});

export const activities = mysqlTable("activities", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id", { unsigned: true }).references(() => users.id, { onDelete: "cascade" } ),
	applicantId: int("applicant_id", { unsigned: true }).references(() => applicants.id, { onDelete: "cascade" } ),
	eventId: int("event_id", { unsigned: true }).notNull().references(() => events.id, { onDelete: "cascade" } ),
	amount: int("amount"),
	point: int("point"),
	screeningAnswer: text("screening_answer"),
	cvMasking: varchar("cv_masking", { length: 191 }),
	interviewNo: tinyint("interview_no").default(1),
	interviewCode: varchar("interview_code", { length: 191 }),
	interviewLocation: varchar("interview_location", { length: 191 }),
	interviewDatetime: varchar("interview_datetime", { length: 191 }),
	interviewDateTime: datetime("interview_date_time", { mode: 'string'}),
	interviewPic: varchar("interview_pic", { length: 191 }),
	interviewPicTel: varchar("interview_pic_tel", { length: 191 }),
	interviewRemarks: text("interview_remarks"),
	interviewPicPosition: varchar("interview_pic_position", { length: 191 }),
	interviewAccept: tinyint("interview_accept"),
	interviewReschedule: varchar("interview_reschedule", { length: 191 }),
	feedback: varchar("feedback", { length: 191 }),
	remark: text("remark"),
	remarkClient: varchar("remark_client", { length: 191 }),
	failureReason: varchar("failure_reason", { length: 191 }),
	failureReasonDetail: text("failure_reason_detail"),
	devNote: text("dev_note"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		activitiesId: primaryKey({ columns: [table.id], name: "activities_id"}),
	}
});

export const announcements = mysqlTable("announcements", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 191 }).notNull(),
	description: varchar("description", { length: 191 }),
	imagePath: varchar("image_path", { length: 191 }),
	linkTo: varchar("link_to", { length: 191 }),
	isLinkTargetBlank: tinyint("is_link_target_blank").default(0).notNull(),
	type: varchar("type", { length: 191 }).default('general').notNull(),
	isAlert: tinyint("is_alert").default(0).notNull(),
	status: varchar("status", { length: 191 }).default('draft').notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		announcementsId: primaryKey({ columns: [table.id], name: "announcements_id"}),
	}
});

export const appVersion = mysqlTable("app_version", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	version: varchar("version", { length: 191 }).notNull(),
	device: mysqlEnum("device", ['android','ios']).default('android'),
	remark: text("remark"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		appVersionId: primaryKey({ columns: [table.id], name: "app_version_id"}),
	}
});

export const applicants = mysqlTable("applicants", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id, { onDelete: "cascade" } ),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	userId: int("user_id", { unsigned: true }).default(1).references(() => users.id, { onDelete: "cascade" } ),
	currentEventId: int("current_event_id", { unsigned: true }).notNull().references(() => events.id, { onDelete: "cascade" } ),
	internalEventId: int("internal_event_id", { unsigned: true }).references(() => events.id, { onDelete: "cascade" } ),
	rating: int("rating").notNull(),
	verificationCode: varchar("verification_code", { length: 191 }),
	isVerified: tinyint("is_verified"),
	noticePeriod: varchar("notice_period", { length: 191 }),
	reasonToApply: text("reason_to_apply"),
	reasonToLeave: text("reason_to_leave"),
	screeningAnswer: text("screening_answer"),
	salaryCurrent: int("salary_current"),
	salaryExpected: int("salary_expected"),
	salaryFinal: int("salary_final"),
	autoScreenPass: tinyint("auto_screen_pass"),
	autoScreenReport: text("auto_screen_report"),
	note: text("note"),
	noteClient: varchar("note_client", { length: 191 }),
	report: varchar("report", { length: 191 }),
	urgent: varchar("urgent", { length: 191 }),
	isSuggested: tinyint("is_suggested").default(0).notNull(),
	matchingScore: decimal("matching_score", { precision: 8, scale: 2 }),
	matchingReport: json("matching_report"),
	scoutData: json("scout_data"),
	applyData: json("apply_data"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	jobScreeningAnswers: json("job_screening_answers"),
},
(table) => {
	return {
		applicantsId: primaryKey({ columns: [table.id], name: "applicants_id"}),
	}
});

export const assesments = mysqlTable("assesments", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	question: varchar("question", { length: 191 }),
	o1: varchar("o1", { length: 191 }).notNull(),
	o2: varchar("o2", { length: 191 }).notNull(),
	o3: varchar("o3", { length: 191 }).notNull(),
	o4: varchar("o4", { length: 191 }).notNull(),
	correctAnswer: varchar("correct_answer", { length: 191 }).notNull(),
	image: varchar("image", { length: 191 }),
},
(table) => {
	return {
		assesmentsId: primaryKey({ columns: [table.id], name: "assesments_id"}),
	}
});

export const billItems = mysqlTable("bill_items", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	billId: int("bill_id", { unsigned: true }).notNull().references(() => bills.id, { onDelete: "cascade" } ),
	productId: int("product_id", { unsigned: true }).notNull().references(() => products.id, { onDelete: "cascade" } ),
	price: int("price").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		billItemsId: primaryKey({ columns: [table.id], name: "bill_items_id"}),
	}
});

export const bills = mysqlTable("bills", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id, { onDelete: "cascade" } ),
	couponId: int("coupon_id", { unsigned: true }).references(() => coupons.id, { onDelete: "cascade" } ),
	status: mysqlEnum("status", ['pending','paid','cancel']),
	totalPrice: decimal("total_price", { precision: 8, scale: 2 }).notNull(),
	tax: int("tax").default(6).notNull(),
	netPrice: decimal("net_price", { precision: 8, scale: 2 }).notNull(),
	paymentRef: varchar("payment_ref", { length: 191 }),
	paymentMethod: mysqlEnum("payment_method", ['card','banktransfer','cheque']),
	expiryDate: timestamp("expiry_date", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		billsId: primaryKey({ columns: [table.id], name: "bills_id"}),
	}
});

export const candidateCertification = mysqlTable("candidate_certification", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	name: varchar("name", { length: 191 }),
	description: varchar("description", { length: 191 }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	start: date("start", { mode: 'string' }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	end: date("end", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).onUpdateNow(),
},
(table) => {
	return {
		candidateCertificationId: primaryKey({ columns: [table.id], name: "candidate_certification_id"}),
	}
});

export const candidateCompany = mysqlTable("candidate_company", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateCompanyId: primaryKey({ columns: [table.id], name: "candidate_company_id"}),
	}
});

export const candidateEducation = mysqlTable("candidate_education", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	qualification: varchar("qualification", { length: 191 }),
	title: varchar("title", { length: 191 }),
	institute: varchar("institute", { length: 191 }),
	description: varchar("description", { length: 191 }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	start: date("start", { mode: 'string' }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	end: date("end", { mode: 'string' }),
	result: varchar("result", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).onUpdateNow(),
},
(table) => {
	return {
		candidateEducationId: primaryKey({ columns: [table.id], name: "candidate_education_id"}),
	}
});

export const candidateFavourite = mysqlTable("candidate_favourite", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateFavouriteId: primaryKey({ columns: [table.id], name: "candidate_favourite_id"}),
	}
});

export const candidateFollowing = mysqlTable("candidate_following", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateFollowingId: primaryKey({ columns: [table.id], name: "candidate_following_id"}),
	}
});

export const candidateInbox = mysqlTable("candidate_inbox", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	inboxableType: varchar("inboxable_type", { length: 191 }).notNull(),
	inboxableId: int("inboxable_id", { unsigned: true }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateInboxId: primaryKey({ columns: [table.id], name: "candidate_inbox_id"}),
	}
});

export const candidateLanguage = mysqlTable("candidate_language", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull(),
	language: varchar("language", { length: 191 }).notNull(),
	level: mysqlEnum("level", ['Basic','Intermediate','Advance','Native']).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateIdForeign: index("candidate_language_candidate_id_foreign").on(table.candidateId),
		candidateLanguageId: primaryKey({ columns: [table.id], name: "candidate_language_id"}),
	}
});

export const candidateLogs = mysqlTable("candidate_logs", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id, { onDelete: "cascade" } ),
	event: varchar("event", { length: 191 }).notNull(),
},
(table) => {
	return {
		candidateLogsId: primaryKey({ columns: [table.id], name: "candidate_logs_id"}),
	}
});

export const candidateOnesignal = mysqlTable("candidate_onesignal", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id, { onDelete: "cascade" } ),
	playerId: varchar("player_id", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		playerIdIdx: index().on(table.playerId),
		candidateOnesignalId: primaryKey({ columns: [table.id], name: "candidate_onesignal_id"}),
	}
});

export const candidatePortfolio = mysqlTable("candidate_portfolio", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 191 }).notNull(),
	description: varchar("description", { length: 191 }).notNull(),
	link: varchar("link", { length: 191 }),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	createdAt: timestamp("created_at", { mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).onUpdateNow(),
	file: varchar("file", { length: 191 }),
},
(table) => {
	return {
		candidatePortfolioId: primaryKey({ columns: [table.id], name: "candidate_portfolio_id"}),
	}
});

export const candidateScoutTags = mysqlTable("candidate_scout_tags", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id, { onDelete: "cascade" } ),
	isActiveLooking: tinyint("is_active_looking").default(0).notNull(),
	isShortNotice: tinyint("is_short_notice").default(0).notNull(),
	isReady: tinyint("is_ready").default(0).notNull(),
	isNew: tinyint("is_new").default(0).notNull(),
	isUpdatedCv: tinyint("is_updated_cv").default(0).notNull(),
	isHot: tinyint("is_hot").default(0).notNull(),
	isFreshGraduate: tinyint("is_fresh_graduate").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateScoutTagsId: primaryKey({ columns: [table.id], name: "candidate_scout_tags_id"}),
	}
});

export const candidateSkill = mysqlTable("candidate_skill", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id, { onDelete: "cascade" } ),
	skill: varchar("skill", { length: 191 }).notNull(),
	rate: varchar("rate", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateSkillId: primaryKey({ columns: [table.id], name: "candidate_skill_id"}),
	}
});

export const candidateWork = mysqlTable("candidate_work", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	companyName: varchar("company_name", { length: 191 }).notNull(),
	role: varchar("role", { length: 191 }).notNull(),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	start: date("start", { mode: 'string' }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	end: date("end", { mode: 'string' }),
	responsibilities: text("responsibilities"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		candidateWorkId: primaryKey({ columns: [table.id], name: "candidate_work_id"}),
	}
});

export const candidates = mysqlTable("candidates", {
	aboutMe: text("about_me"),
	birthYear: int("birth_year"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	currentPosition: varchar("current_position", { length: 191 }),
	currentSalary: int("current_salary"),
	cv: varchar("cv", { length: 191 }),
	cvMasking: varchar("cv_masking", { length: 191 }),
	cvRating: int("cv_rating"),
	cvReport: text("cv_report"),
	cvUpdatedAt: timestamp("cv_updated_at", { mode: 'string' }),
	cvVerified: tinyint("cv_verified").default(0).notNull(),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	education: varchar("education", { length: 191 }),
	educationLevel: varchar("education_level", { length: 191 }),
	email: varchar("email", { length: 191 }).notNull(),
	expectedSalary: int("expected_salary"),
	experienceData: json("experience_data"),
	facebook: varchar("facebook", { length: 191 }).default(''),
	facebookId: varchar("facebook_id", { length: 191 }),
	followup: varchar("followup", { length: 191 }),
	gender: varchar("gender", { length: 191 }),
	github: varchar("github", { length: 191 }).default(''),
	googleId: int("google_id"),
	googleRefreshToken: varchar("google_refresh_token", { length: 191 }),
	googleToken: varchar("google_token", { length: 191 }),
	gotVisa: tinyint("got_visa"),
	gotWorkPermit: tinyint("got_work_permit"),
	ic: varchar("ic", { length: 191 }),
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	image: varchar("image", { length: 191 }),
	imageUrl: varchar("image_url", { length: 191 }),
	instagram: varchar("instagram", { length: 191 }).default(''),
	isApp: tinyint("is_app").default(0).notNull(),
	isBlacklisted: tinyint("is_blacklisted"),
	isLocal: tinyint("is_local"),
	isMigrated: tinyint("is_migrated").default(0).notNull(),
	isOffer: tinyint("is_offer").default(0).notNull(),
	isPrimary: tinyint("is_primary").default(0).notNull(),
	isVerified: tinyint("is_verified"),
	language: varchar("language", { length: 191 }),
	languageDetailed: json("language_detailed"),
	languageLevel: varchar("language_level", { length: 191 }),
	lastLogin: timestamp("last_login", { mode: 'string' }),
	lastSeen: timestamp("last_seen", { mode: 'string' }),
	level: mysqlEnum("level", ['entry','junior','senior','manager','senior manager']),
	linkedin: varchar("linkedin", { length: 191 }).default(''),
	linkedinId: varchar("linkedin_id", { length: 191 }),
	location: varchar("location", { length: 191 }),
	mobile: varchar("mobile", { length: 191 }),
	name: varchar("name", { length: 191 }).notNull(),
	nationality: varchar("nationality", { length: 191 }),
	noticePeriod: varchar("notice_period", { length: 191 }),
	offerData: json("offer_data"),
	openToWork: tinyint("open_to_work").default(1).notNull(),
	ownDriverLicense: tinyint("own_driver_license"),
	ownTransport: tinyint("own_transport"),
	password: varchar("password", { length: 191 }),
	portfolio: varchar("portfolio", { length: 191 }),
	race: varchar("race", { length: 191 }),
	remarks: varchar("remarks", { length: 191 }),
	roleId: int("role_id", { unsigned: true }).references(() => roles.id, { onDelete: "cascade" } ),
	roleIds: varchar("role_ids", { length: 191 }),
	skills: json("skills"),
	state: varchar("state", { length: 191 }),
	twitter: varchar("twitter", { length: 191 }).default(''),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	userId: int("user_id"),
	website: varchar("website", { length: 191 }).default(''),
	willingToTravel: tinyint("willing_to_travel"),
},
(table) => {
	return {
		nameIdx: index().on(table.name),
		emailIdx: index().on(table.email),
		userIdIdx: index().on(table.userId),
		candidatesId: primaryKey({ columns: [table.id], name: "candidates_id"}),
	}
});

export const challenges = mysqlTable("challenges", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	eventId: int("event_id", { unsigned: true }).notNull().references(() => events.id),
	type: mysqlEnum("type", ['special','daily','weekly','monthly']).notNull(),
	minCount: int("min_count", { unsigned: true }).notNull(),
	maxCount: int("max_count", { unsigned: true }),
	countingMethod: text("counting_method"),
	status: mysqlEnum("status", ['active','inactive']).default('active').notNull(),
	isVerified: tinyint("is_verified").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		challengesId: primaryKey({ columns: [table.id], name: "challenges_id"}),
	}
});

export const chapters = mysqlTable("chapters", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	heading: text("heading").notNull(),
	subheading: text("subheading").notNull(),
	description: text("description"),
	reward: int("reward"),
	image: text("image"),
	ranking: varchar("ranking", { length: 191 }),
	sortOrder: int("sort_order"),
	status: mysqlEnum("status", ['draft','open','closed']).default('draft').notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		chaptersId: primaryKey({ columns: [table.id], name: "chapters_id"}),
	}
});

export const clientPayments = mysqlTable("client_payments", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	uuid: char("uuid", { length: 36 }).notNull(),
	clientId: int("client_id", { unsigned: true }).notNull().references(() => clients.id),
	planId: int("plan_id", { unsigned: true }),
	amount: decimal("amount", { precision: 8, scale: 2 }),
	paymentMethod: json("payment_method"),
	purchaseData: json("purchase_data"),
	invoiceData: json("invoice_data"),
	receiptData: json("receipt_data"),
	status: varchar("status", { length: 191 }).default('pending').notNull(),
	statusData: json("status_data"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		clientPaymentsId: primaryKey({ columns: [table.id], name: "client_payments_id"}),
	}
});

export const clientScouts = mysqlTable("client_scouts", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	clientId: int("client_id", { unsigned: true }).notNull().references(() => clients.id),
	candidateId: int("candidate_id", { unsigned: true }).notNull().references(() => candidates.id),
	jobId: int("job_id", { unsigned: true }).references(() => jobs.id),
	code: varchar("code", { length: 191 }).notNull(),
	message: text("message"),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	deadline: date("deadline", { mode: 'string' }),
	status: varchar("status", { length: 191 }),
	extraData: json("extra_data"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		clientScoutsId: primaryKey({ columns: [table.id], name: "client_scouts_id"}),
	}
});

export const clientSubscriptions = mysqlTable("client_subscriptions", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	clientId: int("client_id").notNull(),
	subscriptionPlanId: int("subscription_plan_id").notNull(),
	uuid: varchar("uuid", { length: 191 }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	oldExpiryDate: date("old_expiry_date", { mode: 'string' }),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	newExpiryDate: date("new_expiry_date", { mode: 'string' }),
	isExpired: tinyint("is_expired").default(0).notNull(),
	note: varchar("note", { length: 191 }),
	jsonData: json("json_data"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		clientSubscriptionsId: primaryKey({ columns: [table.id], name: "client_subscriptions_id"}),
	}
});

export const clients = mysqlTable("clients", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id, { onDelete: "cascade" } ),
	name: varchar("name", { length: 191 }),
	email: varchar("email", { length: 191 }).notNull(),
	password: varchar("password", { length: 191 }).notNull(),
	tel: varchar("tel", { length: 191 }),
	subscriptionPlanId: int("subscription_plan_id").default(1).notNull(),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	expiryDate: date("expiry_date", { mode: 'string' }),
	jobQuota: int("job_quota").default(0).notNull(),
	scoutCredit: int("scout_credit").default(0).notNull(),
	status: mysqlEnum("status", ['pending','active','inactive']).default('active').notNull(),
	isVerified: tinyint("is_verified").default(0).notNull(),
	isAdmin: tinyint("is_admin").default(0).notNull(),
	lastLogin: timestamp("last_login", { mode: 'string' }),
	stripeId: varchar("stripe_id", { length: 191 }),
	cardBrand: varchar("card_brand", { length: 191 }),
	cardLastFour: varchar("card_last_four", { length: 4 }),
	trialEndsAt: timestamp("trial_ends_at", { mode: 'string' }),
	rememberToken: varchar("remember_token", { length: 100 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		stripeIdIdx: index().on(table.stripeId),
		clientsId: primaryKey({ columns: [table.id], name: "clients_id"}),
	}
});

export const companies = mysqlTable("companies", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	slug: varchar("slug", { length: 191 }),
	regNo: varchar("reg_no", { length: 191 }),
	building: varchar("building", { length: 191 }),
	address: varchar("address", { length: 191 }),
	postalCode: int("postal_code"),
	city: varchar("city", { length: 191 }),
	state: varchar("state", { length: 191 }),
	location: varchar("location", { length: 191 }),
	long: varchar("long", { length: 191 }),
	lat: varchar("lat", { length: 191 }),
	industryId: int("industry_id", { unsigned: true }).notNull().references(() => industries.id, { onDelete: "cascade" } ),
	size: varchar("size", { length: 191 }),
	overview: text("overview"),
	highlight: varchar("highlight", { length: 191 }),
	logo: varchar("logo", { length: 191 }),
	isGeneratedLogo: tinyint("is_generated_logo").default(0).notNull(),
	backgroundImage: varchar("background_image", { length: 191 }),
	picEmail: varchar("pic_email", { length: 191 }),
	picPhone: varchar("pic_phone", { length: 191 }),
	allowances: varchar("allowances", { length: 191 }),
	benefits: varchar("benefits", { length: 191 }),
	annualLeave: varchar("annual_leave", { length: 191 }),
	bonus: varchar("bonus", { length: 191 }),
	cultureText: text("culture_text"),
	cultureVideoUrl: varchar("culture_video_url", { length: 191 }),
	culturePhoto: varchar("culture_photo", { length: 191 }),
	registrationData: json("registration_data"),
	tncAgree: tinyint("tnc_agree").default(0).notNull(),
	isHighlight: tinyint("is_highlight").default(0).notNull(),
	isTopEmployer: tinyint("is_top_employer").default(0).notNull(),
	isMain: tinyint("is_main").notNull(),
	scoringData: json("scoring_data"),
	score: decimal("score", { precision: 8, scale: 2 }).default('0.00').notNull(),
	isSrsPremium: tinyint("is_srs_premium").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	url: varchar("url", { length: 191 }),
	videoUrl: varchar("video_url", { length: 191 }),
	urlPath: varchar("url_path", { length: 191 }),
	corpcareId: int("corpcare_id", { unsigned: true }).references(() => users.id),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	remarkDev: varchar("remark_dev", { length: 191 }),
},
(table) => {
	return {
		companiesId: primaryKey({ columns: [table.id], name: "companies_id"}),
		companiesSlugUnique: unique("companies_slug_unique").on(table.slug),
	}
});

export const companyAttachment = mysqlTable("company_attachment", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id, { onDelete: "cascade" } ),
	attachment: varchar("attachment", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		companyAttachmentId: primaryKey({ columns: [table.id], name: "company_attachment_id"}),
	}
});

export const companyBenefit = mysqlTable("company_benefit", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id),
	heading: varchar("heading", { length: 191 }).notNull(),
	description: varchar("description", { length: 191 }).notNull(),
	icon: varchar("icon", { length: 191 }),
	textColor: varchar("text_color", { length: 191 }).default('#000000'),
	backgroundColor: varchar("background_color", { length: 191 }).default('#F1F5F8'),
	orderNo: smallint("order_no"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		companyBenefitId: primaryKey({ columns: [table.id], name: "company_benefit_id"}),
	}
});

export const companyCompetitor = mysqlTable("company_competitor", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id, { onDelete: "cascade" } ),
	name: varchar("name", { length: 191 }).notNull(),
	link: varchar("link", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		companyCompetitorId: primaryKey({ columns: [table.id], name: "company_competitor_id"}),
	}
});

export const companyImage = mysqlTable("company_image", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id, { onDelete: "cascade" } ),
	type: mysqlEnum("type", ['gallery','product']),
	filename: varchar("filename", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	caption: varchar("caption", { length: 191 }),
	priority: int("priority"),
},
(table) => {
	return {
		companyImageId: primaryKey({ columns: [table.id], name: "company_image_id"}),
	}
});

export const companyInterviewSettingJobs = mysqlTable("company_interview_setting_jobs", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	interviewSettingId: int("interview_setting_id").notNull(),
	jobId: int("job_id").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		companyInterviewSettingJobsId: primaryKey({ columns: [table.id], name: "company_interview_setting_jobs_id"}),
	}
});

export const companyInterviewSettings = mysqlTable("company_interview_settings", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id").notNull(),
	isGeneral: tinyint("is_general").notNull(),
	location: varchar("location", { length: 191 }).notNull(),
	picName: varchar("pic_name", { length: 191 }).notNull(),
	picPhone: varchar("pic_phone", { length: 191 }).notNull(),
	picPosition: varchar("pic_position", { length: 191 }).notNull(),
	defaultRemark: text("default_remark"),
	status: tinyint("status").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		companyInterviewSettingsId: primaryKey({ columns: [table.id], name: "company_interview_settings_id"}),
	}
});

export const companyStory = mysqlTable("company_story", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id),
	heading: varchar("heading", { length: 191 }).notNull(),
	content: text("content").notNull(),
	type: mysqlEnum("type", ['text','video','image']).default('text').notNull(),
	backgroundColor: varchar("background_color", { length: 191 }).default('#F1F5F8'),
	orderNo: smallint("order_no"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		companyStoryId: primaryKey({ columns: [table.id], name: "company_story_id"}),
	}
});

export const companyTimeslots = mysqlTable("company_timeslots", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	interviewSettingId: int("interview_setting_id").notNull(),
	interviewId: int("interview_id"),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	date: date("date", { mode: 'string' }).notNull(),
	time: time("time").notNull(),
	availability: tinyint("availability").default(1).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		companyTimeslotsId: primaryKey({ columns: [table.id], name: "company_timeslots_id"}),
	}
});

export const coupons = mysqlTable("coupons", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	clientId: int("client_id"),
	code: varchar("code", { length: 191 }).notNull(),
	fixedDiscount: int("fixed_discount"),
	percentageDiscount: int("percentage_discount"),
	status: mysqlEnum("status", ['draft','active','disable','expired','used']),
	expiryDate: timestamp("expiry_date", { mode: 'string' }),
	extraData: json("extra_data"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		couponsId: primaryKey({ columns: [table.id], name: "coupons_id"}),
	}
});

export const enquiries = mysqlTable("enquiries", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	subject: varchar("subject", { length: 191 }).notNull(),
	category: mysqlEnum("category", ['Feedback','Bug','General','Employer']).default('General'),
	content: text("content").notNull(),
	senderName: varchar("sender_name", { length: 191 }).notNull(),
	senderEmail: varchar("sender_email", { length: 191 }).notNull(),
	company: varchar("company", { length: 191 }),
	phone: varchar("phone", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		enquiriesId: primaryKey({ columns: [table.id], name: "enquiries_id"}),
	}
});

export const events = mysqlTable("events", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	altName: varchar("alt_name", { length: 191 }),
	code: varchar("code", { length: 191 }).notNull(),
	amountDefault: varchar("amount_default", { length: 191 }).notNull(),
	pointDefault: varchar("point_default", { length: 191 }).notNull(),
	category: varchar("category", { length: 191 }).notNull(),
	color: varchar("color", { length: 191 }),
	icon: varchar("icon", { length: 191 }),
	priority: int("priority"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		nameIdx: index().on(table.name),
		eventsId: primaryKey({ columns: [table.id], name: "events_id"}),
		eventsCodeUnique: unique("events_code_unique").on(table.code),
	}
});

export const failedJobs = mysqlTable("failed_jobs", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	connection: text("connection").notNull(),
	queue: text("queue").notNull(),
	payload: longtext("payload").notNull(),
	exception: longtext("exception").notNull(),
	failedAt: timestamp("failed_at", { mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
},
(table) => {
	return {
		failedJobsId: primaryKey({ columns: [table.id], name: "failed_jobs_id"}),
	}
});

export const faqs = mysqlTable("faqs", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	category: varchar("category", { length: 191 }).notNull(),
	topic: varchar("topic", { length: 191 }).notNull(),
	question: varchar("question", { length: 191 }).notNull(),
	answer: text("answer").notNull(),
	status: mysqlEnum("status", ['draft','open']).notNull(),
	sortOrder: int("sort_order"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		faqsId: primaryKey({ columns: [table.id], name: "faqs_id"}),
	}
});

export const getresponseList = mysqlTable("getresponse_list", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	campaignId: varchar("campaign_id", { length: 191 }).notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	source: varchar("source", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		getresponseListId: primaryKey({ columns: [table.id], name: "getresponse_list_id"}),
	}
});

export const industries = mysqlTable("industries", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	parentId: int("parent_id", { unsigned: true }),
	name: varchar("name", { length: 191 }).notNull(),
	color: varchar("color", { length: 191 }),
	icon: varchar("icon", { length: 191 }),
	imagePath: varchar("image_path", { length: 191 }),
	slug: varchar("slug", { length: 191 }),
	priority: int("priority", { unsigned: true }),
	sortOrder: int("sort_order", { unsigned: true }),
	isParent: tinyint("is_parent").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	seoTitle: varchar("seo_title", { length: 191 }),
	seoDescription: text("seo_description"),
	seoImage: varchar("seo_image", { length: 191 }),
},
(table) => {
	return {
		industriesId: primaryKey({ columns: [table.id], name: "industries_id"}),
		slug: unique("slug").on(table.slug),
		industriesSlugUnique: unique("industries_slug_unique").on(table.slug),
	}
});

export const jobCondition = mysqlTable("job_condition", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	workingDays: varchar("working_days", { length: 191 }),
	workingHour: varchar("working_hour", { length: 191 }),
	isShiftWork: tinyint("is_shift_work").default(0).notNull(),
	allowances: varchar("allowances", { length: 191 }),
	bonus: varchar("bonus", { length: 191 }),
	annualLeave: varchar("annual_leave", { length: 191 }),
	holiday: varchar("holiday", { length: 191 }),
	probation: varchar("probation", { length: 191 }),
	benefits: varchar("benefits", { length: 191 }),
	type: varchar("type", { length: 191 }),
	remarks: varchar("remarks", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		jobConditionId: primaryKey({ columns: [table.id], name: "job_condition_id"}),
	}
});

export const jobCustoms = mysqlTable("job_customs", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 191 }).notNull(),
	slug: varchar("slug", { length: 191 }).notNull(),
	status: mysqlEnum("status", ['show','hide']).default('show').notNull(),
	remark: varchar("remark", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		jobCustomsId: primaryKey({ columns: [table.id], name: "job_customs_id"}),
	}
});

export const jobLists = mysqlTable("job_lists", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	jobCustomId: int("job_custom_id", { unsigned: true }).notNull().references(() => jobCustoms.id, { onDelete: "cascade" } ),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		jobListsId: primaryKey({ columns: [table.id], name: "job_lists_id"}),
	}
});

export const jobRequirement = mysqlTable("job_requirement", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	ageMin: int("age_min").default(18).notNull(),
	ageMax: int("age_max"),
	gender: varchar("gender", { length: 191 }),
	race: varchar("race", { length: 191 }),
	language: varchar("language", { length: 191 }),
	isLocalOnly: tinyint("is_local_only"),
	ownTransport: tinyint("own_transport"),
	ownDriverLicense: tinyint("own_driver_license"),
	willingToTravel: tinyint("willing_to_travel"),
	gotWorkPermit: tinyint("got_work_permit"),
	gotVisa: tinyint("got_visa"),
	gotPortfolio: tinyint("got_portfolio"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		jobRequirementId: primaryKey({ columns: [table.id], name: "job_requirement_id"}),
	}
});

export const jobRevenue = mysqlTable("job_revenue", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	amount: int("amount").notNull(),
	status: mysqlEnum("status", ['expected','actual']).notNull(),
	date: datetime("date", { mode: 'string'}).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		jobRevenueId: primaryKey({ columns: [table.id], name: "job_revenue_id"}),
	}
});

export const jobScreeningQuestions = mysqlTable("job_screening_questions", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	type: varchar("type", { length: 191 }).notNull(),
	question: varchar("question", { length: 191 }),
},
(table) => {
	return {
		jobScreeningQuestionsId: primaryKey({ columns: [table.id], name: "job_screening_questions_id"}),
	}
});

export const jobUpdate = mysqlTable("job_update", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	dateAt: date("date_at", { mode: 'string' }).notNull(),
	note: varchar("note", { length: 191 }).notNull(),
	tag: varchar("tag", { length: 191 }),
	tagColor: varchar("tag_color", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		jobUpdateId: primaryKey({ columns: [table.id], name: "job_update_id"}),
	}
});

export const jobVisibility = mysqlTable("job_visibility", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id", { unsigned: true }).notNull().references(() => users.id, { onDelete: "cascade" } ),
	jobId: int("job_id", { unsigned: true }).notNull().references((): AnyMySqlColumn => jobs.id, { onDelete: "cascade" } ),
	show: tinyint("show").notNull(),
	hide: tinyint("hide").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		jobVisibilityId: primaryKey({ columns: [table.id], name: "job_visibility_id"}),
	}
});

export const jobs = mysqlTable("jobs", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	package: mysqlEnum("package", ['Premium','Pay-Per-Post','Free-Posting']).default('Premium'),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	expiryDate: date("expiry_date", { mode: 'string' }),
	status: mysqlEnum("status", ['open','temporarily_closed','closed','draft']),
	slug: varchar("slug", { length: 191 }).default('').notNull(),
	urlPathRole: varchar("url_path_role", { length: 191 }),
	isDifferentLocation: tinyint("is_different_location").default(0),
	city: varchar("city", { length: 191 }),
	title: varchar("title", { length: 191 }).default('').notNull(),
	description: text("description").notNull(),
	closeReason: varchar("close_reason", { length: 191 }),
	tempCloseReason: varchar("temp_close_reason", { length: 191 }),
	industryId: int("industry_id", { unsigned: true }).references(() => industries.id),
	companyId: int("company_id", { unsigned: true }).notNull().references(() => companies.id, { onDelete: "cascade" } ),
	companyPicName: varchar("company_pic_name", { length: 191 }),
	companyPicEmail: varchar("company_pic_email", { length: 191 }),
	companyPicPhone: varchar("company_pic_phone", { length: 191 }),
	saleId: int("sale_id", { unsigned: true }).references(() => users.id),
	consultantId: int("consultant_id", { unsigned: true }).references(() => users.id),
	isHighlight: tinyint("is_highlight").default(0).notNull(),
	roleId: int("role_id", { unsigned: true }).notNull().references(() => roles.id, { onDelete: "cascade" } ),
	level: varchar("level", { length: 191 }).notNull(),
	location: varchar("location", { length: 191 }),
	locationRemark: varchar("location_remark", { length: 191 }),
	address: varchar("address", { length: 191 }),
	building: varchar("building", { length: 191 }),
	state: varchar("state", { length: 191 }),
	postalCode: int("postal_code"),
	long: varchar("long", { length: 191 }),
	lat: varchar("lat", { length: 191 }),
	responsibility: text("responsibility").notNull(),
	interviewMethod: varchar("interview_method", { length: 191 }),
	reqMustHave: text("req_must_have"),
	reqOthers: text("req_others"),
	experienceMinYears: varchar("experience_min_years", { length: 191 }),
	experienceField: varchar("experience_field", { length: 191 }),
	screeningQuestion: text("screening_question"),
	image: varchar("image", { length: 191 }),
	isProcessing: tinyint("is_processing").default(0).notNull(),
	remark: text("remark"),
	corporateRemark: text("corporate_remark"),
	feature: varchar("feature", { length: 191 }),
	acceptFresh: tinyint("accept_fresh").default(0).notNull(),
	acceptForeigner: tinyint("accept_foreigner").default(0).notNull(),
	salaryMin: int("salary_min").notNull(),
	salaryMax: int("salary_max").notNull(),
	rewardMin: int("reward_min"),
	rewardMax: int("reward_max"),
	sentToClientReward: int("sent_to_client_reward"),
	sentToClientLimit: int("sent_to_client_limit"),
	shortlistReward: int("shortlist_reward"),
	shortlistLimit: int("shortlist_limit").notNull(),
	isRewardBoost: tinyint("is_reward_boost").default(0).notNull(),
	boostStatus: varchar("boost_status", { length: 191 }),
	amendmentNote: text("amendment_note"),
	needVerification: tinyint("need_verification").default(0).notNull(),
	videoUrl: varchar("video_url", { length: 191 }),
	minMatchingRate: int("min_matching_rate", { unsigned: true }).default(20).notNull(),
	indeedData: json("indeed_data"),
	scoringData: json("scoring_data"),
	score: decimal("score", { precision: 12, scale: 8 }),
	extraData: json("extra_data"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	revenueModel: varchar("revenue_model", { length: 191 }),
	urlPathIndustry: varchar("url_path_industry", { length: 191 }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	hideFrom: json("hide_from"),
	jobVisibilityId: int("job_visibility_id", { unsigned: true }).references((): AnyMySqlColumn => jobVisibility.id, { onDelete: "cascade" } ),
	hideFromAllRecruiter: tinyint("hide_from_all_recruiter").default(0),
	isStaffPick: tinyint("is_staff_pick").default(0),
	showToRecruiterGroup: varchar("show_to_recruiter_group", { length: 191 }).default('A,B'),
	publishToRecruiters: tinyint("publish_to_recruiters").default(0),
	isRemote: tinyint("is_remote").default(0).notNull(),
},
(table) => {
	return {
		idxSlug: index("idx_slug").on(table.slug),
		idxStatus: index("idx_status").on(table.status),
		idxPackage: index("idx_package").on(table.package),
		jobsId: primaryKey({ columns: [table.id], name: "jobs_id"}),
	}
});

export const jobsQueue = mysqlTable("jobs_queue", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	queue: varchar("queue", { length: 191 }).notNull(),
	payload: longtext("payload").notNull(),
	attempts: tinyint("attempts").notNull(),
	reservedAt: int("reserved_at", { unsigned: true }),
	availableAt: int("available_at", { unsigned: true }).notNull(),
	createdAt: int("created_at", { unsigned: true }).notNull(),
},
(table) => {
	return {
		queueIdx: index().on(table.queue),
		jobsQueueId: primaryKey({ columns: [table.id], name: "jobs_queue_id"}),
	}
});

export const masterLookups = mysqlTable("master_lookups", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	value: varchar("value", { length: 191 }).notNull(),
	type: varchar("type", { length: 191 }).notNull(),
	jsonData: json("json_data").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		masterLookupsId: primaryKey({ columns: [table.id], name: "master_lookups_id"}),
	}
});

export const migrateAttachments = mysqlTable("migrate_attachments", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	diskName: varchar("disk_name", { length: 255 }).notNull(),
	fileName: varchar("file_name", { length: 255 }).notNull(),
	fileSize: int("file_size").notNull(),
	contentType: varchar("content_type", { length: 255 }).notNull(),
	title: varchar("title", { length: 255 }),
	description: text("description"),
	field: varchar("field", { length: 255 }),
	attachmentId: varchar("attachment_id", { length: 255 }),
	attachmentType: varchar("attachment_type", { length: 255 }),
	isPublic: tinyint("is_public").default(1).notNull(),
	sortOrder: int("sort_order"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		systemFilesFieldIdx: index("system_files_field_index").on(table.field),
		systemFilesAttachmentIdIdx: index("system_files_attachment_id_index").on(table.attachmentId),
		systemFilesAttachmentTypeIdx: index("system_files_attachment_type_index").on(table.attachmentType),
		migrateAttachmentsId: primaryKey({ columns: [table.id], name: "migrate_attachments_id"}),
	}
});

export const migrateCandidates = mysqlTable("migrate_candidates", {
	id: int("id").autoincrement().notNull(),
	jobId: int("job_id").notNull(),
	referrerId: int("referrer_id").notNull(),
	name: varchar("name", { length: 255 }).notNull(),
	email: varchar("email", { length: 100 }).notNull(),
	mobile: varchar("mobile", { length: 20 }).notNull(),
	gender: mysqlEnum("gender", ['male','female']),
	spokenLanguage: text("spoken_language"),
	citizenship: mysqlEnum("citizenship", ['malaysian','with permit','none']),
	currentIndustry: varchar("current_industry", { length: 255 }),
	currentPosition: varchar("current_position", { length: 255 }),
	currentSalary: decimal("current_salary", { precision: 10, scale: 2 }),
	expectedSalary: decimal("expected_salary", { precision: 10, scale: 2 }),
	offeredSalary: decimal("offered_salary", { precision: 10, scale: 2 }).default('0.00').notNull(),
	noticePeriod: varchar("notice_period", { length: 255 }),
	birthYear: year("birth_year"),
	linkedin: varchar("linkedin", { length: 255 }),
	reward: decimal("reward", { precision: 10, scale: 2 }),
	paidAmount: decimal("paid_amount", { precision: 10, scale: 2 }).default('0.00'),
	sentStatus: tinyint("sent_status"),
	// you can use { mode: 'date' }, if you want to have Date as type for this column
	interviewDate: date("interview_date", { mode: 'string' }),
	interviewTime: time("interview_time"),
	interviewVenue: text("interview_venue"),
	interviewerName: varchar("interviewer_name", { length: 255 }),
	interviewStatus: tinyint("interview_status").default(0),
	commencementDate: datetime("commencement_date", { mode: 'string'}),
	commencement30Date: datetime("commencement_30_date", { mode: 'string'}),
	remark: text("remark"),
	offerStatus: tinyint("offer_status"),
	note: text("note"),
	screeningQuestion: text("screening_question"),
	status: tinyint("status").notNull(),
	blacklist: tinyint("blacklist"),
	ipAddress: varchar("ip_address", { length: 20 }).notNull(),
	created: datetime("created", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedBy: int("updated_by").default(0),
	updated: datetime("updated", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`).notNull(),
},
(table) => {
	return {
		applicationsJobIdIdx: index("applications_job_id_index").on(table.jobId),
		migrateCandidatesId: primaryKey({ columns: [table.id], name: "migrate_candidates_id"}),
	}
});

export const migrateUsers = mysqlTable("migrate_users", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: varchar("name", { length: 255 }),
	email: varchar("email", { length: 255 }).notNull(),
	contactNo: varchar("contact_no", { length: 16 }).notNull(),
	currentSpecialization: varchar("current_specialization", { length: 255 }),
	recruitingExperience: int("recruiting_experience"),
	password: varchar("password", { length: 255 }).notNull(),
	activationCode: varchar("activation_code", { length: 255 }),
	persistCode: varchar("persist_code", { length: 255 }),
	resetPasswordCode: varchar("reset_password_code", { length: 255 }),
	permissions: text("permissions"),
	isActivated: tinyint("is_activated").default(0).notNull(),
	activatedAt: datetime("activated_at", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`),
	lastLogin: datetime("last_login", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`),
	createdAt: datetime("created_at", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`),
	updatedAt: datetime("updated_at", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`),
	username: varchar("username", { length: 255 }),
	surname: varchar("surname", { length: 255 }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
	lastSeen: datetime("last_seen", { mode: 'string'}).default(sql`CURRENT_TIMESTAMP`),
	sessionId: varchar("session_id", { length: 255 }),
	affiliateId: int("affiliate_id"),
	isAffiliate: tinyint("is_affiliate"),
	subscribe: tinyint("subscribe").default(1).notNull(),
},
(table) => {
	return {
		usersActivationCodeIdx: index("users_activation_code_index").on(table.activationCode),
		usersResetPasswordCodeIdx: index("users_reset_password_code_index").on(table.resetPasswordCode),
		usersLoginIdx: index("users_login_index").on(table.username),
		migrateUsersId: primaryKey({ columns: [table.id], name: "migrate_users_id"}),
		usersEmailUnique: unique("users_email_unique").on(table.email),
		usersLoginUnique: unique("users_login_unique").on(table.username),
	}
});

export const migrations = mysqlTable("migrations", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	migration: varchar("migration", { length: 191 }).notNull(),
	batch: int("batch").notNull(),
},
(table) => {
	return {
		migrationsId: primaryKey({ columns: [table.id], name: "migrations_id"}),
	}
});

export const milestoneEvents = mysqlTable("milestone_events", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	milestoneId: int("milestone_id").notNull(),
	eventId: int("event_id").notNull(),
	countMethod: varchar("count_method", { length: 191 }).default('count_activity').notNull(),
	minCount: int("min_count").default(1).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		milestoneEventsId: primaryKey({ columns: [table.id], name: "milestone_events_id"}),
	}
});

export const milestones = mysqlTable("milestones", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	rankId: int("rank_id", { unsigned: true }).notNull().references(() => events.id),
	nextRankId: int("next_rank_id", { unsigned: true }).notNull().references(() => events.id),
	name: varchar("name", { length: 191 }).notNull(),
	description: text("description"),
	completionReward: int("completion_reward").notNull(),
	order: int("order"),
	logo: varchar("logo", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		milestonesId: primaryKey({ columns: [table.id], name: "milestones_id"}),
	}
});

export const ncEvolutions = mysqlTable("nc_evolutions", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 255 }).notNull(),
	titleDown: varchar("titleDown", { length: 255 }),
	description: varchar("description", { length: 255 }),
	batch: int("batch"),
	checksum: varchar("checksum", { length: 255 }),
	status: int("status"),
	created: datetime("created", { mode: 'string'}),
	createdAt: datetime("created_at", { mode: 'string'}),
	updatedAt: datetime("updated_at", { mode: 'string'}),
},
(table) => {
	return {
		ncEvolutionsId: primaryKey({ columns: [table.id], name: "nc_evolutions_id"}),
	}
});

export const news = mysqlTable("news", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 191 }).notNull(),
	slug: varchar("slug", { length: 191 }).notNull(),
	content: text("content"),
	upload: varchar("upload", { length: 191 }),
	image: varchar("image", { length: 191 }),
	type: mysqlEnum("type", ['app','desktop','both']).default('both').notNull(),
	isFeatured: tinyint("is_featured"),
	category: mysqlEnum("category", ['promo','general','update']).default('general').notNull(),
	active: tinyint("active").default(1).notNull(),
	pushNotification: tinyint("push_notification").default(0).notNull(),
	segment: varchar("segment", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	module: mysqlEnum("module", ['jobfinder','recruiter','employer']).default('recruiter'),
},
(table) => {
	return {
		newsId: primaryKey({ columns: [table.id], name: "news_id"}),
	}
});

export const notifications = mysqlTable("notifications", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	heading: varchar("heading", { length: 191 }).notNull(),
	subheading: varchar("subheading", { length: 191 }).notNull(),
	content: text("content").notNull(),
	type: mysqlEnum("type", ['news','campaign','update','activity']).default('news').notNull(),
	module: mysqlEnum("module", ['recruiter','jobfinder','employer']).default('recruiter'),
	url: varchar("url", { length: 191 }),
	image: varchar("image", { length: 191 }),
	segments: varchar("segments", { length: 191 }),
	playerIds: varchar("player_ids", { length: 191 }),
	createdByActivity: tinyint("created_by_activity").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		notificationsId: primaryKey({ columns: [table.id], name: "notifications_id"}),
	}
});

export const oauthAccessTokens = mysqlTable("oauth_access_tokens", {
	id: varchar("id", { length: 100 }).notNull(),
	userId: int("user_id"),
	clientId: int("client_id").notNull(),
	name: varchar("name", { length: 191 }),
	scopes: text("scopes"),
	revoked: tinyint("revoked").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	expiresAt: datetime("expires_at", { mode: 'string'}),
},
(table) => {
	return {
		userIdIdx: index().on(table.userId),
		oauthAccessTokensId: primaryKey({ columns: [table.id], name: "oauth_access_tokens_id"}),
	}
});

export const oauthAuthCodes = mysqlTable("oauth_auth_codes", {
	id: varchar("id", { length: 100 }).notNull(),
	userId: int("user_id").notNull(),
	clientId: int("client_id").notNull(),
	scopes: text("scopes"),
	revoked: tinyint("revoked").notNull(),
	expiresAt: datetime("expires_at", { mode: 'string'}),
},
(table) => {
	return {
		oauthAuthCodesId: primaryKey({ columns: [table.id], name: "oauth_auth_codes_id"}),
	}
});

export const oauthClients = mysqlTable("oauth_clients", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id"),
	name: varchar("name", { length: 191 }).notNull(),
	secret: varchar("secret", { length: 100 }).notNull(),
	redirect: text("redirect").notNull(),
	personalAccessClient: tinyint("personal_access_client").notNull(),
	passwordClient: tinyint("password_client").notNull(),
	revoked: tinyint("revoked").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		userIdIdx: index().on(table.userId),
		oauthClientsId: primaryKey({ columns: [table.id], name: "oauth_clients_id"}),
	}
});

export const oauthPersonalAccessClients = mysqlTable("oauth_personal_access_clients", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	clientId: int("client_id").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		clientIdIdx: index().on(table.clientId),
		oauthPersonalAccessClientsId: primaryKey({ columns: [table.id], name: "oauth_personal_access_clients_id"}),
	}
});

export const oauthRefreshTokens = mysqlTable("oauth_refresh_tokens", {
	id: varchar("id", { length: 100 }).notNull(),
	accessTokenId: varchar("access_token_id", { length: 100 }).notNull(),
	revoked: tinyint("revoked").notNull(),
	expiresAt: datetime("expires_at", { mode: 'string'}),
},
(table) => {
	return {
		accessTokenIdIdx: index().on(table.accessTokenId),
		oauthRefreshTokensId: primaryKey({ columns: [table.id], name: "oauth_refresh_tokens_id"}),
	}
});

export const passwordResets = mysqlTable("password_resets", {
	email: varchar("email", { length: 191 }).notNull(),
	token: varchar("token", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
},
(table) => {
	return {
		emailIdx: index().on(table.email),
	}
});

export const popups = mysqlTable("popups", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	image: varchar("image", { length: 191 }).notNull(),
	mode: mysqlEnum("mode", ['internal','external']).notNull(),
	pageType: mysqlEnum("page_type", ['job','inbox']),
	pageId: int("page_id"),
	externalUrl: varchar("external_url", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		popupsId: primaryKey({ columns: [table.id], name: "popups_id"}),
	}
});

export const products = mysqlTable("products", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	code: varchar("code", { length: 191 }).notNull(),
	description: varchar("description", { length: 191 }),
	status: mysqlEnum("status", ['draft','active','disable']).notNull(),
	type: mysqlEnum("type", ['package','addon']).notNull(),
	remark: varchar("remark", { length: 191 }),
	price: int("price").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		productsId: primaryKey({ columns: [table.id], name: "products_id"}),
	}
});

export const questions = mysqlTable("questions", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	industryId: int("industry_id", { unsigned: true }),
	text: varchar("text", { length: 191 }).notNull(),
	tags: varchar("tags", { length: 191 }),
	isEndorsement: tinyint("is_endorsement").default(0).notNull(),
	priority: int("priority").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		questionsId: primaryKey({ columns: [table.id], name: "questions_id"}),
	}
});

export const quizOption = mysqlTable("quiz_option", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	quizId: bigint("quiz_id", { mode: "number", unsigned: true }).notNull().references(() => quizzes.id),
	optionNo: int("option_no").notNull(),
	text: varchar("text", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		quizOptionId: primaryKey({ columns: [table.id], name: "quiz_option_id"}),
	}
});

export const quizzes = mysqlTable("quizzes", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	chapterId: bigint("chapter_id", { mode: "number", unsigned: true }).notNull().references(() => chapters.id),
	question: varchar("question", { length: 191 }).notNull(),
	answer: int("answer").notNull(),
	image: varchar("image", { length: 191 }),
	description: text("description"),
	sortOrder: int("sort_order"),
	status: mysqlEnum("status", ['draft','open','closed']).default('draft').notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		quizzesId: primaryKey({ columns: [table.id], name: "quizzes_id"}),
	}
});

export const recommendations = mysqlTable("recommendations", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id").notNull(),
	type: varchar("type", { length: 191 }).default('jd').notNull(),
	data: json("data"),
	status: varchar("status", { length: 191 }).default('show').notNull(),
	snoozedUntil: datetime("snoozed_until", { mode: 'string'}),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	deletedAt: timestamp("deleted_at", { mode: 'string' }),
},
(table) => {
	return {
		jobIdIdx: index("job_id_index").on(table.jobId),
		typeIdx: index("type_index").on(table.type),
		statusIdx: index("status_index").on(table.status),
		recommendationsId: primaryKey({ columns: [table.id], name: "recommendations_id"}),
	}
});

export const roles = mysqlTable("roles", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	parentId: int("parent_id", { unsigned: true }),
	name: varchar("name", { length: 191 }).notNull(),
	slug: varchar("slug", { length: 191 }),
	image: varchar("image", { length: 191 }),
	isParent: tinyint("is_parent").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	icon: varchar("icon", { length: 191 }),
	color: varchar("color", { length: 191 }),
	seoTitle: varchar("seo_title", { length: 191 }),
	seoDescription: text("seo_description"),
	seoImage: varchar("seo_image", { length: 191 }),
	isDigital: tinyint("is_digital").default(0).notNull(),
},
(table) => {
	return {
		rolesId: primaryKey({ columns: [table.id], name: "roles_id"}),
	}
});

export const scoutTemplates = mysqlTable("scout_templates", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 191 }).notNull(),
	message: text("message").notNull(),
	status: varchar("status", { length: 191 }).default('draft').notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		scoutTemplatesId: primaryKey({ columns: [table.id], name: "scout_templates_id"}),
	}
});

export const screeningAnswers = mysqlTable("screening_answers", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	applicantId: int("applicant_id", { unsigned: true }).notNull().references(() => applicants.id, { onDelete: "cascade" } ),
	questionId: int("question_id", { unsigned: true }).notNull().references(() => questions.id, { onDelete: "cascade" } ),
	answer: text("answer").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		screeningAnswersId: primaryKey({ columns: [table.id], name: "screening_answers_id"}),
	}
});

export const screeningQuestions = mysqlTable("screening_questions", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	industryId: int("industry_id", { unsigned: true }),
	questionId: int("question_id", { unsigned: true }).notNull().references(() => questions.id, { onDelete: "cascade" } ),
	priority: int("priority").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		screeningQuestionsId: primaryKey({ columns: [table.id], name: "screening_questions_id"}),
	}
});

export const sessions = mysqlTable("sessions", {
	id: varchar("id", { length: 191 }).notNull(),
	userId: int("user_id", { unsigned: true }),
	ipAddress: varchar("ip_address", { length: 45 }),
	userAgent: text("user_agent"),
	payload: text("payload").notNull(),
	lastActivity: int("last_activity").notNull(),
},
(table) => {
	return {
		sessionsIdUnique: unique("sessions_id_unique").on(table.id),
	}
});

export const settings = mysqlTable("settings", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	type: varchar("type", { length: 191 }).notNull(),
	value: varchar("value", { length: 191 }).notNull(),
	extra: varchar("extra", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		settingsId: primaryKey({ columns: [table.id], name: "settings_id"}),
	}
});

export const siteAlerts = mysqlTable("site_alerts", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	batchUuid: char("batch_uuid", { length: 36 }),
	type: varchar("type", { length: 191 }).default('Frontend').notNull(),
	level: varchar("level", { length: 191 }).default('error').notNull(),
	url: varchar("url", { length: 191 }),
	userData: json("user_data"),
	alertData: json("alert_data"),
	status: varchar("status", { length: 191 }).default('reported').notNull(),
	comments: text("comments"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		siteAlertsId: primaryKey({ columns: [table.id], name: "site_alerts_id"}),
	}
});

export const skills = mysqlTable("skills", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	slug: int("slug"),
	category: varchar("category", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		skillsId: primaryKey({ columns: [table.id], name: "skills_id"}),
	}
});

export const srsFeedbacks = mysqlTable("srs_feedbacks", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	clientId: int("client_id").notNull(),
	attractiveFeature: text("attractive_feature"),
	improvement: text("improvement"),
	rating: int("rating").default(1).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		srsFeedbacksId: primaryKey({ columns: [table.id], name: "srs_feedbacks_id"}),
	}
});

export const subscriptionItems = mysqlTable("subscription_items", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	subscriptionId: bigint("subscription_id", { mode: "number", unsigned: true }).notNull(),
	stripeId: varchar("stripe_id", { length: 191 }).notNull(),
	stripePlan: varchar("stripe_plan", { length: 191 }).notNull(),
	quantity: int("quantity").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		stripeIdIdx: index().on(table.stripeId),
		subscriptionItemsId: primaryKey({ columns: [table.id], name: "subscription_items_id"}),
		subscriptionItemsSubscriptionIdStripePlanUnique: unique("subscription_items_subscription_id_stripe_plan_unique").on(table.subscriptionId, table.stripePlan),
	}
});

export const subscriptions = mysqlTable("subscriptions", {
	id: bigint("id", { mode: "number", unsigned: true }).autoincrement().notNull(),
	clientId: bigint("client_id", { mode: "number", unsigned: true }).notNull(),
	name: varchar("name", { length: 191 }).notNull(),
	stripeId: varchar("stripe_id", { length: 191 }).notNull(),
	stripeStatus: varchar("stripe_status", { length: 191 }).notNull(),
	stripePlan: varchar("stripe_plan", { length: 191 }),
	quantity: int("quantity"),
	trialEndsAt: timestamp("trial_ends_at", { mode: 'string' }),
	endsAt: timestamp("ends_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		clientIdStripeStatusIdx: index().on(table.clientId, table.stripeStatus),
		subscriptionsId: primaryKey({ columns: [table.id], name: "subscriptions_id"}),
	}
});

export const taggables = mysqlTable("taggables", {
	tagId: int("tag_id", { unsigned: true }).notNull().references(() => tags.id, { onDelete: "cascade" } ),
	taggableType: varchar("taggable_type", { length: 191 }).notNull(),
	taggableId: bigint("taggable_id", { mode: "number", unsigned: true }).notNull(),
	score: double("score", { precision: 8, scale: 2 }).notNull(),
},
(table) => {
	return {
		taggableTypeTaggableIdIdx: index().on(table.taggableType, table.taggableId),
		idxTaggableId: index("idx_taggable_id").on(table.taggableId),
	}
});

export const tags = mysqlTable("tags", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	name: json("name").notNull(),
	slug: json("slug").notNull(),
	subtags: text("subtags"),
	categories: text("categories"),
	type: varchar("type", { length: 191 }),
	orderColumn: int("order_column"),
	isVerified: tinyint("is_verified").default(0).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	isPopular: tinyint("is_popular").default(0).notNull(),
},
(table) => {
	return {
		idxId: index("idx_id").on(table.id),
		tagsId: primaryKey({ columns: [table.id], name: "tags_id"}),
	}
});

export const userChapter = mysqlTable("user_chapter", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id", { unsigned: true }).notNull().references(() => users.id, { onDelete: "cascade" } ),
	chapterId: int("chapter_id", { unsigned: true }).notNull(),
	isPassed: tinyint("is_passed").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		userChapterId: primaryKey({ columns: [table.id], name: "user_chapter_id"}),
	}
});

export const userFavourite = mysqlTable("user_favourite", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id", { unsigned: true }).notNull().references(() => users.id, { onDelete: "cascade" } ),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		userFavouriteId: primaryKey({ columns: [table.id], name: "user_favourite_id"}),
	}
});

export const userOnesignal = mysqlTable("user_onesignal", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id", { unsigned: true }).notNull().references(() => users.id, { onDelete: "cascade" } ),
	playerId: varchar("player_id", { length: 191 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		userOnesignalId: primaryKey({ columns: [table.id], name: "user_onesignal_id"}),
	}
});

export const userVerifications = mysqlTable("user_verifications", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	token: varchar("token", { length: 191 }).notNull(),
	userId: int("user_id", { unsigned: true }).references(() => users.id, { onDelete: "cascade" } ),
	candidateId: int("candidate_id", { unsigned: true }).references(() => candidates.id, { onDelete: "cascade" } ),
	clientId: int("client_id", { unsigned: true }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		userVerificationsId: primaryKey({ columns: [table.id], name: "user_verifications_id"}),
	}
});

export const userWithdraw = mysqlTable("user_withdraw", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	userId: int("user_id", { unsigned: true }).notNull().references(() => users.id, { onDelete: "cascade" } ),
	amount: int("amount").notNull(),
	status: mysqlEnum("status", ['requested','completed','rejected']).notNull(),
	transactionRef: varchar("transaction_ref", { length: 191 }),
	transactionDate: datetime("transaction_date", { mode: 'string'}),
	note: varchar("note", { length: 191 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	bankName: varchar("bank_name", { length: 191 }),
	bankNo: varchar("bank_no", { length: 191 }),
},
(table) => {
	return {
		userWithdrawId: primaryKey({ columns: [table.id], name: "user_withdraw_id"}),
	}
});

export const users = mysqlTable("users", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	rankId: int("rank_id"),
	name: varchar("name", { length: 191 }).notNull(),
	isAdmin: tinyint("is_admin").default(0).notNull(),
	isSale: tinyint("is_sale").default(0).notNull(),
	isConsultant: tinyint("is_consultant").default(0).notNull(),
	isCorpcare: tinyint("is_corpcare").default(0).notNull(),
	email: varchar("email", { length: 191 }).notNull(),
	password: varchar("password", { length: 191 }).notNull(),
	mobile: varchar("mobile", { length: 191 }).notNull(),
	ic: varchar("ic", { length: 191 }).default(''),
	experienceYears: int("experience_years"),
	occupation: varchar("occupation", { length: 191 }),
	image: varchar("image", { length: 191 }),
	bankNo: varchar("bank_no", { length: 191 }),
	bankName: varchar("bank_name", { length: 191 }),
	location: varchar("location", { length: 191 }),
	state: varchar("state", { length: 191 }),
	specialization: varchar("specialization", { length: 191 }).default(''),
	appFirstLogin: datetime("app_first_login", { mode: 'string'}),
	lastLogin: datetime("last_login", { mode: 'string'}),
	lastLoginIp: varchar("last_login_ip", { length: 191 }),
	registrationIp: varchar("registration_ip", { length: 191 }),
	isVerified: tinyint("is_verified").default(0).notNull(),
	isBanned: tinyint("is_banned").default(0).notNull(),
	isMigrated: tinyint("is_migrated").default(0).notNull(),
	isPassed: tinyint("is_passed"),
	rememberToken: varchar("remember_token", { length: 100 }),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	remark: varchar("remark", { length: 191 }),
	mailchimpId: varchar("mailchimp_id", { length: 191 }),
	savedJobs: json("saved_jobs").notNull(),
	recruiterGroup: varchar("recruiter_group", { length: 191 }).default('B').notNull(),
	companyName: varchar("company_name", { length: 191 }),
	age: int("age"),
	designation: varchar("designation", { length: 191 }),
	facebook: varchar("facebook", { length: 191 }),
	twitter: varchar("twitter", { length: 191 }),
	instagram: varchar("instagram", { length: 191 }),
	linkedin: varchar("linkedin", { length: 191 }),
	languages: varchar("languages", { length: 191 }),
	committedHoursDaily: int("committed_hours_daily"),
	sourcingMethod: varchar("sourcing_method", { length: 191 }),
	isTechnicalRecruiter: tinyint("is_technical_recruiter").default(0),
	isWorkingFulltime: tinyint("is_working_fulltime").default(0),
	lastSeen: timestamp("last_seen", { mode: 'string' }),
},
(table) => {
	return {
		usersId: primaryKey({ columns: [table.id], name: "users_id"}),
		usersEmailUnique: unique("users_email_unique").on(table.email),
	}
});

export const wantedJob = mysqlTable("wanted_job", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	wantedId: int("wanted_id", { unsigned: true }).notNull().references(() => wanteds.id, { onDelete: "cascade" } ),
	jobId: int("job_id", { unsigned: true }).notNull().references(() => jobs.id, { onDelete: "cascade" } ),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		wantedJobId: primaryKey({ columns: [table.id], name: "wanted_job_id"}),
	}
});

export const wanteds = mysqlTable("wanteds", {
	id: int("id", { unsigned: true }).autoincrement().notNull(),
	title: varchar("title", { length: 191 }).notNull(),
	image: varchar("image", { length: 191 }).notNull(),
	isActive: tinyint("is_active").default(1).notNull(),
	priority: int("priority"),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
},
(table) => {
	return {
		wantedsId: primaryKey({ columns: [table.id], name: "wanteds_id"}),
	}
});