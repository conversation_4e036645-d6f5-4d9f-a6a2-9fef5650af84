{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "5", "dialect": "mysql", "tables": {"action_events": {"name": "action_events", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "batch_id": {"autoincrement": false, "name": "batch_id", "type": "char(36)", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "actionable_type": {"autoincrement": false, "name": "actionable_type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "actionable_id": {"autoincrement": false, "name": "actionable_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "target_type": {"autoincrement": false, "name": "target_type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "target_id": {"autoincrement": false, "name": "target_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "model_type": {"autoincrement": false, "name": "model_type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "model_id": {"autoincrement": false, "name": "model_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "fields": {"autoincrement": false, "name": "fields", "type": "text", "primaryKey": false, "notNull": true}, "status": {"default": "'running'", "autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(25)", "primaryKey": false, "notNull": true}, "exception": {"autoincrement": false, "name": "exception", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "original": {"autoincrement": false, "name": "original", "type": "text", "primaryKey": false, "notNull": false}, "changes": {"autoincrement": false, "name": "changes", "type": "text", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"action_events_id": {"name": "action_events_id", "columns": ["id"]}}, "indexes": {"action_events_actionable_type_actionable_id_index": {"name": "action_events_actionable_type_actionable_id_index", "columns": ["actionable_type", "actionable_id"], "isUnique": false}, "action_events_batch_id_model_type_model_id_index": {"name": "action_events_batch_id_model_type_model_id_index", "columns": ["batch_id", "model_type", "model_id"], "isUnique": false}, "action_events_user_id_index": {"name": "action_events_user_id_index", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "activities": {"name": "activities", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "applicant_id": {"autoincrement": false, "name": "applicant_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "event_id": {"autoincrement": false, "name": "event_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "amount": {"autoincrement": false, "name": "amount", "type": "int", "primaryKey": false, "notNull": false}, "point": {"autoincrement": false, "name": "point", "type": "int", "primaryKey": false, "notNull": false}, "screening_answer": {"autoincrement": false, "name": "screening_answer", "type": "text", "primaryKey": false, "notNull": false}, "cv_masking": {"autoincrement": false, "name": "cv_masking", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_no": {"default": 1, "autoincrement": false, "name": "interview_no", "type": "tinyint", "primaryKey": false, "notNull": false}, "interview_code": {"autoincrement": false, "name": "interview_code", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_location": {"autoincrement": false, "name": "interview_location", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_datetime": {"autoincrement": false, "name": "interview_datetime", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_date_time": {"autoincrement": false, "name": "interview_date_time", "type": "datetime", "primaryKey": false, "notNull": false}, "interview_pic": {"autoincrement": false, "name": "interview_pic", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_pic_tel": {"autoincrement": false, "name": "interview_pic_tel", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_remarks": {"autoincrement": false, "name": "interview_remarks", "type": "text", "primaryKey": false, "notNull": false}, "interview_pic_position": {"autoincrement": false, "name": "interview_pic_position", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "interview_accept": {"autoincrement": false, "name": "interview_accept", "type": "tinyint", "primaryKey": false, "notNull": false}, "interview_reschedule": {"autoincrement": false, "name": "interview_reschedule", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "feedback": {"autoincrement": false, "name": "feedback", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "remark": {"autoincrement": false, "name": "remark", "type": "text", "primaryKey": false, "notNull": false}, "remark_client": {"autoincrement": false, "name": "remark_client", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "failure_reason": {"autoincrement": false, "name": "failure_reason", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "failure_reason_detail": {"autoincrement": false, "name": "failure_reason_detail", "type": "text", "primaryKey": false, "notNull": false}, "dev_note": {"autoincrement": false, "name": "dev_note", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"activities_id": {"name": "activities_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"activities_applicant_id_foreign": {"name": "activities_applicant_id_foreign", "tableFrom": "activities", "tableTo": "applicants", "columnsFrom": ["applicant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "activities_event_id_foreign": {"name": "activities_event_id_foreign", "tableFrom": "activities", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "activities_user_id_foreign": {"name": "activities_user_id_foreign", "tableFrom": "activities", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "announcements": {"name": "announcements", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image_path": {"autoincrement": false, "name": "image_path", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "link_to": {"autoincrement": false, "name": "link_to", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_link_target_blank": {"default": 0, "autoincrement": false, "name": "is_link_target_blank", "type": "tinyint", "primaryKey": false, "notNull": true}, "type": {"default": "'general'", "autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "is_alert": {"default": 0, "autoincrement": false, "name": "is_alert", "type": "tinyint", "primaryKey": false, "notNull": true}, "status": {"default": "'draft'", "autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"announcements_id": {"name": "announcements_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "app_version": {"name": "app_version", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "version": {"autoincrement": false, "name": "version", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "device": {"default": "'android'", "autoincrement": false, "name": "device", "type": "enum('android','ios')", "primaryKey": false, "notNull": false}, "remark": {"autoincrement": false, "name": "remark", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"app_version_id": {"name": "app_version_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "applicants": {"name": "applicants", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"default": 1, "autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "current_event_id": {"autoincrement": false, "name": "current_event_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "internal_event_id": {"autoincrement": false, "name": "internal_event_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "rating": {"autoincrement": false, "name": "rating", "type": "int", "primaryKey": false, "notNull": true}, "verification_code": {"autoincrement": false, "name": "verification_code", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_verified": {"autoincrement": false, "name": "is_verified", "type": "tinyint", "primaryKey": false, "notNull": false}, "notice_period": {"autoincrement": false, "name": "notice_period", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "reason_to_apply": {"autoincrement": false, "name": "reason_to_apply", "type": "text", "primaryKey": false, "notNull": false}, "reason_to_leave": {"autoincrement": false, "name": "reason_to_leave", "type": "text", "primaryKey": false, "notNull": false}, "screening_answer": {"autoincrement": false, "name": "screening_answer", "type": "text", "primaryKey": false, "notNull": false}, "salary_current": {"autoincrement": false, "name": "salary_current", "type": "int", "primaryKey": false, "notNull": false}, "salary_expected": {"autoincrement": false, "name": "salary_expected", "type": "int", "primaryKey": false, "notNull": false}, "salary_final": {"autoincrement": false, "name": "salary_final", "type": "int", "primaryKey": false, "notNull": false}, "auto_screen_pass": {"autoincrement": false, "name": "auto_screen_pass", "type": "tinyint", "primaryKey": false, "notNull": false}, "auto_screen_report": {"autoincrement": false, "name": "auto_screen_report", "type": "text", "primaryKey": false, "notNull": false}, "note": {"autoincrement": false, "name": "note", "type": "text", "primaryKey": false, "notNull": false}, "note_client": {"autoincrement": false, "name": "note_client", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "report": {"autoincrement": false, "name": "report", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "urgent": {"autoincrement": false, "name": "urgent", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_suggested": {"default": 0, "autoincrement": false, "name": "is_suggested", "type": "tinyint", "primaryKey": false, "notNull": true}, "matching_score": {"autoincrement": false, "name": "matching_score", "type": "decimal(8,2)", "primaryKey": false, "notNull": false}, "matching_report": {"autoincrement": false, "name": "matching_report", "type": "json", "primaryKey": false, "notNull": false}, "scout_data": {"autoincrement": false, "name": "scout_data", "type": "json", "primaryKey": false, "notNull": false}, "apply_data": {"autoincrement": false, "name": "apply_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "job_screening_answers": {"autoincrement": false, "name": "job_screening_answers", "type": "json", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"applicants_id": {"name": "applicants_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"applicants_candidate_id_foreign": {"name": "applicants_candidate_id_foreign", "tableFrom": "applicants", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "applicants_current_event_id_foreign": {"name": "applicants_current_event_id_foreign", "tableFrom": "applicants", "tableTo": "events", "columnsFrom": ["current_event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "applicants_internal_event_id_foreign": {"name": "applicants_internal_event_id_foreign", "tableFrom": "applicants", "tableTo": "events", "columnsFrom": ["internal_event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "applicants_job_id_foreign": {"name": "applicants_job_id_foreign", "tableFrom": "applicants", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "applicants_user_id_foreign": {"name": "applicants_user_id_foreign", "tableFrom": "applicants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "assesments": {"name": "assesments", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "question": {"autoincrement": false, "name": "question", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "o1": {"autoincrement": false, "name": "o1", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "o2": {"autoincrement": false, "name": "o2", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "o3": {"autoincrement": false, "name": "o3", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "o4": {"autoincrement": false, "name": "o4", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "correct_answer": {"autoincrement": false, "name": "correct_answer", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"assesments_id": {"name": "assesments_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "bill_items": {"name": "bill_items", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "bill_id": {"autoincrement": false, "name": "bill_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "product_id": {"autoincrement": false, "name": "product_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "price": {"autoincrement": false, "name": "price", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"bill_items_id": {"name": "bill_items_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"bill_items_bill_id_foreign": {"name": "bill_items_bill_id_foreign", "tableFrom": "bill_items", "tableTo": "bills", "columnsFrom": ["bill_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bill_items_product_id_foreign": {"name": "bill_items_product_id_foreign", "tableFrom": "bill_items", "tableTo": "products", "columnsFrom": ["product_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "bills": {"name": "bills", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "coupon_id": {"autoincrement": false, "name": "coupon_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "enum('pending','paid','cancel')", "primaryKey": false, "notNull": false}, "total_price": {"autoincrement": false, "name": "total_price", "type": "decimal(8,2)", "primaryKey": false, "notNull": true}, "tax": {"default": 6, "autoincrement": false, "name": "tax", "type": "int", "primaryKey": false, "notNull": true}, "net_price": {"autoincrement": false, "name": "net_price", "type": "decimal(8,2)", "primaryKey": false, "notNull": true}, "payment_ref": {"autoincrement": false, "name": "payment_ref", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "payment_method": {"autoincrement": false, "name": "payment_method", "type": "enum('card','banktransfer','cheque')", "primaryKey": false, "notNull": false}, "expiry_date": {"autoincrement": false, "name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"bills_id": {"name": "bills_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"bills_company_id_foreign": {"name": "bills_company_id_foreign", "tableFrom": "bills", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "bills_coupon_id_foreign": {"name": "bills_coupon_id_foreign", "tableFrom": "bills", "tableTo": "coupons", "columnsFrom": ["coupon_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_certification": {"name": "candidate_certification", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "start": {"autoincrement": false, "name": "start", "type": "date", "primaryKey": false, "notNull": false}, "end": {"autoincrement": false, "name": "end", "type": "date", "primaryKey": false, "notNull": false}, "created_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "onUpdate": true}}, "compositePrimaryKeys": {"candidate_certification_id": {"name": "candidate_certification_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_certification_candidate_id_foreign": {"name": "candidate_certification_candidate_id_foreign", "tableFrom": "candidate_certification", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_company": {"name": "candidate_company", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_company_id": {"name": "candidate_company_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_company_candidate_id_foreign": {"name": "candidate_company_candidate_id_foreign", "tableFrom": "candidate_company", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "candidate_company_company_id_foreign": {"name": "candidate_company_company_id_foreign", "tableFrom": "candidate_company", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_education": {"name": "candidate_education", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "qualification": {"autoincrement": false, "name": "qualification", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "institute": {"autoincrement": false, "name": "institute", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "start": {"autoincrement": false, "name": "start", "type": "date", "primaryKey": false, "notNull": false}, "end": {"autoincrement": false, "name": "end", "type": "date", "primaryKey": false, "notNull": false}, "result": {"autoincrement": false, "name": "result", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "onUpdate": true}}, "compositePrimaryKeys": {"candidate_education_id": {"name": "candidate_education_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_education_candidate_id_foreign": {"name": "candidate_education_candidate_id_foreign", "tableFrom": "candidate_education", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_favourite": {"name": "candidate_favourite", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_favourite_id": {"name": "candidate_favourite_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_favourite_candidate_id_foreign": {"name": "candidate_favourite_candidate_id_foreign", "tableFrom": "candidate_favourite", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "candidate_favourite_job_id_foreign": {"name": "candidate_favourite_job_id_foreign", "tableFrom": "candidate_favourite", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_following": {"name": "candidate_following", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_following_id": {"name": "candidate_following_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_following_candidate_id_foreign": {"name": "candidate_following_candidate_id_foreign", "tableFrom": "candidate_following", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "candidate_following_company_id_foreign": {"name": "candidate_following_company_id_foreign", "tableFrom": "candidate_following", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_inbox": {"name": "candidate_inbox", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "inboxable_type": {"autoincrement": false, "name": "inboxable_type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "inboxable_id": {"autoincrement": false, "name": "inboxable_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_inbox_id": {"name": "candidate_inbox_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_inbox_candidate_id_foreign": {"name": "candidate_inbox_candidate_id_foreign", "tableFrom": "candidate_inbox", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_language": {"name": "candidate_language", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "language": {"autoincrement": false, "name": "language", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "level": {"autoincrement": false, "name": "level", "type": "enum('Basic','Intermediate','Advance','Native')", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_language_id": {"name": "candidate_language_id", "columns": ["id"]}}, "indexes": {"candidate_language_candidate_id_foreign": {"name": "candidate_language_candidate_id_foreign", "columns": ["candidate_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "candidate_logs": {"name": "candidate_logs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "event": {"autoincrement": false, "name": "event", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"candidate_logs_id": {"name": "candidate_logs_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_logs_candidate_id_foreign": {"name": "candidate_logs_candidate_id_foreign", "tableFrom": "candidate_logs", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "candidate_logs_job_id_foreign": {"name": "candidate_logs_job_id_foreign", "tableFrom": "candidate_logs", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_onesignal": {"name": "candidate_ones<PERSON>al", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "player_id": {"autoincrement": false, "name": "player_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_onesignal_id": {"name": "candidate_onesignal_id", "columns": ["id"]}}, "indexes": {"candidate_onesignal_player_id_index": {"name": "candidate_onesignal_player_id_index", "columns": ["player_id"], "isUnique": false}}, "foreignKeys": {"candidate_onesignal_candidate_id_foreign": {"name": "candidate_ones<PERSON>al_candidate_id_foreign", "tableFrom": "candidate_ones<PERSON>al", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_portfolio": {"name": "candidate_portfolio", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "link": {"autoincrement": false, "name": "link", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "onUpdate": true}, "file": {"autoincrement": false, "name": "file", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_portfolio_id": {"name": "candidate_portfolio_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_portfolio_candidate_id_foreign": {"name": "candidate_portfolio_candidate_id_foreign", "tableFrom": "candidate_portfolio", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_scout_tags": {"name": "candidate_scout_tags", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "is_active_looking": {"default": 0, "autoincrement": false, "name": "is_active_looking", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_short_notice": {"default": 0, "autoincrement": false, "name": "is_short_notice", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_ready": {"default": 0, "autoincrement": false, "name": "is_ready", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_new": {"default": 0, "autoincrement": false, "name": "is_new", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_updated_cv": {"default": 0, "autoincrement": false, "name": "is_updated_cv", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_hot": {"default": 0, "autoincrement": false, "name": "is_hot", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_fresh_graduate": {"default": 0, "autoincrement": false, "name": "is_fresh_graduate", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_scout_tags_id": {"name": "candidate_scout_tags_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_scout_tags_candidate_id_foreign": {"name": "candidate_scout_tags_candidate_id_foreign", "tableFrom": "candidate_scout_tags", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_skill": {"name": "candidate_skill", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "skill": {"autoincrement": false, "name": "skill", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "rate": {"autoincrement": false, "name": "rate", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_skill_id": {"name": "candidate_skill_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_skill_candidate_id_foreign": {"name": "candidate_skill_candidate_id_foreign", "tableFrom": "candidate_skill", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidate_work": {"name": "candidate_work", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_name": {"autoincrement": false, "name": "company_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "role": {"autoincrement": false, "name": "role", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "start": {"autoincrement": false, "name": "start", "type": "date", "primaryKey": false, "notNull": false}, "end": {"autoincrement": false, "name": "end", "type": "date", "primaryKey": false, "notNull": false}, "responsibilities": {"autoincrement": false, "name": "responsibilities", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidate_work_id": {"name": "candidate_work_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"candidate_work_candidate_id_foreign": {"name": "candidate_work_candidate_id_foreign", "tableFrom": "candidate_work", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "candidates": {"name": "candidates", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "password": {"autoincrement": false, "name": "password", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "mobile": {"autoincrement": false, "name": "mobile", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "ic": {"autoincrement": false, "name": "ic", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "birth_year": {"autoincrement": false, "name": "birth_year", "type": "int", "primaryKey": false, "notNull": false}, "gender": {"autoincrement": false, "name": "gender", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "location": {"autoincrement": false, "name": "location", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "state": {"autoincrement": false, "name": "state", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "language": {"autoincrement": false, "name": "language", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "race": {"autoincrement": false, "name": "race", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "linkedin": {"default": "''", "autoincrement": false, "name": "linkedin", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "portfolio": {"autoincrement": false, "name": "portfolio", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "cv": {"autoincrement": false, "name": "cv", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "cv_masking": {"autoincrement": false, "name": "cv_masking", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "cv_rating": {"autoincrement": false, "name": "cv_rating", "type": "int", "primaryKey": false, "notNull": false}, "cv_report": {"autoincrement": false, "name": "cv_report", "type": "text", "primaryKey": false, "notNull": false}, "cv_verified": {"default": 0, "autoincrement": false, "name": "cv_verified", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_blacklisted": {"autoincrement": false, "name": "is_blacklisted", "type": "tinyint", "primaryKey": false, "notNull": false}, "is_local": {"autoincrement": false, "name": "is_local", "type": "tinyint", "primaryKey": false, "notNull": false}, "own_transport": {"autoincrement": false, "name": "own_transport", "type": "tinyint", "primaryKey": false, "notNull": false}, "own_driver_license": {"autoincrement": false, "name": "own_driver_license", "type": "tinyint", "primaryKey": false, "notNull": false}, "willing_to_travel": {"autoincrement": false, "name": "willing_to_travel", "type": "tinyint", "primaryKey": false, "notNull": false}, "got_work_permit": {"autoincrement": false, "name": "got_work_permit", "type": "tinyint", "primaryKey": false, "notNull": false}, "got_visa": {"autoincrement": false, "name": "got_visa", "type": "tinyint", "primaryKey": false, "notNull": false}, "is_verified": {"autoincrement": false, "name": "is_verified", "type": "tinyint", "primaryKey": false, "notNull": false}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "current_position": {"autoincrement": false, "name": "current_position", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "level": {"autoincrement": false, "name": "level", "type": "enum('entry','junior','senior','manager','senior manager')", "primaryKey": false, "notNull": false}, "role_id": {"autoincrement": false, "name": "role_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "role_ids": {"autoincrement": false, "name": "role_ids", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "language_level": {"autoincrement": false, "name": "language_level", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "followup": {"autoincrement": false, "name": "followup", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "remarks": {"autoincrement": false, "name": "remarks", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "skills": {"autoincrement": false, "name": "skills", "type": "json", "primaryKey": false, "notNull": false}, "is_offer": {"default": 0, "autoincrement": false, "name": "is_offer", "type": "tinyint", "primaryKey": false, "notNull": true}, "offer_data": {"autoincrement": false, "name": "offer_data", "type": "json", "primaryKey": false, "notNull": false}, "experience_data": {"autoincrement": false, "name": "experience_data", "type": "json", "primaryKey": false, "notNull": false}, "is_app": {"default": 0, "autoincrement": false, "name": "is_app", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_primary": {"default": 0, "autoincrement": false, "name": "is_primary", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_migrated": {"default": 0, "autoincrement": false, "name": "is_migrated", "type": "tinyint", "primaryKey": false, "notNull": true}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cv_updated_at": {"autoincrement": false, "name": "cv_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login": {"autoincrement": false, "name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "language_detailed": {"autoincrement": false, "name": "language_detailed", "type": "json", "primaryKey": false, "notNull": false}, "education": {"autoincrement": false, "name": "education", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "expected_salary": {"autoincrement": false, "name": "expected_salary", "type": "int", "primaryKey": false, "notNull": false}, "education_level": {"autoincrement": false, "name": "education_level", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "open_to_work": {"default": 1, "autoincrement": false, "name": "open_to_work", "type": "tinyint", "primaryKey": false, "notNull": true}, "last_seen": {"autoincrement": false, "name": "last_seen", "type": "timestamp", "primaryKey": false, "notNull": false}, "current_salary": {"autoincrement": false, "name": "current_salary", "type": "int", "primaryKey": false, "notNull": false}, "notice_period": {"autoincrement": false, "name": "notice_period", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "google_id": {"autoincrement": false, "name": "google_id", "type": "int", "primaryKey": false, "notNull": false}, "google_token": {"autoincrement": false, "name": "google_token", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "google_refresh_token": {"autoincrement": false, "name": "google_refresh_token", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "facebook_id": {"autoincrement": false, "name": "facebook_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "linkedin_id": {"autoincrement": false, "name": "linkedin_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "about_me": {"autoincrement": false, "name": "about_me", "type": "text", "primaryKey": false, "notNull": false}, "facebook": {"default": "''", "autoincrement": false, "name": "facebook", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "github": {"default": "''", "autoincrement": false, "name": "github", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "instagram": {"default": "''", "autoincrement": false, "name": "instagram", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "twitter": {"default": "''", "autoincrement": false, "name": "twitter", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "website": {"default": "''", "autoincrement": false, "name": "website", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "nationality": {"autoincrement": false, "name": "nationality", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image_url": {"autoincrement": false, "name": "image_url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"candidates_id": {"name": "candidates_id", "columns": ["id"]}}, "indexes": {"candidates_name_index": {"name": "candidates_name_index", "columns": ["name"], "isUnique": false}, "candidates_email_index": {"name": "candidates_email_index", "columns": ["email"], "isUnique": false}, "candidates_user_id_index": {"name": "candidates_user_id_index", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {"candidates_role_id_foreign": {"name": "candidates_role_id_foreign", "tableFrom": "candidates", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "challenges": {"name": "challenges", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "event_id": {"autoincrement": false, "name": "event_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "enum('special','daily','weekly','monthly')", "primaryKey": false, "notNull": true}, "min_count": {"autoincrement": false, "name": "min_count", "type": "int unsigned", "primaryKey": false, "notNull": true}, "max_count": {"autoincrement": false, "name": "max_count", "type": "int unsigned", "primaryKey": false, "notNull": false}, "counting_method": {"autoincrement": false, "name": "counting_method", "type": "text", "primaryKey": false, "notNull": false}, "status": {"default": "'active'", "autoincrement": false, "name": "status", "type": "enum('active','inactive')", "primaryKey": false, "notNull": true}, "is_verified": {"default": 0, "autoincrement": false, "name": "is_verified", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"challenges_id": {"name": "challenges_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"challenges_event_id_foreign": {"name": "challenges_event_id_foreign", "tableFrom": "challenges", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "chapters": {"name": "chapters", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "heading": {"autoincrement": false, "name": "heading", "type": "text", "primaryKey": false, "notNull": true}, "subheading": {"autoincrement": false, "name": "subheading", "type": "text", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "reward": {"autoincrement": false, "name": "reward", "type": "int", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "text", "primaryKey": false, "notNull": false}, "ranking": {"autoincrement": false, "name": "ranking", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "sort_order": {"autoincrement": false, "name": "sort_order", "type": "int", "primaryKey": false, "notNull": false}, "status": {"default": "'draft'", "autoincrement": false, "name": "status", "type": "enum('draft','open','closed')", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"chapters_id": {"name": "chapters_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "client_payments": {"name": "client_payments", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "uuid": {"autoincrement": false, "name": "uuid", "type": "char(36)", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "plan_id": {"autoincrement": false, "name": "plan_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "amount": {"autoincrement": false, "name": "amount", "type": "decimal(8,2)", "primaryKey": false, "notNull": false}, "payment_method": {"autoincrement": false, "name": "payment_method", "type": "json", "primaryKey": false, "notNull": false}, "purchase_data": {"autoincrement": false, "name": "purchase_data", "type": "json", "primaryKey": false, "notNull": false}, "invoice_data": {"autoincrement": false, "name": "invoice_data", "type": "json", "primaryKey": false, "notNull": false}, "receipt_data": {"autoincrement": false, "name": "receipt_data", "type": "json", "primaryKey": false, "notNull": false}, "status": {"default": "'pending'", "autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "status_data": {"autoincrement": false, "name": "status_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"client_payments_id": {"name": "client_payments_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"client_payments_client_id_foreign": {"name": "client_payments_client_id_foreign", "tableFrom": "client_payments", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "client_scouts": {"name": "client_scouts", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "code": {"autoincrement": false, "name": "code", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "message": {"autoincrement": false, "name": "message", "type": "text", "primaryKey": false, "notNull": false}, "deadline": {"autoincrement": false, "name": "deadline", "type": "date", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "extra_data": {"autoincrement": false, "name": "extra_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"client_scouts_id": {"name": "client_scouts_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"client_scouts_candidate_id_foreign": {"name": "client_scouts_candidate_id_foreign", "tableFrom": "client_scouts", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "client_scouts_client_id_foreign": {"name": "client_scouts_client_id_foreign", "tableFrom": "client_scouts", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "client_scouts_job_id_foreign": {"name": "client_scouts_job_id_foreign", "tableFrom": "client_scouts", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "client_subscriptions": {"name": "client_subscriptions", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int", "primaryKey": false, "notNull": true}, "subscription_plan_id": {"autoincrement": false, "name": "subscription_plan_id", "type": "int", "primaryKey": false, "notNull": true}, "uuid": {"autoincrement": false, "name": "uuid", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "old_expiry_date": {"autoincrement": false, "name": "old_expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "new_expiry_date": {"autoincrement": false, "name": "new_expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "is_expired": {"default": 0, "autoincrement": false, "name": "is_expired", "type": "tinyint", "primaryKey": false, "notNull": true}, "note": {"autoincrement": false, "name": "note", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "json_data": {"autoincrement": false, "name": "json_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"client_subscriptions_id": {"name": "client_subscriptions_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "clients": {"name": "clients", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "password": {"autoincrement": false, "name": "password", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "tel": {"autoincrement": false, "name": "tel", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "subscription_plan_id": {"default": 1, "autoincrement": false, "name": "subscription_plan_id", "type": "int", "primaryKey": false, "notNull": true}, "expiry_date": {"autoincrement": false, "name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "job_quota": {"default": 0, "autoincrement": false, "name": "job_quota", "type": "int", "primaryKey": false, "notNull": true}, "scout_credit": {"default": 0, "autoincrement": false, "name": "scout_credit", "type": "int", "primaryKey": false, "notNull": true}, "status": {"default": "'active'", "autoincrement": false, "name": "status", "type": "enum('pending','active','inactive')", "primaryKey": false, "notNull": true}, "is_verified": {"default": 0, "autoincrement": false, "name": "is_verified", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_admin": {"default": 0, "autoincrement": false, "name": "is_admin", "type": "tinyint", "primaryKey": false, "notNull": true}, "last_login": {"autoincrement": false, "name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "stripe_id": {"autoincrement": false, "name": "stripe_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "card_brand": {"autoincrement": false, "name": "card_brand", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "card_last_four": {"autoincrement": false, "name": "card_last_four", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false}, "trial_ends_at": {"autoincrement": false, "name": "trial_ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "remember_token": {"autoincrement": false, "name": "remember_token", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"clients_id": {"name": "clients_id", "columns": ["id"]}}, "indexes": {"clients_stripe_id_index": {"name": "clients_stripe_id_index", "columns": ["stripe_id"], "isUnique": false}}, "foreignKeys": {"clients_company_id_foreign": {"name": "clients_company_id_foreign", "tableFrom": "clients", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "companies": {"name": "companies", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "slug": {"autoincrement": false, "name": "slug", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "reg_no": {"autoincrement": false, "name": "reg_no", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "building": {"autoincrement": false, "name": "building", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "address": {"autoincrement": false, "name": "address", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "postal_code": {"autoincrement": false, "name": "postal_code", "type": "int", "primaryKey": false, "notNull": false}, "city": {"autoincrement": false, "name": "city", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "state": {"autoincrement": false, "name": "state", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "location": {"autoincrement": false, "name": "location", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "long": {"autoincrement": false, "name": "long", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "lat": {"autoincrement": false, "name": "lat", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "industry_id": {"autoincrement": false, "name": "industry_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "size": {"autoincrement": false, "name": "size", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "overview": {"autoincrement": false, "name": "overview", "type": "text", "primaryKey": false, "notNull": false}, "highlight": {"autoincrement": false, "name": "highlight", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "logo": {"autoincrement": false, "name": "logo", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_generated_logo": {"default": 0, "autoincrement": false, "name": "is_generated_logo", "type": "tinyint", "primaryKey": false, "notNull": true}, "background_image": {"autoincrement": false, "name": "background_image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "pic_email": {"autoincrement": false, "name": "pic_email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "pic_phone": {"autoincrement": false, "name": "pic_phone", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "allowances": {"autoincrement": false, "name": "allowances", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "benefits": {"autoincrement": false, "name": "benefits", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "annual_leave": {"autoincrement": false, "name": "annual_leave", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "bonus": {"autoincrement": false, "name": "bonus", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "culture_text": {"autoincrement": false, "name": "culture_text", "type": "text", "primaryKey": false, "notNull": false}, "culture_video_url": {"autoincrement": false, "name": "culture_video_url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "culture_photo": {"autoincrement": false, "name": "culture_photo", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "registration_data": {"autoincrement": false, "name": "registration_data", "type": "json", "primaryKey": false, "notNull": false}, "tnc_agree": {"default": 0, "autoincrement": false, "name": "tnc_agree", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_highlight": {"default": 0, "autoincrement": false, "name": "is_highlight", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_top_employer": {"default": 0, "autoincrement": false, "name": "is_top_employer", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_main": {"autoincrement": false, "name": "is_main", "type": "tinyint", "primaryKey": false, "notNull": true}, "scoring_data": {"autoincrement": false, "name": "scoring_data", "type": "json", "primaryKey": false, "notNull": false}, "score": {"default": "'0.00'", "autoincrement": false, "name": "score", "type": "decimal(8,2)", "primaryKey": false, "notNull": true}, "is_srs_premium": {"default": 0, "autoincrement": false, "name": "is_srs_premium", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "url": {"autoincrement": false, "name": "url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "video_url": {"autoincrement": false, "name": "video_url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "url_path": {"autoincrement": false, "name": "url_path", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "corpcare_id": {"autoincrement": false, "name": "corpcare_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "remark_dev": {"autoincrement": false, "name": "remark_dev", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"companies_id": {"name": "companies_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"companies_corpcare_id_foreign": {"name": "companies_corpcare_id_foreign", "tableFrom": "companies", "tableTo": "users", "columnsFrom": ["corpcare_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "companies_industry_id_foreign": {"name": "companies_industry_id_foreign", "tableFrom": "companies", "tableTo": "industries", "columnsFrom": ["industry_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {"companies_slug_unique": {"name": "companies_slug_unique", "columns": ["slug"]}}}, "company_attachment": {"name": "company_attachment", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "attachment": {"autoincrement": false, "name": "attachment", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_attachment_id": {"name": "company_attachment_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"company_attachment_company_id_foreign": {"name": "company_attachment_company_id_foreign", "tableFrom": "company_attachment", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "company_benefit": {"name": "company_benefit", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "heading": {"autoincrement": false, "name": "heading", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "icon": {"autoincrement": false, "name": "icon", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "text_color": {"default": "'#000000'", "autoincrement": false, "name": "text_color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "background_color": {"default": "'#F1F5F8'", "autoincrement": false, "name": "background_color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "order_no": {"autoincrement": false, "name": "order_no", "type": "smallint", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_benefit_id": {"name": "company_benefit_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"company_benefit_company_id_foreign": {"name": "company_benefit_company_id_foreign", "tableFrom": "company_benefit", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "company_competitor": {"name": "company_competitor", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "link": {"autoincrement": false, "name": "link", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_competitor_id": {"name": "company_competitor_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"company_competitor_company_id_foreign": {"name": "company_competitor_company_id_foreign", "tableFrom": "company_competitor", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "company_image": {"name": "company_image", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "enum('gallery','product')", "primaryKey": false, "notNull": false}, "filename": {"autoincrement": false, "name": "filename", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "caption": {"autoincrement": false, "name": "caption", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "priority": {"autoincrement": false, "name": "priority", "type": "int", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_image_id": {"name": "company_image_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"company_image_company_id_foreign": {"name": "company_image_company_id_foreign", "tableFrom": "company_image", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "company_interview_setting_jobs": {"name": "company_interview_setting_jobs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "interview_setting_id": {"autoincrement": false, "name": "interview_setting_id", "type": "int", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_interview_setting_jobs_id": {"name": "company_interview_setting_jobs_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "company_interview_settings": {"name": "company_interview_settings", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int", "primaryKey": false, "notNull": true}, "is_general": {"autoincrement": false, "name": "is_general", "type": "tinyint", "primaryKey": false, "notNull": true}, "location": {"autoincrement": false, "name": "location", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "pic_name": {"autoincrement": false, "name": "pic_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "pic_phone": {"autoincrement": false, "name": "pic_phone", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "pic_position": {"autoincrement": false, "name": "pic_position", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "default_remark": {"autoincrement": false, "name": "default_remark", "type": "text", "primaryKey": false, "notNull": false}, "status": {"default": 0, "autoincrement": false, "name": "status", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_interview_settings_id": {"name": "company_interview_settings_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "company_story": {"name": "company_story", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "heading": {"autoincrement": false, "name": "heading", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": true}, "type": {"default": "'text'", "autoincrement": false, "name": "type", "type": "enum('text','video','image')", "primaryKey": false, "notNull": true}, "background_color": {"default": "'#F1F5F8'", "autoincrement": false, "name": "background_color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "order_no": {"autoincrement": false, "name": "order_no", "type": "smallint", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_story_id": {"name": "company_story_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"company_story_company_id_foreign": {"name": "company_story_company_id_foreign", "tableFrom": "company_story", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "company_timeslots": {"name": "company_timeslots", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "interview_setting_id": {"autoincrement": false, "name": "interview_setting_id", "type": "int", "primaryKey": false, "notNull": true}, "interview_id": {"autoincrement": false, "name": "interview_id", "type": "int", "primaryKey": false, "notNull": false}, "date": {"autoincrement": false, "name": "date", "type": "date", "primaryKey": false, "notNull": true}, "time": {"autoincrement": false, "name": "time", "type": "time", "primaryKey": false, "notNull": true}, "availability": {"default": 1, "autoincrement": false, "name": "availability", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"company_timeslots_id": {"name": "company_timeslots_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "coupons": {"name": "coupons", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int", "primaryKey": false, "notNull": false}, "code": {"autoincrement": false, "name": "code", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "fixed_discount": {"autoincrement": false, "name": "fixed_discount", "type": "int", "primaryKey": false, "notNull": false}, "percentage_discount": {"autoincrement": false, "name": "percentage_discount", "type": "int", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "enum('draft','active','disable','expired','used')", "primaryKey": false, "notNull": false}, "expiry_date": {"autoincrement": false, "name": "expiry_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "extra_data": {"autoincrement": false, "name": "extra_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"coupons_id": {"name": "coupons_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "enquiries": {"name": "enquiries", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "subject": {"autoincrement": false, "name": "subject", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "category": {"default": "'General'", "autoincrement": false, "name": "category", "type": "enum('Feedback','Bug','General','Employer')", "primaryKey": false, "notNull": false}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": true}, "sender_name": {"autoincrement": false, "name": "sender_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "sender_email": {"autoincrement": false, "name": "sender_email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "company": {"autoincrement": false, "name": "company", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "phone": {"autoincrement": false, "name": "phone", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"enquiries_id": {"name": "enquiries_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "events": {"name": "events", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "alt_name": {"autoincrement": false, "name": "alt_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "code": {"autoincrement": false, "name": "code", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "amount_default": {"autoincrement": false, "name": "amount_default", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "point_default": {"autoincrement": false, "name": "point_default", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "category": {"autoincrement": false, "name": "category", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "color": {"autoincrement": false, "name": "color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "icon": {"autoincrement": false, "name": "icon", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "priority": {"autoincrement": false, "name": "priority", "type": "int", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"events_id": {"name": "events_id", "columns": ["id"]}}, "indexes": {"events_name_index": {"name": "events_name_index", "columns": ["name"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {"events_code_unique": {"name": "events_code_unique", "columns": ["code"]}}}, "failed_jobs": {"name": "failed_jobs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "connection": {"autoincrement": false, "name": "connection", "type": "text", "primaryKey": false, "notNull": true}, "queue": {"autoincrement": false, "name": "queue", "type": "text", "primaryKey": false, "notNull": true}, "payload": {"autoincrement": false, "name": "payload", "type": "longtext", "primaryKey": false, "notNull": true}, "exception": {"autoincrement": false, "name": "exception", "type": "longtext", "primaryKey": false, "notNull": true}, "failed_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "failed_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"failed_jobs_id": {"name": "failed_jobs_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "faqs": {"name": "faqs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "category": {"autoincrement": false, "name": "category", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "topic": {"autoincrement": false, "name": "topic", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "question": {"autoincrement": false, "name": "question", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "answer": {"autoincrement": false, "name": "answer", "type": "text", "primaryKey": false, "notNull": true}, "status": {"autoincrement": false, "name": "status", "type": "enum('draft','open')", "primaryKey": false, "notNull": true}, "sort_order": {"autoincrement": false, "name": "sort_order", "type": "int", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"faqs_id": {"name": "faqs_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "getresponse_list": {"name": "getresponse_list", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "campaign_id": {"autoincrement": false, "name": "campaign_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "source": {"autoincrement": false, "name": "source", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"getresponse_list_id": {"name": "getresponse_list_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "industries": {"name": "industries", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "parent_id": {"autoincrement": false, "name": "parent_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "color": {"autoincrement": false, "name": "color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "icon": {"autoincrement": false, "name": "icon", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image_path": {"autoincrement": false, "name": "image_path", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "slug": {"autoincrement": false, "name": "slug", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "priority": {"autoincrement": false, "name": "priority", "type": "int unsigned", "primaryKey": false, "notNull": false}, "sort_order": {"autoincrement": false, "name": "sort_order", "type": "int unsigned", "primaryKey": false, "notNull": false}, "is_parent": {"default": 0, "autoincrement": false, "name": "is_parent", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "seo_title": {"autoincrement": false, "name": "seo_title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "seo_description": {"autoincrement": false, "name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "seo_image": {"autoincrement": false, "name": "seo_image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"industries_id": {"name": "industries_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {"slug": {"name": "slug", "columns": ["slug"]}, "industries_slug_unique": {"name": "industries_slug_unique", "columns": ["slug"]}}}, "job_condition": {"name": "job_condition", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "working_days": {"autoincrement": false, "name": "working_days", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "working_hour": {"autoincrement": false, "name": "working_hour", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_shift_work": {"default": 0, "autoincrement": false, "name": "is_shift_work", "type": "tinyint", "primaryKey": false, "notNull": true}, "allowances": {"autoincrement": false, "name": "allowances", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "bonus": {"autoincrement": false, "name": "bonus", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "annual_leave": {"autoincrement": false, "name": "annual_leave", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "holiday": {"autoincrement": false, "name": "holiday", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "probation": {"autoincrement": false, "name": "probation", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "benefits": {"autoincrement": false, "name": "benefits", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "type": {"autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "remarks": {"autoincrement": false, "name": "remarks", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_condition_id": {"name": "job_condition_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_condition_job_id_foreign": {"name": "job_condition_job_id_foreign", "tableFrom": "job_condition", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "job_customs": {"name": "job_customs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "slug": {"autoincrement": false, "name": "slug", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "status": {"default": "'show'", "autoincrement": false, "name": "status", "type": "enum('show','hide')", "primaryKey": false, "notNull": true}, "remark": {"autoincrement": false, "name": "remark", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_customs_id": {"name": "job_customs_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "job_lists": {"name": "job_lists", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_custom_id": {"autoincrement": false, "name": "job_custom_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_lists_id": {"name": "job_lists_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_lists_job_custom_id_foreign": {"name": "job_lists_job_custom_id_foreign", "tableFrom": "job_lists", "tableTo": "job_customs", "columnsFrom": ["job_custom_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "job_lists_job_id_foreign": {"name": "job_lists_job_id_foreign", "tableFrom": "job_lists", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "job_requirement": {"name": "job_requirement", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "age_min": {"default": 18, "autoincrement": false, "name": "age_min", "type": "int", "primaryKey": false, "notNull": true}, "age_max": {"autoincrement": false, "name": "age_max", "type": "int", "primaryKey": false, "notNull": false}, "gender": {"autoincrement": false, "name": "gender", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "race": {"autoincrement": false, "name": "race", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "language": {"autoincrement": false, "name": "language", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_local_only": {"autoincrement": false, "name": "is_local_only", "type": "tinyint", "primaryKey": false, "notNull": false}, "own_transport": {"autoincrement": false, "name": "own_transport", "type": "tinyint", "primaryKey": false, "notNull": false}, "own_driver_license": {"autoincrement": false, "name": "own_driver_license", "type": "tinyint", "primaryKey": false, "notNull": false}, "willing_to_travel": {"autoincrement": false, "name": "willing_to_travel", "type": "tinyint", "primaryKey": false, "notNull": false}, "got_work_permit": {"autoincrement": false, "name": "got_work_permit", "type": "tinyint", "primaryKey": false, "notNull": false}, "got_visa": {"autoincrement": false, "name": "got_visa", "type": "tinyint", "primaryKey": false, "notNull": false}, "got_portfolio": {"autoincrement": false, "name": "got_portfolio", "type": "tinyint", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_requirement_id": {"name": "job_requirement_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_requirement_job_id_foreign": {"name": "job_requirement_job_id_foreign", "tableFrom": "job_requirement", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "job_revenue": {"name": "job_revenue", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "amount": {"autoincrement": false, "name": "amount", "type": "int", "primaryKey": false, "notNull": true}, "status": {"autoincrement": false, "name": "status", "type": "enum('expected','actual')", "primaryKey": false, "notNull": true}, "date": {"autoincrement": false, "name": "date", "type": "datetime", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_revenue_id": {"name": "job_revenue_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_revenue_job_id_foreign": {"name": "job_revenue_job_id_foreign", "tableFrom": "job_revenue", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "job_screening_questions": {"name": "job_screening_questions", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "question": {"autoincrement": false, "name": "question", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_screening_questions_id": {"name": "job_screening_questions_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_screening_questions_job_id_foreign": {"name": "job_screening_questions_job_id_foreign", "tableFrom": "job_screening_questions", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "job_update": {"name": "job_update", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "date_at": {"autoincrement": false, "name": "date_at", "type": "date", "primaryKey": false, "notNull": true}, "note": {"autoincrement": false, "name": "note", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "tag": {"autoincrement": false, "name": "tag", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "tag_color": {"autoincrement": false, "name": "tag_color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_update_id": {"name": "job_update_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_update_job_id_foreign": {"name": "job_update_job_id_foreign", "tableFrom": "job_update", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "job_visibility": {"name": "job_visibility", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "show": {"autoincrement": false, "name": "show", "type": "tinyint", "primaryKey": false, "notNull": true}, "hide": {"autoincrement": false, "name": "hide", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"job_visibility_id": {"name": "job_visibility_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"job_visibility_job_id_foreign": {"name": "job_visibility_job_id_foreign", "tableFrom": "job_visibility", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "job_visibility_user_id_foreign": {"name": "job_visibility_user_id_foreign", "tableFrom": "job_visibility", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "jobs": {"name": "jobs", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "package": {"default": "'Premium'", "autoincrement": false, "name": "package", "type": "enum('Premium','Pay-Per-Post','Free-Posting')", "primaryKey": false, "notNull": false}, "expiry_date": {"autoincrement": false, "name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "enum('open','temporarily_closed','closed','draft')", "primaryKey": false, "notNull": false}, "slug": {"default": "''", "autoincrement": false, "name": "slug", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "url_path_role": {"autoincrement": false, "name": "url_path_role", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_different_location": {"default": 0, "autoincrement": false, "name": "is_different_location", "type": "tinyint", "primaryKey": false, "notNull": false}, "city": {"autoincrement": false, "name": "city", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "title": {"default": "''", "autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": true}, "close_reason": {"autoincrement": false, "name": "close_reason", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "temp_close_reason": {"autoincrement": false, "name": "temp_close_reason", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "industry_id": {"autoincrement": false, "name": "industry_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "company_id": {"autoincrement": false, "name": "company_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "company_pic_name": {"autoincrement": false, "name": "company_pic_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "company_pic_email": {"autoincrement": false, "name": "company_pic_email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "company_pic_phone": {"autoincrement": false, "name": "company_pic_phone", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "sale_id": {"autoincrement": false, "name": "sale_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "consultant_id": {"autoincrement": false, "name": "consultant_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "is_highlight": {"default": 0, "autoincrement": false, "name": "is_highlight", "type": "tinyint", "primaryKey": false, "notNull": true}, "role_id": {"autoincrement": false, "name": "role_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "level": {"autoincrement": false, "name": "level", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "location": {"autoincrement": false, "name": "location", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "location_remark": {"autoincrement": false, "name": "location_remark", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "address": {"autoincrement": false, "name": "address", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "building": {"autoincrement": false, "name": "building", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "state": {"autoincrement": false, "name": "state", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "postal_code": {"autoincrement": false, "name": "postal_code", "type": "int", "primaryKey": false, "notNull": false}, "long": {"autoincrement": false, "name": "long", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "lat": {"autoincrement": false, "name": "lat", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "responsibility": {"autoincrement": false, "name": "responsibility", "type": "text", "primaryKey": false, "notNull": true}, "interview_method": {"autoincrement": false, "name": "interview_method", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "req_must_have": {"autoincrement": false, "name": "req_must_have", "type": "text", "primaryKey": false, "notNull": false}, "req_others": {"autoincrement": false, "name": "req_others", "type": "text", "primaryKey": false, "notNull": false}, "experience_min_years": {"autoincrement": false, "name": "experience_min_years", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "experience_field": {"autoincrement": false, "name": "experience_field", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "screening_question": {"autoincrement": false, "name": "screening_question", "type": "text", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_processing": {"default": 0, "autoincrement": false, "name": "is_processing", "type": "tinyint", "primaryKey": false, "notNull": true}, "remark": {"autoincrement": false, "name": "remark", "type": "text", "primaryKey": false, "notNull": false}, "corporate_remark": {"autoincrement": false, "name": "corporate_remark", "type": "text", "primaryKey": false, "notNull": false}, "feature": {"autoincrement": false, "name": "feature", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "accept_fresh": {"default": 0, "autoincrement": false, "name": "accept_fresh", "type": "tinyint", "primaryKey": false, "notNull": true}, "accept_foreigner": {"default": 0, "autoincrement": false, "name": "accept_foreigner", "type": "tinyint", "primaryKey": false, "notNull": true}, "salary_min": {"autoincrement": false, "name": "salary_min", "type": "int", "primaryKey": false, "notNull": true}, "salary_max": {"autoincrement": false, "name": "salary_max", "type": "int", "primaryKey": false, "notNull": true}, "reward_min": {"autoincrement": false, "name": "reward_min", "type": "int", "primaryKey": false, "notNull": false}, "reward_max": {"autoincrement": false, "name": "reward_max", "type": "int", "primaryKey": false, "notNull": false}, "sent_to_client_reward": {"autoincrement": false, "name": "sent_to_client_reward", "type": "int", "primaryKey": false, "notNull": false}, "sent_to_client_limit": {"autoincrement": false, "name": "sent_to_client_limit", "type": "int", "primaryKey": false, "notNull": false}, "shortlist_reward": {"autoincrement": false, "name": "shortlist_reward", "type": "int", "primaryKey": false, "notNull": false}, "shortlist_limit": {"autoincrement": false, "name": "shortlist_limit", "type": "int", "primaryKey": false, "notNull": true}, "is_reward_boost": {"default": 0, "autoincrement": false, "name": "is_reward_boost", "type": "tinyint", "primaryKey": false, "notNull": true}, "boost_status": {"autoincrement": false, "name": "boost_status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "amendment_note": {"autoincrement": false, "name": "amendment_note", "type": "text", "primaryKey": false, "notNull": false}, "need_verification": {"default": 0, "autoincrement": false, "name": "need_verification", "type": "tinyint", "primaryKey": false, "notNull": true}, "video_url": {"autoincrement": false, "name": "video_url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "min_matching_rate": {"default": 20, "autoincrement": false, "name": "min_matching_rate", "type": "int unsigned", "primaryKey": false, "notNull": true}, "indeed_data": {"autoincrement": false, "name": "indeed_data", "type": "json", "primaryKey": false, "notNull": false}, "scoring_data": {"autoincrement": false, "name": "scoring_data", "type": "json", "primaryKey": false, "notNull": false}, "score": {"autoincrement": false, "name": "score", "type": "decimal(12,8)", "primaryKey": false, "notNull": false}, "extra_data": {"autoincrement": false, "name": "extra_data", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revenue_model": {"autoincrement": false, "name": "revenue_model", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "url_path_industry": {"autoincrement": false, "name": "url_path_industry", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "hide_from": {"autoincrement": false, "name": "hide_from", "type": "json", "primaryKey": false, "notNull": false}, "job_visibility_id": {"autoincrement": false, "name": "job_visibility_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "hide_from_all_recruiter": {"default": 0, "autoincrement": false, "name": "hide_from_all_recruiter", "type": "tinyint", "primaryKey": false, "notNull": false}, "is_staff_pick": {"default": 0, "autoincrement": false, "name": "is_staff_pick", "type": "tinyint", "primaryKey": false, "notNull": false}, "show_to_recruiter_group": {"default": "'A,B'", "autoincrement": false, "name": "show_to_recruiter_group", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "publish_to_recruiters": {"default": 0, "autoincrement": false, "name": "publish_to_recruiters", "type": "tinyint", "primaryKey": false, "notNull": false}, "is_remote": {"default": 0, "autoincrement": false, "name": "is_remote", "type": "tinyint", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"jobs_id": {"name": "jobs_id", "columns": ["id"]}}, "indexes": {"idx_slug": {"name": "idx_slug", "columns": ["slug"], "isUnique": false}, "idx_status": {"name": "idx_status", "columns": ["status"], "isUnique": false}, "idx_package": {"name": "idx_package", "columns": ["package"], "isUnique": false}}, "foreignKeys": {"jobs_company_id_foreign": {"name": "jobs_company_id_foreign", "tableFrom": "jobs", "tableTo": "companies", "columnsFrom": ["company_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "jobs_consultant_id_foreign": {"name": "jobs_consultant_id_foreign", "tableFrom": "jobs", "tableTo": "users", "columnsFrom": ["consultant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "jobs_industry_id_foreign": {"name": "jobs_industry_id_foreign", "tableFrom": "jobs", "tableTo": "industries", "columnsFrom": ["industry_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "jobs_job_visibility_id_foreign": {"name": "jobs_job_visibility_id_foreign", "tableFrom": "jobs", "tableTo": "job_visibility", "columnsFrom": ["job_visibility_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "jobs_role_id_foreign": {"name": "jobs_role_id_foreign", "tableFrom": "jobs", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "jobs_sale_id_foreign": {"name": "jobs_sale_id_foreign", "tableFrom": "jobs", "tableTo": "users", "columnsFrom": ["sale_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "jobs_queue": {"name": "jobs_queue", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "queue": {"autoincrement": false, "name": "queue", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "payload": {"autoincrement": false, "name": "payload", "type": "longtext", "primaryKey": false, "notNull": true}, "attempts": {"autoincrement": false, "name": "attempts", "type": "tinyint", "primaryKey": false, "notNull": true}, "reserved_at": {"autoincrement": false, "name": "reserved_at", "type": "int unsigned", "primaryKey": false, "notNull": false}, "available_at": {"autoincrement": false, "name": "available_at", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "int unsigned", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"jobs_queue_id": {"name": "jobs_queue_id", "columns": ["id"]}}, "indexes": {"jobs_queue_queue_index": {"name": "jobs_queue_queue_index", "columns": ["queue"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "master_lookups": {"name": "master_lookups", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "value": {"autoincrement": false, "name": "value", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "json_data": {"autoincrement": false, "name": "json_data", "type": "json", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"master_lookups_id": {"name": "master_lookups_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "migrate_attachments": {"name": "migrate_attachments", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "disk_name": {"autoincrement": false, "name": "disk_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_name": {"autoincrement": false, "name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_size": {"autoincrement": false, "name": "file_size", "type": "int", "primaryKey": false, "notNull": true}, "content_type": {"autoincrement": false, "name": "content_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "field": {"autoincrement": false, "name": "field", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "attachment_id": {"autoincrement": false, "name": "attachment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "attachment_type": {"autoincrement": false, "name": "attachment_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_public": {"default": 1, "autoincrement": false, "name": "is_public", "type": "tinyint", "primaryKey": false, "notNull": true}, "sort_order": {"autoincrement": false, "name": "sort_order", "type": "int", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"migrate_attachments_id": {"name": "migrate_attachments_id", "columns": ["id"]}}, "indexes": {"system_files_field_index": {"name": "system_files_field_index", "columns": ["field"], "isUnique": false}, "system_files_attachment_id_index": {"name": "system_files_attachment_id_index", "columns": ["attachment_id"], "isUnique": false}, "system_files_attachment_type_index": {"name": "system_files_attachment_type_index", "columns": ["attachment_type"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "migrate_candidates": {"name": "migrate_candidates", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int", "primaryKey": false, "notNull": true}, "referrer_id": {"autoincrement": false, "name": "referrer_id", "type": "int", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "mobile": {"autoincrement": false, "name": "mobile", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "gender": {"autoincrement": false, "name": "gender", "type": "enum('male','female')", "primaryKey": false, "notNull": false}, "spoken_language": {"autoincrement": false, "name": "spoken_language", "type": "text", "primaryKey": false, "notNull": false}, "citizenship": {"autoincrement": false, "name": "citizenship", "type": "enum('malaysian','with permit','none')", "primaryKey": false, "notNull": false}, "current_industry": {"autoincrement": false, "name": "current_industry", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "current_position": {"autoincrement": false, "name": "current_position", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "current_salary": {"autoincrement": false, "name": "current_salary", "type": "decimal(10,2)", "primaryKey": false, "notNull": false}, "expected_salary": {"autoincrement": false, "name": "expected_salary", "type": "decimal(10,2)", "primaryKey": false, "notNull": false}, "offered_salary": {"default": "'0.00'", "autoincrement": false, "name": "offered_salary", "type": "decimal(10,2)", "primaryKey": false, "notNull": true}, "notice_period": {"autoincrement": false, "name": "notice_period", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "birth_year": {"autoincrement": false, "name": "birth_year", "type": "year", "primaryKey": false, "notNull": false}, "linkedin": {"autoincrement": false, "name": "linkedin", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "reward": {"autoincrement": false, "name": "reward", "type": "decimal(10,2)", "primaryKey": false, "notNull": false}, "paid_amount": {"default": "'0.00'", "autoincrement": false, "name": "paid_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false}, "sent_status": {"autoincrement": false, "name": "sent_status", "type": "tinyint", "primaryKey": false, "notNull": false}, "interview_date": {"autoincrement": false, "name": "interview_date", "type": "date", "primaryKey": false, "notNull": false}, "interview_time": {"autoincrement": false, "name": "interview_time", "type": "time", "primaryKey": false, "notNull": false}, "interview_venue": {"autoincrement": false, "name": "interview_venue", "type": "text", "primaryKey": false, "notNull": false}, "interviewer_name": {"autoincrement": false, "name": "interviewer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "interview_status": {"default": 0, "autoincrement": false, "name": "interview_status", "type": "tinyint", "primaryKey": false, "notNull": false}, "commencement_date": {"autoincrement": false, "name": "commencement_date", "type": "datetime", "primaryKey": false, "notNull": false}, "commencement_30_date": {"autoincrement": false, "name": "commencement_30_date", "type": "datetime", "primaryKey": false, "notNull": false}, "remark": {"autoincrement": false, "name": "remark", "type": "text", "primaryKey": false, "notNull": false}, "offer_status": {"autoincrement": false, "name": "offer_status", "type": "tinyint", "primaryKey": false, "notNull": false}, "note": {"autoincrement": false, "name": "note", "type": "text", "primaryKey": false, "notNull": false}, "screening_question": {"autoincrement": false, "name": "screening_question", "type": "text", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "tinyint", "primaryKey": false, "notNull": true}, "blacklist": {"autoincrement": false, "name": "blacklist", "type": "tinyint", "primaryKey": false, "notNull": false}, "ip_address": {"autoincrement": false, "name": "ip_address", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "created": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "created", "type": "datetime", "primaryKey": false, "notNull": true}, "updated_by": {"default": 0, "autoincrement": false, "name": "updated_by", "type": "int", "primaryKey": false, "notNull": false}, "updated": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "updated", "type": "datetime", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"migrate_candidates_id": {"name": "migrate_candidates_id", "columns": ["id"]}}, "indexes": {"applications_job_id_index": {"name": "applications_job_id_index", "columns": ["job_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "migrate_users": {"name": "migrate_users", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contact_no": {"autoincrement": false, "name": "contact_no", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": true}, "current_specialization": {"autoincrement": false, "name": "current_specialization", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "recruiting_experience": {"autoincrement": false, "name": "recruiting_experience", "type": "int", "primaryKey": false, "notNull": false}, "password": {"autoincrement": false, "name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "activation_code": {"autoincrement": false, "name": "activation_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "persist_code": {"autoincrement": false, "name": "persist_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "reset_password_code": {"autoincrement": false, "name": "reset_password_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "permissions": {"autoincrement": false, "name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "is_activated": {"default": 0, "autoincrement": false, "name": "is_activated", "type": "tinyint", "primaryKey": false, "notNull": true}, "activated_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "activated_at", "type": "datetime", "primaryKey": false, "notNull": false}, "last_login": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "last_login", "type": "datetime", "primaryKey": false, "notNull": false}, "created_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false}, "updated_at": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false}, "username": {"autoincrement": false, "name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "surname": {"autoincrement": false, "name": "surname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_seen": {"default": "CURRENT_TIMESTAMP", "autoincrement": false, "name": "last_seen", "type": "datetime", "primaryKey": false, "notNull": false}, "session_id": {"autoincrement": false, "name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "affiliate_id": {"autoincrement": false, "name": "affiliate_id", "type": "int", "primaryKey": false, "notNull": false}, "is_affiliate": {"autoincrement": false, "name": "is_affiliate", "type": "tinyint", "primaryKey": false, "notNull": false}, "subscribe": {"default": 1, "autoincrement": false, "name": "subscribe", "type": "tinyint", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"migrate_users_id": {"name": "migrate_users_id", "columns": ["id"]}}, "indexes": {"users_activation_code_index": {"name": "users_activation_code_index", "columns": ["activation_code"], "isUnique": false}, "users_reset_password_code_index": {"name": "users_reset_password_code_index", "columns": ["reset_password_code"], "isUnique": false}, "users_login_index": {"name": "users_login_index", "columns": ["username"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"]}, "users_login_unique": {"name": "users_login_unique", "columns": ["username"]}}}, "migrations": {"name": "migrations", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "migration": {"autoincrement": false, "name": "migration", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "batch": {"autoincrement": false, "name": "batch", "type": "int", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"migrations_id": {"name": "migrations_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "milestone_events": {"name": "milestone_events", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "milestone_id": {"autoincrement": false, "name": "milestone_id", "type": "int", "primaryKey": false, "notNull": true}, "event_id": {"autoincrement": false, "name": "event_id", "type": "int", "primaryKey": false, "notNull": true}, "count_method": {"default": "'count_activity'", "autoincrement": false, "name": "count_method", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "min_count": {"default": 1, "autoincrement": false, "name": "min_count", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"milestone_events_id": {"name": "milestone_events_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "milestones": {"name": "milestones", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "rank_id": {"autoincrement": false, "name": "rank_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "next_rank_id": {"autoincrement": false, "name": "next_rank_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "completion_reward": {"autoincrement": false, "name": "completion_reward", "type": "int", "primaryKey": false, "notNull": true}, "order": {"autoincrement": false, "name": "order", "type": "int", "primaryKey": false, "notNull": false}, "logo": {"autoincrement": false, "name": "logo", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"milestones_id": {"name": "milestones_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"milestones_next_rank_id_foreign": {"name": "milestones_next_rank_id_foreign", "tableFrom": "milestones", "tableTo": "events", "columnsFrom": ["next_rank_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "milestones_rank_id_foreign": {"name": "milestones_rank_id_foreign", "tableFrom": "milestones", "tableTo": "events", "columnsFrom": ["rank_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "nc_evolutions": {"name": "nc_evolutions", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "titleDown": {"autoincrement": false, "name": "titleDown", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "batch": {"autoincrement": false, "name": "batch", "type": "int", "primaryKey": false, "notNull": false}, "checksum": {"autoincrement": false, "name": "checksum", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "int", "primaryKey": false, "notNull": false}, "created": {"autoincrement": false, "name": "created", "type": "datetime", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"nc_evolutions_id": {"name": "nc_evolutions_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "news": {"name": "news", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "slug": {"autoincrement": false, "name": "slug", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": false}, "upload": {"autoincrement": false, "name": "upload", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "type": {"default": "'both'", "autoincrement": false, "name": "type", "type": "enum('app','desktop','both')", "primaryKey": false, "notNull": true}, "is_featured": {"autoincrement": false, "name": "is_featured", "type": "tinyint", "primaryKey": false, "notNull": false}, "category": {"default": "'general'", "autoincrement": false, "name": "category", "type": "enum('promo','general','update')", "primaryKey": false, "notNull": true}, "active": {"default": 1, "autoincrement": false, "name": "active", "type": "tinyint", "primaryKey": false, "notNull": true}, "push_notification": {"default": 0, "autoincrement": false, "name": "push_notification", "type": "tinyint", "primaryKey": false, "notNull": true}, "segment": {"autoincrement": false, "name": "segment", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "module": {"default": "'recruiter'", "autoincrement": false, "name": "module", "type": "enum('jobfinder','recruiter','employer')", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"news_id": {"name": "news_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "notifications": {"name": "notifications", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "heading": {"autoincrement": false, "name": "heading", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "subheading": {"autoincrement": false, "name": "subheading", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "content": {"autoincrement": false, "name": "content", "type": "text", "primaryKey": false, "notNull": true}, "type": {"default": "'news'", "autoincrement": false, "name": "type", "type": "enum('news','campaign','update','activity')", "primaryKey": false, "notNull": true}, "module": {"default": "'recruiter'", "autoincrement": false, "name": "module", "type": "enum('recruiter','jobfinder','employer')", "primaryKey": false, "notNull": false}, "url": {"autoincrement": false, "name": "url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "segments": {"autoincrement": false, "name": "segments", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "player_ids": {"autoincrement": false, "name": "player_ids", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_by_activity": {"default": 0, "autoincrement": false, "name": "created_by_activity", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"notifications_id": {"name": "notifications_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "oauth_access_tokens": {"name": "oauth_access_tokens", "columns": {"id": {"autoincrement": false, "name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int", "primaryKey": false, "notNull": false}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "scopes": {"autoincrement": false, "name": "scopes", "type": "text", "primaryKey": false, "notNull": false}, "revoked": {"autoincrement": false, "name": "revoked", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"autoincrement": false, "name": "expires_at", "type": "datetime", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"oauth_access_tokens_id": {"name": "oauth_access_tokens_id", "columns": ["id"]}}, "indexes": {"oauth_access_tokens_user_id_index": {"name": "oauth_access_tokens_user_id_index", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "oauth_auth_codes": {"name": "oauth_auth_codes", "columns": {"id": {"autoincrement": false, "name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int", "primaryKey": false, "notNull": true}, "scopes": {"autoincrement": false, "name": "scopes", "type": "text", "primaryKey": false, "notNull": false}, "revoked": {"autoincrement": false, "name": "revoked", "type": "tinyint", "primaryKey": false, "notNull": true}, "expires_at": {"autoincrement": false, "name": "expires_at", "type": "datetime", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"oauth_auth_codes_id": {"name": "oauth_auth_codes_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "oauth_clients": {"name": "oauth_clients", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int", "primaryKey": false, "notNull": false}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "secret": {"autoincrement": false, "name": "secret", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "redirect": {"autoincrement": false, "name": "redirect", "type": "text", "primaryKey": false, "notNull": true}, "personal_access_client": {"autoincrement": false, "name": "personal_access_client", "type": "tinyint", "primaryKey": false, "notNull": true}, "password_client": {"autoincrement": false, "name": "password_client", "type": "tinyint", "primaryKey": false, "notNull": true}, "revoked": {"autoincrement": false, "name": "revoked", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"oauth_clients_id": {"name": "oauth_clients_id", "columns": ["id"]}}, "indexes": {"oauth_clients_user_id_index": {"name": "oauth_clients_user_id_index", "columns": ["user_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "oauth_personal_access_clients": {"name": "oauth_personal_access_clients", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"oauth_personal_access_clients_id": {"name": "oauth_personal_access_clients_id", "columns": ["id"]}}, "indexes": {"oauth_personal_access_clients_client_id_index": {"name": "oauth_personal_access_clients_client_id_index", "columns": ["client_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "oauth_refresh_tokens": {"name": "oauth_refresh_tokens", "columns": {"id": {"autoincrement": false, "name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "access_token_id": {"autoincrement": false, "name": "access_token_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "revoked": {"autoincrement": false, "name": "revoked", "type": "tinyint", "primaryKey": false, "notNull": true}, "expires_at": {"autoincrement": false, "name": "expires_at", "type": "datetime", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"oauth_refresh_tokens_id": {"name": "oauth_refresh_tokens_id", "columns": ["id"]}}, "indexes": {"oauth_refresh_tokens_access_token_id_index": {"name": "oauth_refresh_tokens_access_token_id_index", "columns": ["access_token_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "password_resets": {"name": "password_resets", "columns": {"email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "token": {"autoincrement": false, "name": "token", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {}, "indexes": {"password_resets_email_index": {"name": "password_resets_email_index", "columns": ["email"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "popups": {"name": "popups", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "mode": {"autoincrement": false, "name": "mode", "type": "enum('internal','external')", "primaryKey": false, "notNull": true}, "page_type": {"autoincrement": false, "name": "page_type", "type": "enum('job','inbox')", "primaryKey": false, "notNull": false}, "page_id": {"autoincrement": false, "name": "page_id", "type": "int", "primaryKey": false, "notNull": false}, "external_url": {"autoincrement": false, "name": "external_url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"popups_id": {"name": "popups_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "products": {"name": "products", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "code": {"autoincrement": false, "name": "code", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "description": {"autoincrement": false, "name": "description", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "status": {"autoincrement": false, "name": "status", "type": "enum('draft','active','disable')", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "enum('package','addon')", "primaryKey": false, "notNull": true}, "remark": {"autoincrement": false, "name": "remark", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "price": {"autoincrement": false, "name": "price", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"products_id": {"name": "products_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "questions": {"name": "questions", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "industry_id": {"autoincrement": false, "name": "industry_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "text": {"autoincrement": false, "name": "text", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "tags": {"autoincrement": false, "name": "tags", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_endorsement": {"default": 0, "autoincrement": false, "name": "is_endorsement", "type": "tinyint", "primaryKey": false, "notNull": true}, "priority": {"autoincrement": false, "name": "priority", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"questions_id": {"name": "questions_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "quiz_option": {"name": "quiz_option", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "quiz_id": {"autoincrement": false, "name": "quiz_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "option_no": {"autoincrement": false, "name": "option_no", "type": "int", "primaryKey": false, "notNull": true}, "text": {"autoincrement": false, "name": "text", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"quiz_option_id": {"name": "quiz_option_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"quiz_option_quiz_id_foreign": {"name": "quiz_option_quiz_id_foreign", "tableFrom": "quiz_option", "tableTo": "quizzes", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "quizzes": {"name": "quizzes", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "chapter_id": {"autoincrement": false, "name": "chapter_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "question": {"autoincrement": false, "name": "question", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "answer": {"autoincrement": false, "name": "answer", "type": "int", "primaryKey": false, "notNull": true}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "description": {"autoincrement": false, "name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"autoincrement": false, "name": "sort_order", "type": "int", "primaryKey": false, "notNull": false}, "status": {"default": "'draft'", "autoincrement": false, "name": "status", "type": "enum('draft','open','closed')", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"quizzes_id": {"name": "quizzes_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"quizzes_chapter_id_foreign": {"name": "quizzes_chapter_id_foreign", "tableFrom": "quizzes", "tableTo": "chapters", "columnsFrom": ["chapter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "recommendations": {"name": "recommendations", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int", "primaryKey": false, "notNull": true}, "type": {"default": "'jd'", "autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "data": {"autoincrement": false, "name": "data", "type": "json", "primaryKey": false, "notNull": false}, "status": {"default": "'show'", "autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "snoozed_until": {"autoincrement": false, "name": "snoozed_until", "type": "datetime", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"autoincrement": false, "name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"recommendations_id": {"name": "recommendations_id", "columns": ["id"]}}, "indexes": {"job_id_index": {"name": "job_id_index", "columns": ["job_id"], "isUnique": false}, "type_index": {"name": "type_index", "columns": ["type"], "isUnique": false}, "status_index": {"name": "status_index", "columns": ["status"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "roles": {"name": "roles", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "parent_id": {"autoincrement": false, "name": "parent_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "slug": {"autoincrement": false, "name": "slug", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_parent": {"default": 0, "autoincrement": false, "name": "is_parent", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "icon": {"autoincrement": false, "name": "icon", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "color": {"autoincrement": false, "name": "color", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "seo_title": {"autoincrement": false, "name": "seo_title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "seo_description": {"autoincrement": false, "name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "seo_image": {"autoincrement": false, "name": "seo_image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_digital": {"default": 0, "autoincrement": false, "name": "is_digital", "type": "tinyint", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"roles_id": {"name": "roles_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "scout_templates": {"name": "scout_templates", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "message": {"autoincrement": false, "name": "message", "type": "text", "primaryKey": false, "notNull": true}, "status": {"default": "'draft'", "autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"scout_templates_id": {"name": "scout_templates_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "screening_answers": {"name": "screening_answers", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "applicant_id": {"autoincrement": false, "name": "applicant_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "question_id": {"autoincrement": false, "name": "question_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "answer": {"autoincrement": false, "name": "answer", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"screening_answers_id": {"name": "screening_answers_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"screening_answers_applicant_id_foreign": {"name": "screening_answers_applicant_id_foreign", "tableFrom": "screening_answers", "tableTo": "applicants", "columnsFrom": ["applicant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "screening_answers_question_id_foreign": {"name": "screening_answers_question_id_foreign", "tableFrom": "screening_answers", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "screening_questions": {"name": "screening_questions", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "industry_id": {"autoincrement": false, "name": "industry_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "question_id": {"autoincrement": false, "name": "question_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "priority": {"autoincrement": false, "name": "priority", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"screening_questions_id": {"name": "screening_questions_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"screening_questions_job_id_foreign": {"name": "screening_questions_job_id_foreign", "tableFrom": "screening_questions", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "screening_questions_question_id_foreign": {"name": "screening_questions_question_id_foreign", "tableFrom": "screening_questions", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "sessions": {"name": "sessions", "columns": {"id": {"autoincrement": false, "name": "id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "ip_address": {"autoincrement": false, "name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"autoincrement": false, "name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "payload": {"autoincrement": false, "name": "payload", "type": "text", "primaryKey": false, "notNull": true}, "last_activity": {"autoincrement": false, "name": "last_activity", "type": "int", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {"sessions_id_unique": {"name": "sessions_id_unique", "columns": ["id"]}}}, "settings": {"name": "settings", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "type": {"autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "value": {"autoincrement": false, "name": "value", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "extra": {"autoincrement": false, "name": "extra", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"settings_id": {"name": "settings_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "site_alerts": {"name": "site_alerts", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "batch_uuid": {"autoincrement": false, "name": "batch_uuid", "type": "char(36)", "primaryKey": false, "notNull": false}, "type": {"default": "'Frontend'", "autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "level": {"default": "'error'", "autoincrement": false, "name": "level", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "url": {"autoincrement": false, "name": "url", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "user_data": {"autoincrement": false, "name": "user_data", "type": "json", "primaryKey": false, "notNull": false}, "alert_data": {"autoincrement": false, "name": "alert_data", "type": "json", "primaryKey": false, "notNull": false}, "status": {"default": "'reported'", "autoincrement": false, "name": "status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "comments": {"autoincrement": false, "name": "comments", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"site_alerts_id": {"name": "site_alerts_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "skills": {"name": "skills", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "slug": {"autoincrement": false, "name": "slug", "type": "int", "primaryKey": false, "notNull": false}, "category": {"autoincrement": false, "name": "category", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"skills_id": {"name": "skills_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "srs_feedbacks": {"name": "srs_feedbacks", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int", "primaryKey": false, "notNull": true}, "attractive_feature": {"autoincrement": false, "name": "attractive_feature", "type": "text", "primaryKey": false, "notNull": false}, "improvement": {"autoincrement": false, "name": "improvement", "type": "text", "primaryKey": false, "notNull": false}, "rating": {"default": 1, "autoincrement": false, "name": "rating", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"srs_feedbacks_id": {"name": "srs_feedbacks_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}, "subscription_items": {"name": "subscription_items", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "subscription_id": {"autoincrement": false, "name": "subscription_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "stripe_id": {"autoincrement": false, "name": "stripe_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "stripe_plan": {"autoincrement": false, "name": "stripe_plan", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "quantity": {"autoincrement": false, "name": "quantity", "type": "int", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"subscription_items_id": {"name": "subscription_items_id", "columns": ["id"]}}, "indexes": {"subscription_items_stripe_id_index": {"name": "subscription_items_stripe_id_index", "columns": ["stripe_id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {"subscription_items_subscription_id_stripe_plan_unique": {"name": "subscription_items_subscription_id_stripe_plan_unique", "columns": ["subscription_id", "stripe_plan"]}}}, "subscriptions": {"name": "subscriptions", "columns": {"id": {"autoincrement": true, "name": "id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "client_id": {"autoincrement": false, "name": "client_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "stripe_id": {"autoincrement": false, "name": "stripe_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "stripe_status": {"autoincrement": false, "name": "stripe_status", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "stripe_plan": {"autoincrement": false, "name": "stripe_plan", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "quantity": {"autoincrement": false, "name": "quantity", "type": "int", "primaryKey": false, "notNull": false}, "trial_ends_at": {"autoincrement": false, "name": "trial_ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "ends_at": {"autoincrement": false, "name": "ends_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"subscriptions_id": {"name": "subscriptions_id", "columns": ["id"]}}, "indexes": {"subscriptions_client_id_stripe_status_index": {"name": "subscriptions_client_id_stripe_status_index", "columns": ["client_id", "stripe_status"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "taggables": {"name": "taggables", "columns": {"tag_id": {"autoincrement": false, "name": "tag_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "taggable_type": {"autoincrement": false, "name": "taggable_type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "taggable_id": {"autoincrement": false, "name": "taggable_id", "type": "bigint unsigned", "primaryKey": false, "notNull": true}, "score": {"autoincrement": false, "name": "score", "type": "double(8,2)", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {}, "indexes": {"taggables_taggable_type_taggable_id_index": {"name": "taggables_taggable_type_taggable_id_index", "columns": ["taggable_type", "taggable_id"], "isUnique": false}, "idx_taggable_id": {"name": "idx_taggable_id", "columns": ["taggable_id"], "isUnique": false}}, "foreignKeys": {"taggables_tag_id_foreign": {"name": "taggables_tag_id_foreign", "tableFrom": "taggables", "tableTo": "tags", "columnsFrom": ["tag_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "tags": {"name": "tags", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "name": {"autoincrement": false, "name": "name", "type": "json", "primaryKey": false, "notNull": true}, "slug": {"autoincrement": false, "name": "slug", "type": "json", "primaryKey": false, "notNull": true}, "subtags": {"autoincrement": false, "name": "subtags", "type": "text", "primaryKey": false, "notNull": false}, "categories": {"autoincrement": false, "name": "categories", "type": "text", "primaryKey": false, "notNull": false}, "type": {"autoincrement": false, "name": "type", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "order_column": {"autoincrement": false, "name": "order_column", "type": "int", "primaryKey": false, "notNull": false}, "is_verified": {"default": 0, "autoincrement": false, "name": "is_verified", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_popular": {"default": 0, "autoincrement": false, "name": "is_popular", "type": "tinyint", "primaryKey": false, "notNull": true}}, "compositePrimaryKeys": {"tags_id": {"name": "tags_id", "columns": ["id"]}}, "indexes": {"idx_id": {"name": "idx_id", "columns": ["id"], "isUnique": false}}, "foreignKeys": {}, "uniqueConstraints": {}}, "user_chapter": {"name": "user_chapter", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "chapter_id": {"autoincrement": false, "name": "chapter_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "is_passed": {"autoincrement": false, "name": "is_passed", "type": "tinyint", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"user_chapter_id": {"name": "user_chapter_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"user_chapter_user_id_foreign": {"name": "user_chapter_user_id_foreign", "tableFrom": "user_chapter", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "user_favourite": {"name": "user_favourite", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"user_favourite_id": {"name": "user_favourite_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"user_favourite_job_id_foreign": {"name": "user_favourite_job_id_foreign", "tableFrom": "user_favourite", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_favourite_user_id_foreign": {"name": "user_favourite_user_id_foreign", "tableFrom": "user_favourite", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "user_onesignal": {"name": "user_onesignal", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "player_id": {"autoincrement": false, "name": "player_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"user_onesignal_id": {"name": "user_onesignal_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"user_onesignal_user_id_foreign": {"name": "user_onesignal_user_id_foreign", "tableFrom": "user_onesignal", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "user_verifications": {"name": "user_verifications", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "token": {"autoincrement": false, "name": "token", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "candidate_id": {"autoincrement": false, "name": "candidate_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "client_id": {"autoincrement": false, "name": "client_id", "type": "int unsigned", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"user_verifications_id": {"name": "user_verifications_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"user_verifications_candidate_id_foreign": {"name": "user_verifications_candidate_id_foreign", "tableFrom": "user_verifications", "tableTo": "candidates", "columnsFrom": ["candidate_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_verifications_user_id_foreign": {"name": "user_verifications_user_id_foreign", "tableFrom": "user_verifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "user_withdraw": {"name": "user_withdraw", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "user_id": {"autoincrement": false, "name": "user_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "amount": {"autoincrement": false, "name": "amount", "type": "int", "primaryKey": false, "notNull": true}, "status": {"autoincrement": false, "name": "status", "type": "enum('requested','completed','rejected')", "primaryKey": false, "notNull": true}, "transaction_ref": {"autoincrement": false, "name": "transaction_ref", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "transaction_date": {"autoincrement": false, "name": "transaction_date", "type": "datetime", "primaryKey": false, "notNull": false}, "note": {"autoincrement": false, "name": "note", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "bank_name": {"autoincrement": false, "name": "bank_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "bank_no": {"autoincrement": false, "name": "bank_no", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"user_withdraw_id": {"name": "user_withdraw_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"user_withdraw_user_id_foreign": {"name": "user_withdraw_user_id_foreign", "tableFrom": "user_withdraw", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "users": {"name": "users", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "rank_id": {"autoincrement": false, "name": "rank_id", "type": "int", "primaryKey": false, "notNull": false}, "name": {"autoincrement": false, "name": "name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "is_admin": {"default": 0, "autoincrement": false, "name": "is_admin", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_sale": {"default": 0, "autoincrement": false, "name": "is_sale", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_consultant": {"default": 0, "autoincrement": false, "name": "is_consultant", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_corpcare": {"default": 0, "autoincrement": false, "name": "is_corpcare", "type": "tinyint", "primaryKey": false, "notNull": true}, "email": {"autoincrement": false, "name": "email", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "password": {"autoincrement": false, "name": "password", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "mobile": {"autoincrement": false, "name": "mobile", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "ic": {"default": "''", "autoincrement": false, "name": "ic", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "experience_years": {"autoincrement": false, "name": "experience_years", "type": "int", "primaryKey": false, "notNull": false}, "occupation": {"autoincrement": false, "name": "occupation", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "bank_no": {"autoincrement": false, "name": "bank_no", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "bank_name": {"autoincrement": false, "name": "bank_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "location": {"autoincrement": false, "name": "location", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "state": {"autoincrement": false, "name": "state", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "specialization": {"default": "''", "autoincrement": false, "name": "specialization", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "app_first_login": {"autoincrement": false, "name": "app_first_login", "type": "datetime", "primaryKey": false, "notNull": false}, "last_login": {"autoincrement": false, "name": "last_login", "type": "datetime", "primaryKey": false, "notNull": false}, "last_login_ip": {"autoincrement": false, "name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "registration_ip": {"autoincrement": false, "name": "registration_ip", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_verified": {"default": 0, "autoincrement": false, "name": "is_verified", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_banned": {"default": 0, "autoincrement": false, "name": "is_banned", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_migrated": {"default": 0, "autoincrement": false, "name": "is_migrated", "type": "tinyint", "primaryKey": false, "notNull": true}, "is_passed": {"autoincrement": false, "name": "is_passed", "type": "tinyint", "primaryKey": false, "notNull": false}, "remember_token": {"autoincrement": false, "name": "remember_token", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "remark": {"autoincrement": false, "name": "remark", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "mailchimp_id": {"autoincrement": false, "name": "mailchimp_id", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "saved_jobs": {"autoincrement": false, "name": "saved_jobs", "type": "json", "primaryKey": false, "notNull": true}, "recruiter_group": {"default": "'B'", "autoincrement": false, "name": "recruiter_group", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "company_name": {"autoincrement": false, "name": "company_name", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "age": {"autoincrement": false, "name": "age", "type": "int", "primaryKey": false, "notNull": false}, "designation": {"autoincrement": false, "name": "designation", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "facebook": {"autoincrement": false, "name": "facebook", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "twitter": {"autoincrement": false, "name": "twitter", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "instagram": {"autoincrement": false, "name": "instagram", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "linkedin": {"autoincrement": false, "name": "linkedin", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "languages": {"autoincrement": false, "name": "languages", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "committed_hours_daily": {"autoincrement": false, "name": "committed_hours_daily", "type": "int", "primaryKey": false, "notNull": false}, "sourcing_method": {"autoincrement": false, "name": "sourcing_method", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": false}, "is_technical_recruiter": {"default": 0, "autoincrement": false, "name": "is_technical_recruiter", "type": "tinyint", "primaryKey": false, "notNull": false}, "is_working_fulltime": {"default": 0, "autoincrement": false, "name": "is_working_fulltime", "type": "tinyint", "primaryKey": false, "notNull": false}, "last_seen": {"autoincrement": false, "name": "last_seen", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"users_id": {"name": "users_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"]}}}, "wanted_job": {"name": "wanted_job", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "wanted_id": {"autoincrement": false, "name": "wanted_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "job_id": {"autoincrement": false, "name": "job_id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"wanted_job_id": {"name": "wanted_job_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {"wanted_job_job_id_foreign": {"name": "wanted_job_job_id_foreign", "tableFrom": "wanted_job", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "wanted_job_wanted_id_foreign": {"name": "wanted_job_wanted_id_foreign", "tableFrom": "wanted_job", "tableTo": "wanteds", "columnsFrom": ["wanted_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "uniqueConstraints": {}}, "wanteds": {"name": "wanteds", "columns": {"id": {"autoincrement": true, "name": "id", "type": "int unsigned", "primaryKey": false, "notNull": true}, "title": {"autoincrement": false, "name": "title", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "image": {"autoincrement": false, "name": "image", "type": "<PERSON><PERSON><PERSON>(191)", "primaryKey": false, "notNull": true}, "is_active": {"default": 1, "autoincrement": false, "name": "is_active", "type": "tinyint", "primaryKey": false, "notNull": true}, "priority": {"autoincrement": false, "name": "priority", "type": "int", "primaryKey": false, "notNull": false}, "created_at": {"autoincrement": false, "name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"autoincrement": false, "name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "compositePrimaryKeys": {"wanteds_id": {"name": "wanteds_id", "columns": ["id"]}}, "indexes": {}, "foreignKeys": {}, "uniqueConstraints": {}}}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}