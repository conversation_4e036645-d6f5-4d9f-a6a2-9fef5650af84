import type { Config } from "drizzle-kit";
export default {
  schema: "./schema/*",
  out: "./drizzle",
  driver: 'mysql2',
  // dbCredentials: {
  //   user: "root",
  //   password: "",
  //   host: "127.0.0.1",
  //   port: 3306,
  //   database: "seekers_production",
  // }
  dbCredentials: {
    user: "root",
    password: "",
    host: "127.0.0.1",
    port: 3306,
    database: "seekers_production",
  }
} satisfies Config;