export interface CustomUser {
  [key: string]: any;
  old_password: string;
  candidate_profile: string;
  recruiter_profile: string;
  client_profile: string;
  has_migrated_password: boolean;
}
export interface Candidate {
  about_me?: string;
  birth_year?: number;
  created_at?: string;
  current_position?: string;
  current_salary?: number;
  cv?: string;
  cv_masking?: string;
  cv_rating?: number;
  cv_report?: string;
  cv_updated_at?: string;
  cv_verified?: number;
  education?: string;
  education_level?: string;
  email: string;
  education_history? :any;
  expected_salary?: number;
  experience_data?: string;
  facebook_id?: string;
  first_name?: string;
  gender?: string;
  github?: string;
  google_id?: string;
  got_visa?: number;
  has_work_permit?: number;
  ic?: string;
  id: string;
  image?: string;
  image_url?: string;
  instagram?: string;
  is_app?: number;
  is_blacklisted?: number;
  is_local?: number;
  is_offer?: number;
  is_verified?: number;
  languages?: string[];
  last_login?: string;
  last_name?: string;
  last_seen?: string;
  level?: "entry" | "junior" | "senior" | "manager" | "senior manager";
  linkedin_id?: string;
  location?: string;
  mobile?: string;
  name?: string;
  nationality?: string;
  notice_period?: string;
  offer_data?: any;
  open_to_work?: number;
  own_driver_license?: number;
  own_transport?: number;
  portfolio?: string;
  race?: string;
  remarks?: string;
  roles?: any; // related to roleselated to roles
  skills?: any;
  state?: string;
  status: "active" | "draft" | "archived";
  tags: string[];
  twitter_id?: string;
  updated_at?: string;
  user?: string; // related to directus_users
  website?: string;
  willing_to_travel?: number;
}

interface CandidateLanguage {
  id: number;
  status: string;
  user_created: string;
  date_created: Date;
  user_updated: null;
  date_updated: null;
  language: string;
  level: string;
  candidate: number;
}

interface Tag {
  id: number;
  name: number;
  slug: string;
}

interface CandidateRoles {
  id: UUID;
  candidates_id: string;
  roles_id: string;
}

interface CandidateTag {
  id: UUID;
  candidates_id: string;
  tags_id: string;

}


type UUID = `${string}-${string}-${string}-${string}-${string}`;

export default interface Schema {
  candidates: Candidate[];
  candidates_languages: CandidateLanguage[];
  candidates_roles: CandidateRoles[];
  candidates_tags: CandidateTag[];
  directus_users: CustomUser[]; // extends directus_users
  tags: Tag[];
}
