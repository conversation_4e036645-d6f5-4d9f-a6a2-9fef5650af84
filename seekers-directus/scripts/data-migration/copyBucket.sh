#!/bin/bash

# # Check if both source and destination stages are provided
# if [ "$#" -ne 2 ]; then
#     echo "Error: Both source and destination stages are required."
#     echo "Usage: $0 [source-stage] [destination-stage]"
#     exit 1
# fi

# # Retrieve the stage from the first argument
# from=$1
# to=$2

# Source and destination bucket names with stage variable
SOURCE_BUCKET="seekers-web/cv"
DEST_BUCKET="seekers-directus/cv"

# Optional: Exclude certain files from being copied
# For example, to exclude all .DS_Store files, you can add --exclude "*.DS_Store"
EXCLUDE_PATTERN="--exclude \"*.DS_Store\""

# Confirmation
echo "Are you sure you want to copy contents from $SOURCE_BUCKET to $DEST_BUCKET? (y/N)"
read -r REPLY

if [[ $REPLY =~ ^[Yy]$ ]]
then
    # Perform the sync operation
    aws s3 sync s3://$SOURCE_BUCKET/ s3://$DEST_BUCKET/ $EXCLUDE_PATTERN
else
    echo "Operation cancelled."
    exit 0
fi
