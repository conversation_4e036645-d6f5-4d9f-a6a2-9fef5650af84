
# Seekers-Directus

Seekers-Directus is a custom implementation of the Directus headless CMS, configured to run seamlessly with Docker Compose. This project is tailored for ease of setup and use, focusing on custom extensions to enhance the Directus experience while keeping most configurations default or externally configurable.

## Getting Started

These instructions will get your copy of the Seekers-Directus project up and running on your local machine for development and testing purposes. See deployment for notes on how to deploy the project on a live system.

### Prerequisites

Before you begin, ensure you have Dock<PERSON> and Docker Compose installed on your system. If not, follow the installation guides for [Docker](https://docs.docker.com/get-docker/) and [Docker Compose](https://docs.docker.com/compose/install/).

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/seekers-my/seekers-directus.git
   cd seekers-directus
   ```

2. **Configure the environment variables**

   Duplicate the `.env.example` file, rename it to `.env`, and update the environment variables within it according to your setup. This step is crucial for configuring your Directus instance correctly.

3. **Customize extensions (optional)**

   If you need to customize the Directus extensions, modify the files within the `extensions/` directory. This setup is designed to automatically include these extensions when building the Directus service.

4. **Launch with Docker Compose**

   From the root of your project directory, start your Directus app using Docker Compose:

   ```bash
   docker-compose up -d
   ```

   This command will build and start the containers needed for your Directus application.

### Accessing Directus

Once everything is up and running, you can access the Directus admin interface by navigating to `http://localhost:8055` in your web browser. The default login credentials are set in the `.env` file or can be configured through the Directus admin settings.

## Deployment

To deploy Seekers-Directus on a live system, follow the same steps as above, ensuring that your `.env` file is configured with production-ready settings. For more detailed instructions on deploying Docker Compose applications in production environments, refer to the Docker documentation.

## Built With

- [Directus](https://directus.io/) - Open-source headless CMS
- [Docker](https://docker.com/) - Containerization platform
- [Docker Compose](https://docs.docker.com/compose/) - Tool for defining and running multi-container Docker applications


## Authors

- **Your Name** - *Initial work* - [rafazafar](https://github.com/rafaxafar)

See also the list of [contributors](https://github.com/seekers-my/seekers-directus/contributors) who participated in this project.

## License

This project is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

## Acknowledgments

- Hat tip to anyone whose code was used
- Inspiration
- etc
