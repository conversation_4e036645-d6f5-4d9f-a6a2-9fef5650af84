{"name": "seekers-pages", "description": "Please enter a description for your extension", "icon": "extension", "version": "0.0.4", "keywords": ["directus", "directus-extension", "directus-custom-module"], "type": "module", "directus:extension": {"type": "module", "path": "dist/index.js", "source": "src/index.ts", "host": "^10.3.4"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build -w --no-minify"}, "devDependencies": {"@directus/extensions-sdk": "10.3.4", "chokidar": "^3.6.0", "concurrently": "^8.2.2", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vue": "^3.4.21", "vue-router": "^4.3.2"}, "dependencies": {"@vueuse/core": "^10.9.0"}}