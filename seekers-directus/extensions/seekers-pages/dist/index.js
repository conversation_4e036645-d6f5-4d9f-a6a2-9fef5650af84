import{defineModule as e}from"@directus/extensions-sdk";import{defineComponent as t,useModel as a,ref as l,resolveComponent as n,openBlock as o,createElementBlock as i,createBlock as r,withCtx as s,Fragment as u,renderList as d,createVNode as c,createCommentVNode as p,unref as m,watch as v,createElementVNode as f,toDisplayString as g,createTextVNode as h,pushScopeId as y,popScopeId as _,normalizeStyle as b,computed as k,createSlots as w}from"vue";import{useRoute as V,useRouter as C}from"vue-router";var x=(e,t)=>{const a=e.__vccOpts||e;for(const[e,l]of t)a[e]=l;return a},S=x(t({__name:"Navigation",props:{modelValue:{type:String},modelModifiers:{}},emits:["update:modelValue"],setup(e){const t=a(e,"modelValue"),m=l([{label:"Welcome",to:"/pages",icon:"menu",color:"primary",uri:"pages"},{label:"CV Search",to:"/pages/cv-search",icon:"search",color:"warning",uri:"cv-search"}]);return(e,a)=>{const l=n("v-icon"),v=n("v-list-item-icon"),f=n("v-text-overflow"),g=n("v-list-item-content"),h=n("v-list-item"),y=n("v-list");return o(),i("div",null,[m.value?(o(),r(y,{key:0,nav:""},{default:s((()=>[(o(!0),i(u,null,d(m.value,(e=>(o(),r(h,{key:e.to,active:e.uri==t.value,to:e.to,onClick:a=>{return l=e.uri,void(t.value=l);var l}},{default:s((()=>[c(v,null,{default:s((()=>[c(l,{name:e.icon,color:e.color},null,8,["name","color"])])),_:2},1024),c(g,null,{default:s((()=>[c(f,{text:e.label},null,8,["text"])])),_:2},1024)])),_:2},1032,["active","to","onClick"])))),128))])),_:1})):p("v-if",!0)])}}}),[["__file","Navigation.vue"]]);function T(e){return"function"==typeof e?e():m(e)}"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const P=()=>{};function j(e,t=200,a={}){return function(e,t){return function(...a){return new Promise(((l,n)=>{Promise.resolve(e((()=>t.apply(this,a)),{fn:t,thisArg:this,args:a})).then(l).catch(n)}))}}(function(e,t={}){let a,l,n=P;const o=e=>{clearTimeout(e),n(),n=P};return i=>{const r=T(e),s=T(t.maxWait);return a&&o(a),r<=0||void 0!==s&&s<=0?(l&&(o(l),l=null),Promise.resolve(i())):new Promise(((e,u)=>{n=t.rejectOnCancel?u:e,s&&!l&&(l=setTimeout((()=>{a&&o(a),l=null,e(i())}),s)),a=setTimeout((()=>{l&&o(l),l=null,e(i())}),r)}))}}(t,a),e)}const I=e=>{window.open(e,"_blank")},q=e=>(y("data-v-3fd498cb"),e=e(),_(),e),$={id:"page-content"},z={style:{top:"4em",position:"sticky",background:"white",padding:"1rem","z-index":"1","padding-bottom":"0px"}},N=q((()=>f("h1",null,"Keyword Search",-1))),O={style:{"font-size":"small",color:"slategrey"}},W={style:{height:"1rem"}},A={key:0,class:"cv-search-result-wrapper"},M=q((()=>f("header",{style:{display:"flex","font-weight":"bold"}},[f("div",{style:{width:"5em"}},"ID"),f("div",{style:{width:"14em"}},"Name"),f("div",{class:"col"},"CV")],-1))),H=["onClick"],D={style:{width:"5em"}},G={style:{width:"15em","text-transform":"capitalize"}},L=["innerHTML"],B={key:1},E={style:{display:"flex","margin-top":"1em","justify-content":"center"}};var J=t({__name:"CVSearch",setup(e){const t=V(),a=C(),y=window.location.origin,_=l(""),b=l(null),k=l(1),w=l(50),x=l(!1);v([k,_],(()=>{if(!_.value)return;S(),a.push({query:{q:_.value,page:k.value}});const e=document.getElementById("page-content");e&&e.scrollIntoView({behavior:"smooth"})}));const S=j((async()=>{x.value=!0;const e=await fetch("https://meili.jobsearch-asia.com/indexes/candidates/search",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer meili-master-key007"},body:JSON.stringify({q:_.value,page:k.value,hitsPerPage:w.value,attributesToRetrieve:["id","raw_cv_text","name"],attributesToHighlight:["raw_cv_text"],highlightPreTag:"<span class='highlight' style='color:black; font-weight:bold; font-size:1rem'>",highlightPostTag:"</span>",matchingStrategy:"last",attributesToSearchOn:["raw_cv_text"],offset:0})}),t=await e.json();b.value=t,console.log(t),x.value=!1}),150,{maxWait:3e3});t.query.q&&(_.value=t.query.q,k.value=t.query.page?parseInt(t.query.page):1);const T=e=>{const t=(e=(e=(e=e.replace(/(\r\n|\n|\r)/gm," ")).replace(/\. +(?=[A-Z])/g,".<br/>")).replace(/•/g,"<br/>")).split("<br/>").filter((e=>e.includes("highlight")));return e=t.map((e=>e.includes("highlight")?`<li>${e=e.trim()}</li>`:e)).join("")};return(e,t)=>{var a,l,v;const w=n("VInput"),V=n("VProgressLinear"),C=n("VPagination"),S=n("VDivider"),P=n("VInfo"),j=n("VButton");return o(),i("section",$,[f("div",z,[N,c(w,{modelValue:_.value,"onUpdate:modelValue":t[0]||(t[0]=e=>_.value=e),placeholder:'eg. "sales manager" "kuala lumpur"',autofocus:!0},null,8,["modelValue"]),f("p",O,g(_.value&&""!=_.value?`Results in ${null!=(l=null==(a=b.value)?void 0:a.processingTimeMs)?l:"-"} ms`:"Type to start searching"),1)]),f("div",W,[x.value?(o(),r(V,{key:0,value:70,indeterminate:!0,rounded:!0})):p("v-if",!0)]),_.value&&b.value&&(null==(v=b.value)?void 0:v.hits.length)>0?(o(),i("div",A,[f("div",null,[c(C,{modelValue:k.value,"onUpdate:modelValue":t[1]||(t[1]=e=>k.value=e),length:b.value.totalPages,totalVisible:4,showFirstLast:!0,disabled:x.value,style:{"justify-content":"center"}},null,8,["modelValue","length","disabled"])]),M,c(S),(o(!0),i(u,null,d(b.value.hits,(e=>(o(),i(u,{key:e.id},[f("div",{class:"row",onClick:t=>m(I)(`${m(y)}/admin/content/candidates/${e.id}`)},[f("div",D,g(e.id),1),f("div",G,g(e.name.toLowerCase()),1),f("div",{class:"col",style:{"line-height":"normal"},innerHTML:T(e._formatted.raw_cv_text)},null,8,L)],8,H),c(S)],64)))),128)),c(C,{modelValue:k.value,"onUpdate:modelValue":t[2]||(t[2]=e=>k.value=e),length:b.value.totalPages,totalVisible:4,showFirstLast:!0,style:{"justify-content":"center"}},null,8,["modelValue","length"])])):_.value&&b.value&&0===b.value.hits.length?(o(),i("div",B,[c(P,{title:"No Results",icon:"cookie"},{default:s((()=>[h(" No results found. Try a different keyword. ")])),_:1}),f("div",E,[c(j,{small:"",onClick:t[3]||(t[3]=e=>_.value="")},{default:s((()=>[h("Clear")])),_:1})])])):p("v-if",!0)])}}}),U=[],F=[];function R(e,t){if(e&&"undefined"!=typeof document){var a,l=!0===t.prepend?"prepend":"append",n=!0===t.singleTag,o="string"==typeof t.container?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(n){var i=U.indexOf(o);-1===i&&(i=U.push(o)-1,F[i]={}),a=F[i]&&F[i][l]?F[i][l]:F[i][l]=r()}else a=r();65279===e.charCodeAt(0)&&(e=e.substring(1)),a.styleSheet?a.styleSheet.cssText+=e:a.appendChild(document.createTextNode(e))}function r(){var e=document.createElement("style");if(e.setAttribute("type","text/css"),t.attributes)for(var a=Object.keys(t.attributes),n=0;n<a.length;n++)e.setAttribute(a[n],t.attributes[a[n]]);var i="prepend"===l?"afterbegin":"beforeend";return o.insertAdjacentElement(i,e),e}}R("\n#page-content[data-v-3fd498cb] {\n  padding: 0em 1em 1em 1em;\n}\n.cv-search-result-wrapper[data-v-3fd498cb] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5em;\n}\n.cv-search-result[data-v-3fd498cb] {\n  padding: 1em;\n  border-radius: 5px;\n  border: 1px solid #ccc;\n}\n.status-open-to-work[data-v-3fd498cb] {\n  font-size: 0.75rem;\n  font-style: italic;\n}\n.search-result-item[data-v-3fd498cb] {\n  display: flex;\n  gap: 0.5rem;\n  align-items: center;\n  font-size: small;\n}\n.row[data-v-3fd498cb] {\n  display: flex;\n  border-radius: 10px;\n  padding: 0.5rem;\n  align-items: center;\n  font-size: small;\n  gap: 0.5em;\n}\n.row[data-v-3fd498cb]:hover {\n  background: #f2f2f2;\n  transition: ease-in-out 0.2s;\n  cursor: pointer;\n}\n.col[data-v-3fd498cb] {\n  flex: 1;\n}\n",{});var K=x(J,[["__scopeId","data-v-3fd498cb"],["__file","CVSearch.vue"]]);const Z={style:{display:"flex"}},Q=f("span",{style:{width:"100px"}},"Website",-1),X={style:{display:"flex"}},Y=f("span",{style:{width:"100px"}},"Server",-1),ee={style:{display:"flex"}},te=f("span",{style:{width:"100px"}},"Database",-1);var ae=x(t({__name:"SystemHealthCard",setup(e){const t=l("ok"),a=l("ok"),i=l("ok");return(e,l)=>{const u=n("VCardTitle"),d=n("VIcon"),p=n("VCardText"),m=n("VCard");return o(),r(m,{tile:!1},{default:s((()=>[c(u,null,{default:s((()=>[h("System Health Status")])),_:1}),c(p,null,{default:s((()=>[f("div",Z,[Q,c(d,{style:b({color:"ok"===t.value?"green":" red"}),name:(t.value,"check")},null,8,["style","name"])]),f("div",X,[Y,c(d,{style:b({color:"ok"===a.value?"green":" red"}),name:"ok"===a.value?"check":"error"},null,8,["style","name"])]),f("div",ee,[te,c(d,{style:b({color:"ok"===i.value?"green":" red"}),name:"ok"===i.value?"check":"error"},null,8,["style","name"])])])),_:1})])),_:1})}}}),[["__file","SystemHealthCard.vue"]]);const le={id:"page-content"},ne={class:"grid"},oe={class:"grid"},ie={class:"grid"};var re=t({__name:"Welcome",setup:e=>(e,t)=>{const a=n("VNotice"),l=n("VCardTitle"),r=n("VCardText"),u=n("VButton"),d=n("VCardActions"),p=n("VCard"),v=n("VDivider");return o(),i("section",le,[c(a,{style:{"margin-top":"-1em","margin-bottom":"1em"}},{default:s((()=>[h(" System Overview : Operational ")])),_:1}),f("div",ne,[c(ae),c(p,{tile:!1,disabled:!1},{default:s((()=>[c(l,null,{default:s((()=>[h("Data Insights")])),_:1}),c(r,null,{default:s((()=>[h(" View, edit and create data visualizations. ")])),_:1}),c(d,null,{default:s((()=>[c(u,{secondary:"",onClick:t[0]||(t[0]=t=>e.$router.push("/insights/51a4da21-a658-4b73-9563-ddc54a010e54"))},{default:s((()=>[h(" Go to Insights -> ")])),_:1})])),_:1})])),_:1})]),f("div",oe,[c(p,{tile:!1},{default:s((()=>[c(l,null,{default:s((()=>[h(" CV Search")])),_:1}),c(r,null,{default:s((()=>[h(" Find candidates by using keyword search againts all parsed CVs ")])),_:1}),c(d,null,{default:s((()=>[c(u,{secondary:"",onClick:t[1]||(t[1]=t=>e.$router.push("/pages/cv-search"))},{default:s((()=>[h(" Search -> ")])),_:1})])),_:1})])),_:1}),c(p,{tile:!1},{default:s((()=>[c(l,null,{default:s((()=>[h("Data Studio")])),_:1}),c(r,null,{default:s((()=>[h(" Manage all your Candidates, Jobs, Companies, etc. ")])),_:1}),c(d,null,{default:s((()=>[c(u,{secondary:"",onClick:t[2]||(t[2]=e=>m(I)("https://docs.directus.io/user-guide/overview/data-studio-app.html"))},{default:s((()=>[h(" Help Guide")])),_:1}),c(u,{secondary:"",onClick:t[3]||(t[3]=t=>e.$router.push("/content/applications"))},{default:s((()=>[h(" Manage -> ")])),_:1})])),_:1})])),_:1}),c(p,{tile:!1},{default:s((()=>[c(l,null,{default:s((()=>[h("Settings")])),_:1}),c(r,null,{default:s((()=>[h(" Control your platform settings and preferences. ")])),_:1}),c(d,null,{default:s((()=>[c(u,{secondary:"",onClick:t[4]||(t[4]=t=>e.$router.push("/content/settings"))},{default:s((()=>[h(" Go To Settings -> ")])),_:1})])),_:1})])),_:1})]),c(v,{style:{margin:"1em 0em"}}),f("div",ie,[c(p,{tile:!1},{default:s((()=>[c(l,null,{default:s((()=>[h("Create Job")])),_:1}),c(r,null,{default:s((()=>[h(" Manually create a new public job posting. ")])),_:1}),c(d,null,{default:s((()=>[c(u,{secondary:"",onClick:t[5]||(t[5]=t=>e.$router.push("/content/jobs/+"))},{default:s((()=>[h(" + Job")])),_:1})])),_:1})])),_:1}),c(p,{tile:!1},{default:s((()=>[c(l,null,{default:s((()=>[h("Create Client")])),_:1}),c(r,null,{default:s((()=>[h(" Manually add a new client and company ")])),_:1}),c(d,null,{default:s((()=>[c(u,{secondary:"",onClick:t[6]||(t[6]=t=>e.$router.push("/content/clients/+"))},{default:s((()=>[h(" + Client")])),_:1})])),_:1})])),_:1})])])}});R("\n#page-content[data-v-ca244f63] {\n    padding: 0em 1em 1em 1em;\n}\n.grid[data-v-ca244f63] {\n    margin-top: 1em;\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: 1em;\n}\n",{});var se=x(re,[["__scopeId","data-v-ca244f63"],["__file","Welcome.vue"]]),ue=x(t({__name:"module",props:{page:String},setup(e){const{page:t}=e,a=l(t),i=l([{name:"Pages",to:"/pages"}]),u=k((()=>{switch(a.value){case"pages":default:return"Welcome";case"cv-search":return"CV Search"}}));return(e,t)=>{const l=n("v-breadcrumb"),d=n("private-view");return o(),r(d,{title:u.value},w({navigation:s((()=>[c(S,{modelValue:a.value,"onUpdate:modelValue":t[0]||(t[0]=e=>a.value=e)},null,8,["modelValue"])])),default:s((()=>["cv-search"===a.value?(o(),r(K,{key:0})):(o(),r(se,{key:1}))])),_:2},[i.value?{name:"headline",fn:s((()=>[c(l,{items:i.value},null,8,["items"])])),key:"0"}:void 0]),1032,["title"])}}}),[["__file","module.vue"]]);var de=e({id:"pages",name:"Pages",icon:"rocket_launch",routes:[{path:"",props:!0,component:ue},{name:"test",path:"test",props:!1,component:x(t({setup:()=>({page:l("test")})}),[["render",function(e,t,a,l,i,u){const d=n("router-link"),p=n("private-view");return o(),r(p,{title:"Test Component"},{default:s((()=>[c(d,{to:"/pages/hello-world"},{default:s((()=>[h("Go to landing page")])),_:1})])),_:1})}],["__file","test.vue"]])},{name:"page",path:":page",props:!0,component:ue}]});export{de as default};
