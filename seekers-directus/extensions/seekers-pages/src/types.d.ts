type Breadcrumb = {
  name: string;
  to: string;
}[];

type HealthStatus = "ok" | "pending" | "error";

type MeiliResponse<T> = {
  hits: T[];
  hitsPerPage: number;
  page: number;
  processingTimeMs: number;
  query: string;
  totalHits: number;
  totalPages: number;
};

type MeiliCandidateIndex = {
  id: string;
  name: string;
  raw_cv_text: string;
  _formatted: {
    raw_cv_text: string;
  };
};
