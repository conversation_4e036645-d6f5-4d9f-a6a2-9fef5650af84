<template>
  <private-view :title="getPageTitle">
    <template #navigation>
      <Navigation v-model="pageUri" />
    </template>

    <template v-if="breadcrumb" #headline>
      <v-breadcrumb :items="breadcrumb" />
    </template>

    <CVSearch v-if="pageUri === 'cv-search'" />

    <Welcome v-else />
  </private-view>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import Navigation from "./components/Navigation.vue";
import CVSearch from "./pages/CVSearch.vue";
import Welcome from "./pages/Welcome.vue";

const { page } = defineProps({
  page: String,
});
const pageUri = ref(page);
const breadcrumb = ref<Breadcrumb>([
  {
    name: "Pages",
    to: `/pages`,
  },
]);
const getPageTitle = computed(() => {
  switch (pageUri.value) {
    case "pages":
      return "Welcome";
    case "cv-search":
      return "CV Search";
    default:
      return "Welcome";
  }
});
</script>