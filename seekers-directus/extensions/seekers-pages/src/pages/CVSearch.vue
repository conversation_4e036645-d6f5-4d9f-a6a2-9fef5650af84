<template>
  <section id="page-content">
    <div style="top: 4em;position: sticky; background: white;padding: 1rem; z-index:1;padding-bottom: 0px;">
      <h1>Keyword Search</h1>
      <VInput v-model="searchKeyword" placeholder='eg. "sales manager" "kuala lumpur"' :autofocus="true" />
      <p style="font-size: small; color:slategrey">
        {{ !searchKeyword || searchKeyword == '' ? 'Type to start searching' :
          `Results in ${results?.processingTimeMs ?? '-'} ms` }}
      </p>
    </div>

    <div style="height:1rem;">
      <VProgressLinear v-if="isLoading" :value="70" :indeterminate="true" :rounded="true" />
    </div>
    <div v-if="searchKeyword && results && results?.hits.length > 0" class="cv-search-result-wrapper">
      <div>
        <VPagination v-model="currentPage" :length="results.totalPages" :totalVisible="4" :showFirstLast="true"
          :disabled="isLoading" style="justify-content: center;" />
      </div>
      <header style="display:flex; font-weight: bold;">
        <div style="width:5em">ID</div>
        <div style="width:14em">Name</div>
        <div class="col">CV</div>
      </header>
      <VDivider />
      <template v-for="hit in results.hits" :key="hit.id">
        <div class="row" @click="openNewTab(`${currentOrigin}/admin/content/candidates/${hit.id}`)">
          <div style="width:5em">{{ hit.id }}</div>
          <div style="width:15em;text-transform: capitalize;">{{ hit.name.toLowerCase() }}</div>
          <div class="col" style="line-height: normal;" v-html="processRawCvText(hit._formatted.raw_cv_text)">
          </div>
        </div>
        <VDivider />
      </template>
      <VPagination v-model="currentPage" :length="results.totalPages" :totalVisible="4" :showFirstLast="true"
        style="justify-content: center;" />
    </div>
    <div v-else-if="searchKeyword && results && results.hits.length === 0">
      <VInfo title="No Results" icon="cookie">
        No results found. Try a different keyword.
      </VInfo>
      <div style="display: flex;margin-top: 1em;justify-content: center;">
        <VButton small @click="searchKeyword = ''">Clear</VButton>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useDebounceFn } from "@vueuse/core";
import { useRouter, useRoute } from "vue-router";
import { openNewTab } from "../utils";

const route = useRoute();
const router = useRouter();

// CV Search
const currentOrigin = window.location.origin;
// const candidateCollectionApiUrl = `${currentOrigin}/items/candidates?`;
const meiliIndexSearchUrl = "https://meili.jobsearch-asia.com/indexes/candidates/search";
const searchKeyword = ref('');
const results = ref<MeiliResponse<MeiliCandidateIndex> | null>(null);
const currentPage = ref(1);
const hitsPerPage = ref(50);
const isLoading = ref(false);
// const directusLookupRes = ref<any>(null);

watch([currentPage, searchKeyword], () => {
  if (!searchKeyword.value) return;
  search();
  router.push({ query: { q: searchKeyword.value, page: currentPage.value } })
  const pageContentElement = document.getElementById("page-content");
  if (pageContentElement) {
    pageContentElement.scrollIntoView({ behavior: 'smooth' });
  }
});

const search = useDebounceFn(async () => {
  isLoading.value = true;
  const res = await fetch(meiliIndexSearchUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer meili-master-key007",
    },
    body: JSON.stringify({
      q: searchKeyword.value,
      page: currentPage.value,
      hitsPerPage: hitsPerPage.value,
      "attributesToRetrieve": ["id", "raw_cv_text", "name"],
      "attributesToHighlight": ["raw_cv_text"],
      // "attributesToCrop": ["raw_cv_text"],
      // "cropMarker": "...",
      // "cropLength": 200,
      "highlightPreTag": "<span class='highlight' style='color:black; font-weight:bold; font-size:1rem'>",
      "highlightPostTag": "</span>",
      "matchingStrategy": "last",
      "attributesToSearchOn": ["raw_cv_text"],
      "offset": 0,
    })
  });
  const meiliRes: MeiliResponse<MeiliCandidateIndex> = await res.json();
  results.value = meiliRes;
  console.log(meiliRes)
  isLoading.value = false;

  // // Cross reference with candidates in directus.
  // const filterQuery = `filter[id][_in]=${meiliRes.hits.map((hit: any) => hit.id).join(",")}`
  // const accessToken = '&access_token=ulDoHezGqsQa1iboToVCzVJmmCWHS0zD'
  // const candidatesRes = await (await fetch(candidateCollectionApiUrl + filterQuery + accessToken)).json();
  // const candidates: any = {};
  // candidatesRes.data.map((candidate: any) => {
  //   candidates[candidate.id] = candidate;
  // });

  // directusLookupRes.value = candidates;



}, 150, { maxWait: 3000 });


if (route.query.q) {
  searchKeyword.value = route.query.q as string;
  currentPage.value = route.query.page ? parseInt(route.query.page as string) : 1;
}

const processRawCvText = (rawCvText: string) => {
  rawCvText = rawCvText.replace(/(\r\n|\n|\r)/gm, " ");

  rawCvText = rawCvText.replace(/\. +(?=[A-Z])/g, ".<br/>");

  rawCvText = rawCvText.replace(/•/g, "<br/>");

  const rawCvTextArr = rawCvText.split("<br/>").filter((line) => line.includes("highlight"))

  rawCvText = rawCvTextArr.map((line) => {
    if (line.includes("highlight")) {
      line = line.trim();
      return `<li>${line}</li>`
    } else {
      return line
    }
  }).join("")

  return rawCvText
};
</script>

<style scoped>
#page-content {
  padding: 0em 1em 1em 1em;
}


.cv-search-result-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}

.cv-search-result {
  padding: 1em;
  border-radius: 5px;
  border: 1px solid #ccc;
}

.status-open-to-work {
  font-size: 0.75rem;
  font-style: italic;
}

.search-result-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  font-size: small;
}

.row {
  display: flex;
  border-radius: 10px;
  padding: 0.5rem;
  align-items: center;
  font-size: small;
  gap: 0.5em;
}

.row:hover {
  background: #f2f2f2;
  transition: ease-in-out 0.2s;
  cursor: pointer;
}

.col {
  flex: 1;
}
</style>