<template>
    <section id="page-content">
        <VNotice style="margin-top: -1em; margin-bottom: 1em;">
            System Overview : Operational
        </VNotice>
        <div class="grid">
            <SystemHealthCard />

            <VCard :tile="false" :disabled="false">
                <VCardTitle>Data Insights</VCardTitle>
                <VCardText> View, edit and create data visualizations. </VCardText>
                <VCardActions>
                    <VButton secondary @click="
                        $router.push('/insights/51a4da21-a658-4b73-9563-ddc54a010e54')
                        ">
                        Go to Insights ->
                    </VButton>
                </VCardActions>
            </VCard>
        </div>
        <div class="grid">
            <VCard :tile="false">
                <VCardTitle> CV Search</VCardTitle>
                <VCardText>
                    Find candidates by using keyword search againts all parsed CVs
                </VCardText>
                <VCardActions>
                    <VButton secondary @click="$router.push('/pages/cv-search')"> Search -> </VButton>
                </VCardActions>
            </VCard>

            <VCard :tile="false">
                <VCardTitle>Data Studio</VCardTitle>
                <VCardText>
                    Manage all your Candidates, Jobs, Companies, etc.
                </VCardText>
                <VCardActions>
                    <VButton secondary
                        @click="openNewTab('https://docs.directus.io/user-guide/overview/data-studio-app.html')">
                        Help
                        Guide</VButton>
                    <VButton secondary @click="$router.push('/content/applications')"> Manage -> </VButton>
                </VCardActions>
            </VCard>

            <VCard :tile="false">
                <VCardTitle>Settings</VCardTitle>
                <VCardText>
                    Control your platform settings and preferences.
                </VCardText>
                <VCardActions>
                    <VButton secondary @click="$router.push('/content/settings')"> Go To Settings -> </VButton>
                </VCardActions>
            </VCard>
        </div>
        <VDivider style="margin:1em 0em;" />
        <div class="grid">
            <VCard :tile="false">
                <VCardTitle>Create Job</VCardTitle>
                <VCardText>
                    Manually create a new public job posting.
                </VCardText>
                <VCardActions>
                    <VButton secondary @click="$router.push('/content/jobs/+')"> + Job</VButton>
                </VCardActions>
            </VCard>

            <VCard :tile="false">
                <VCardTitle>Create Client</VCardTitle>
                <VCardText>
                    Manually add a new client and company
                </VCardText>
                <VCardActions>
                    <VButton secondary @click="$router.push('/content/clients/+')"> + Client</VButton>
                </VCardActions>
            </VCard>
        </div>
    </section>
</template>

<script setup lang="ts">
import { openNewTab } from "../utils";
import SystemHealthCard from "../components/SystemHealthCard.vue";
</script>

<style scoped>
#page-content {
    padding: 0em 1em 1em 1em;
}

.grid {
    margin-top: 1em;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1em;
}
</style>