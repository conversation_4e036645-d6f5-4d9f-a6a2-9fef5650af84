<template>
    <VCard :tile="false">
        <VCardTitle>System Health Status</VCardTitle>
        <VCardText>
            <div style="display:flex;">
                <span style="width:100px;">Website</span>
                <VIcon :style="{ color: webStatus === 'ok' ? 'green' : ' red' }"
                    :name="webStatus === 'ok' ? 'check' : 'check'" />
            </div>
            <div style="display:flex;">
                <span style="width:100px;">Server</span>
                <VIcon :style="{ color: serverStatus === 'ok' ? 'green' : ' red' }"
                    :name="serverStatus === 'ok' ? 'check' : 'error'" />
            </div>
            <div style="display:flex;">
                <span style="width:100px;">Database</span>
                <VIcon :style="{ color: dbStatus === 'ok' ? 'green' : ' red' }"
                    :name="dbStatus === 'ok' ? 'check' : 'error'" />
            </div>
        </VCardText>
    </VCard>
</template>

<script setup lang="ts">
import { ref } from 'vue';


// System Health Check
const webStatus = ref<HealthStatus>('ok');
const serverStatus = ref<HealthStatus>('ok');
const dbStatus = ref<HealthStatus>('ok');

// const checkSystemStatus = async () => {
//   // fetch website
//   serverStatus.value = 'pending';
//   dbStatus.value = 'pending';

//   const [serverRes, databaseRes] = await Promise.all([
//     fetch(currentOrigin),
//     fetch(currentOrigin + '/items/jobs'),
//   ]);
//   // systemStatus.value.web = webRes.ok ? 'ok' : 'error';
//   serverStatus.value = serverRes.ok ? 'ok' : 'error';
//   dbStatus.value = databaseRes.ok ? 'ok' : 'error';
//   console.log(serverRes, databaseRes)
// }
// await checkSystemStatus();
</script>