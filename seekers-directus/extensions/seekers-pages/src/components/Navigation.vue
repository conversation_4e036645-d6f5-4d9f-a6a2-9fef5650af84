<template>
  <div>
    <v-list nav v-if="pages">
      <v-list-item
        v-for="navItem in pages"
        :key="navItem.to"
        :active="navItem.uri == model"
        :to="navItem.to"
        @click="update(navItem.uri)"
      >
        <v-list-item-icon
          ><v-icon :name="navItem.icon" :color="navItem.color"
        /></v-list-item-icon>
        <v-list-item-content>
          <v-text-overflow :text="navItem.label" />
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
const model = defineModel({ type: String })
const update = (value: string) => {
  model.value = value;
};
const pages = ref([
  {
    label: "Welcome",
    to: "/pages",
    icon: "menu",
    color: "primary",
    uri: "pages",
  },
  {
    label: "CV Search",
    to: "/pages/cv-search",
    icon: "search",
    color: "warning",
    uri: "cv-search",
  },
]);
</script>
