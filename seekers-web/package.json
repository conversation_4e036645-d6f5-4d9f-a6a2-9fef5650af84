{"private": true, "version": "1.7.0", "scripts": {"build": "nuxt build", "dev": "nuxt cleanup && nuxt dev", "preview": "nuxt build && nlx wrangler dev", "postinstall": "nuxt prepare"}, "devDependencies": {"@formkit/addons": "^1.6.9", "@formkit/auto-animate": "^0.8.2", "@formkit/vue": "^1.6.9", "@nuxt/devtools": "latest", "@nuxt/scripts": "0.11.6", "@nuxtjs/algolia": "^1.10.2", "@nuxtjs/device": "^3.2.4", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^9.5.4", "@nuxtjs/partytown": "^1.6.0", "@nuxtjs/tailwindcss": "6.12.0", "@nuxtjs/turnstile": "1.0.0", "@types/node": "^20.17.16", "@vueuse/nuxt": "^10.11.1", "algoliasearch": "^4.24.0", "animate.css": "^4.1.1", "daisyui": "4.12.10", "dayjs": "^1.11.13", "nuxt": "^3.17.4", "nuxt-build-cache": "latest", "nuxt-calendly": "^0.1.21", "nuxt-icon": "0.6.10", "sweetalert2": "^11.15.10", "transition-style": "^0.1.3", "vite": "^5.4.14", "vue": "^3.5.13", "wrangler": "^4.13.2"}}