export default async () => {
  // Fetch open job and company slugs from the backend
  let jobRoutes = [];
  // const DEV_MODE = process.env.DEV_MODE === "true";
  // if (process.version.includes("v20") && !DEV_MODE) {
  try {
    const jobRes = await fetch(process.env.BASEURL + "/sitemap/jobs");
    jobRoutes = await jobRes.json();
    jobRoutes = jobRoutes.map((slug: string) => "/jobs/" + slug);
  } catch (err) {
    throw err;
  }
  // } else {
  //   console.warn(
  //     `Skipping prefetching. DEV_MODE is ${DEV_MODE}. Node version is ${process.version}`
  //   );
  // }

  // Define the Nuxt config
  return defineNuxtConfig({
    devtools: { enabled: true },
    devServer: {
      port: 3001,
    },
    future: {
      compatibilityVersion: 4,
    },
    compatibilityDate: "2025-01-27",
    nitro: {
      preset: "cloudflare-module",
      routeRules: {
        // StaleWhileRevalidate (SWR)
        "/": { swr: 21600 },
        "/** ": { swr: 86400 },
        "/candidate/dashboard/**": { ssr: false },
        "/company/**": { swr: 86400 },
        "/employer": { swr: 86400 },
        "/hrtrends": { swr: 86400 },
        "/jobs": { swr: 3600 },
        "/jobs/**": { swr: 21600 },
        "/jobs/**/apply": { ssr: false },
        "/recruiter": { swr: 86400 },
        "/recruiter/**": { ssr: false },
        "/salary-guide": { swr: 86400 },
        // Prerender (Static)
        "/about": { prerender: true },
        "/contact": { prerender: true },
        "/forgot-password": { prerender: true },
        "/global-hr": { prerender: true },
        "/privacy": { prerender: true },
        "/refer-a-business": { prerender: true },
        "/rpo-services": { prerender: true },
        "/terms": { prerender: true },
        // Redirects
        "/candidates": { redirect: "/candidate" },
        "/companies": { redirect: "/company" },
        "/employers": { redirect: "/employer" },
        "/srs/registration": {
          redirect: "https://srs.seekers.my/registration",
        },
      },
    },
    app: {
      head: {
        htmlAttrs: {
          lang: "en",
        },
        title: "Job Seekers | Find Jobs & Vacancies in Malaysia  - Seekers",
        meta: [
          {
            name: "description",
            content:
              "Looking for the latest job opportunities in Malaysia? Seekers can help! Browse and apply to your dream job today. Join the thousands of successful job seekers who have found their ideal career through Seekers.",
          },
          {
            name: "og:type",
            content: "website",
          },
          {
            name: "og:title",
            content:
              "Job Seekers | Find Jobs & Vacancies in Malaysia  - Seekers",
          },
          {
            name: "og:description",
            content:
              "Looking for the latest job opportunities in Malaysia? Seekers can help! Browse and apply to your dream job today. Join the thousands of successful job seekers who have found their ideal career through Seekers.",
          },
          {
            name: "og:url",
            content: process.env.WEBURL || "https://seekers.my",
          },
          {
            name: "og:site_name",
            content: "Seekers",
          },
          {
            name: "og:image",
            content:
              "https://seekers-web-backup.s3.ap-southeast-1.amazonaws.com/seekers_meta_banner.jpg",
          },
        ],
        link: [
          { rel: "icon", type: "image/x-icon", href: "/favicon.ico" },
          {
            rel: "canonical",
            href: "https://seekers.my",
          },
        ],
        script: [
          // {
          //   innerHTML: `
          //   (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          //   new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          //   j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          //   'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          //   })(window,document,'script','dataLayer','GTM-WTPBQ9Q');
          //   `,
          //   type: "text/javascript",
          // },
        ],
      },
    },
    scripts: {
      registry: {
        googleTagManager: {
          id: "GTM-WTPBQ9Q",
        },
        googleAnalytics: {
          id: "G-ZF2FXW4L6J",
        },
      },
      // globals: [
      //   "/zcga.js",
      //   "https://onsite.optimonk.com/script.js?account=200776",
      // ],
      globals: {
        zcga: "/zcga.js",
        optimonk: "https://onsite.optimonk.com/script.js?account=200776",
      },
    },
    modules: [
      "@formkit/auto-animate/nuxt",
      "@nuxt/devtools",
      "@nuxt/scripts",
      "@nuxtjs/algolia",
      "@nuxtjs/device",
      "@nuxtjs/i18n",
      "@nuxtjs/partytown",
      "@nuxtjs/tailwindcss",
      "@vueuse/nuxt",
      "nuxt-build-cache",
      "nuxt-icon",
      "nuxt-calendly",
      "@nuxtjs/turnstile",
      [
        "@nuxtjs/google-fonts",
        {
          families: {
            Montserrat: {
              wght: [200, 300, 400, 600, 700],
            },
          },
        },
      ],
    ],
    i18n: {
      defaultLocale: "en",
      detectBrowserLanguage: false,
      locales: ["en", "ja", "zh"],
      strategy: "prefix_except_default",
    },
    algolia: {
      apiKey: process.env.ALGOLIA_API_KEY,
      applicationId: process.env.ALGOLIA_APP_ID,
    },
    turnstile: {
      // siteKey: "1x00000000000000000000BB", // alwasy passes
      // siteKey: "2x00000000000000000000BB", // always blocks
      siteKey: "0x4AAAAAABeBswJ-93FEDtwW", // actual key
      addValidateEndpoint: true,
    },
    // $production: {
    //   turnstile: {
    //     siteKey: "0x4AAAAAABeBswJ-93FEDtwW",
    //     addValidateEndpoint: true,
    //   },
    // },
    runtimeConfig: {
      algoliaApiKey: process.env.ALGOLIA_API_KEY,
      algoliaAppId: process.env.ALGOLIA_APP_ID,
      public: {
        baseUrl: process.env.BASEURL || "BASE-URL-MISSING",
        blogApiKey: process.env.BLOG_API_KEY || "BLOG-API-KEY-MISSING",
        blogApiUrl: process.env.BLOG_API_URL || "BLOG-API-URL-MISSING",
        devMode: process.env.DEV_MODE ? "development" : "production",
      },
      turnstile: {
        secretKey: process.env.NUXT_TURNSTILE_SECRET_KEY,
      },
    },
  });
};
