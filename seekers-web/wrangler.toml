main = "./.output/server/index.mjs"
assets = { directory = "./.output/public/", binding = "ASSETS" }
workers_dev = true
compatibility_date = "2025-01-27"

[observability.logs]
enabled = true

[env.production]
name = "seekers-web"
routes = [
  { pattern = "seekers.my/*", zone_name = "seekers.my" }
]

[env.beta]
name = "seekers-web-beta"
routes = [
   { pattern = "beta.seekers.my/*", zone_name = "seekers.my" }
]

[env.dev]   
name = "seekers-web-dev"
routes = [
   { pattern = "dev.seekers.my/*", zone_name = "seekers.my" }
]