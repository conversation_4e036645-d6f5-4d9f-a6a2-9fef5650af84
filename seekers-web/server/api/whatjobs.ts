export default defineEventHandler(async (event) => {
  let jobs: any = [];
  let data = `<?xml version="1.0" encoding="UTF-8"?>`;
  data += `<jobs xmlns="https://whatjobs.com/XMLSchema"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="https://whatjobs.com/XMLSchema https://whatjobs.com/XMLSchema.xsd ">`;
  try {
    // Get Data from Backend
    jobs = await (
      await fetch("https://backend.seekers.my/api/v1/s3kr3t4dm1n/whatjobs")
    ).json();
  } catch (err) {
    return err;
  } finally {
    for (let i = 0; i < jobs.length; i++) {
      data += `<job>
  <id><![CDATA[${jobs[i].id}]]></id>
  <url><![CDATA[https://seekers.my/jobfinder/jobs/${jobs[i].slug}]]></url>
  <title><![CDATA[${jobs[i].title}]]></title>
  <location><![CDATA[${
    jobs[i].city ? jobs[i].city : "Kuala Lumpur"
  }]]></location>
  <salary><![CDATA[${jobs[i].salary_max}]]></salary>
  <category><![CDATA[${jobs[i].industry.name}]]></category>
  <desc><![CDATA[${jobs[i].responsibility}]]></desc>
  <company-name><![CDATA[${jobs[i].company.name}]]></company-name>
  </job>`;
    }

    setHeaders(event, { "Content-Type": "application/xml" });
    data += `</jobs>`;
    return data;
  }
});
