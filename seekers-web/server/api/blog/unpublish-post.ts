import algoliasearch from "algoliasearch";

export default defineEventHandler(async (event) => {
  const algoliaAppId = useRuntimeConfig().algoliaAppId;
  const algoliaApiKey = useRuntimeConfig().algoliaApiKey;

  const body = await readBody(event);

  // Connect and authenticate with your Algolia app
  const client = algoliasearch(algoliaAppId, algoliaApiKey);
  const index = client.initIndex("seekers_ghost_blog");
  index.deleteObject(body.post.current.id).wait();
  return `${body.post.current.id} deleted`;
});
