import algoliasearch from "algoliasearch";

export default defineEventHandler(async (event) => {
  const algoliaAppId = useRuntimeConfig().algoliaAppId;
  const algoliaApiKey = useRuntimeConfig().algoliaApiKey;

  const body = await readBody(event);

  if (body.post.current.tags.some((tag: any) => tag.slug === "hash-hidden")) {
    return "hidden. not added";
  }

  // Connect and authenticate with your Algolia app
  const client = algoliasearch(algoliaAppId, algoliaApiKey);

  // Format the data to be indexed
  const record = {
    objectID: body.post.current.id,
    title: body.post.current.title,
    excerpt: body.post.current.excerpt,
    url: body.post.current.url,
  };

  const index = client.initIndex("seekers_ghost_blog");
  index.saveObject(record).wait();
  return "done";
});
