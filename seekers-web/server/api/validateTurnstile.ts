export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const headers = getHeaders(event);
  // console.log("body", body);

  const formData = await readFormData(event);

  const token = formData.get("cf-turnstile-response");

  const tokenResponse = await verifyTurnstileToken(token);
  console.log("verify token response", tokenResponse);
  // return tokenResponse;

  if (!token) {
    throw createError({
      statusCode: 422,
      statusMessage: "Token not provided.",
    });
  } else {
    if (tokenResponse.success === true) {
      const response = await $fetch("https://crm.zoho.com/crm/WebToLeadForm", {
        method: "POST",
        body: formData,
      });

      // console.log("zoho response", response);
      return response;
    } else {
      throw createError({
        statusCode: 422,
        statusMessage: "Form not submitted",
      });
    }
  }
});
