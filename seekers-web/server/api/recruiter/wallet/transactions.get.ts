export default defineEventHandler(async (event) => {
  type Transaction = {
    id: number;
    type: "in" | "out";
    status: "completed" | "requested" | "rejected";
    amount: number;
    note: string | null;
    bank_name?: string | null;
    bank_no?: string | null;
    created_at: string;
    updated_at: string;
    transaction_ref?: string | null;
    transaction_date?: string | null;
    sentence: string | null;
    balance: number;
  };

  const backendUrl = useRuntimeConfig().public.baseUrl;
  try {
    const headers = getHeaders(event);

    const [inData, outData] = await Promise.all([
      (
        await fetch(`${backendUrl}/wallet_in`, {
          headers: { Authorization: headers.authorization as string },
        })
      ).json(),
      (
        await fetch(`${backendUrl}/wallet_out`, {
          headers: { Authorization: headers.authorization as string },
        })
      ).json(),
    ]);

    const output: Transaction[] = [];
    inData.forEach((income: any) => {
      output.push({
        id: income.id,
        status: "completed",
        amount: income.amount,
        type: "in",
        created_at: income.created_at,
        updated_at: income.updated_at,
        note: income.event.name,
        sentence: income.sentence ? income.sentence.replace("event", "") : null,
        balance: 0,
      });
    });
    outData.forEach((withdrawals: any) => {
      output.push({
        id: withdrawals.id,
        status: withdrawals.status,
        amount: withdrawals.amount,
        type: "out",
        created_at: withdrawals.created_at,
        updated_at: withdrawals.updated_at,
        note: withdrawals.note,
        bank_name: withdrawals.bank_name,
        bank_no: withdrawals.bank_no,
        transaction_ref: withdrawals.transaction_ref,
        transaction_date: withdrawals.transaction_date,
        sentence: withdrawals.sentence,
        balance: 0,
      });
    });

    // sort out accending updated_at
    output.sort((a, b) => {
      return (
        new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime()
      );
    });

    // calculate balance at each transaction
    output.forEach((transaction, index) => {
      if (index === 0) {
        transaction.balance = transaction.amount;
      } else {
        if (transaction.type === "in" && transaction.status === "completed") {
          transaction.balance = output[index - 1].balance + transaction.amount;
        } else if (
          transaction.type === "out" &&
          transaction.status === "completed"
        ) {
          transaction.balance = output[index - 1].balance - transaction.amount;
        } else {
          transaction.balance = output[index - 1].balance;
        }
      }
    });

    // sort out descending updated_at
    output.sort((a, b) => {
      return (
        new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      );
    });

    return output;
  } catch (error) {
    return error;
  }
});
