export default defineEventHandler(async (event) => {
  const backendUrl = useRuntimeConfig().public.baseUrl;
  try {
    const body = await readBody(event);
    const headers = getHeaders(event);
    const res = await (
      await fetch(`${backendUrl}/wallet_withdraw_request`, {
        method: "POST",
        body: { amount: body.amount } as any,
        headers: {
          Authorization: headers.authorization as string,
          "Content-Type": "application/form-data",
        },
      })
    ).json();

    return res;
  } catch (error) {
    return error;
  }
});
