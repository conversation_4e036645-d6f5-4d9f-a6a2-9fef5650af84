export default defineEventHandler(async (event) => {
  const backendUrl = useRuntimeConfig().public.baseUrl;
  try {
    const data = await (
      await fetch(`${backendUrl}/job/${event.context.params?.job_slug}`)
    ).json();

    if (data.job_condition) {
      Object.keys(data.job_condition).forEach((key) => {
        if (data.job_condition[key] === "null") {
          data.job_condition[key] = null;
        }
      });
    }

    if (data.company) {
      Object.keys(data.company).forEach((key) => {
        if (data.company[key] === "null") {
          data.company[key] = null;
        }
      });
    }

    return data;
  } catch (error) {
    console.error(error);
  }
});
