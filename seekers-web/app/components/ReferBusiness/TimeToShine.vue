<template>
  <div id="inquire-today-hire-tomorrow" class="bg-[#202020] text-white py-16">
    <div
      class="md:max-w-7xl md:mx-auto grid lg:grid-cols-[2fr_5fr] gap-24 items-center text-center mx-4 relative"
    >
      <div class="grid gap-6 text-start">
        <div class="flex items-center gap-2">
          <div class="w-[23%] md:w-[33%] border-t border-[#D8D8D8]"></div>
          <p class="font-semibold text-[#F8F8F8]">
            How it<span class="text-[#FF3839] font-semibold"> Works?</span>
          </p>
        </div>
        <h2 class="md:text-2xl font-semibold">
          Got someone in mind?<br />It's your<span class="text-[#FF3839]">
            time to shine!</span
          >
        </h2>
        <p class="text-sm text-justify">
          Imagine earning cash rewards simply by connecting us with your
          contacts. We're here to spill the beans on<span class="font-semibold">
            how it works</span
          >, so you can start referring them to us right away. Get ready to
          unlock a world of opportunity and rewards.
        </p>
        <p class="text-sm">Let's dive in!</p>
        <nuxt-link
          to="#contact-us"
          class="btn bg-[#FF3839] border-[#FF3839] text-white w-fit"
          >Refer-A-Business</nuxt-link
        >
      </div>

      <div class="grid lg:grid-cols-3 gap-20">
        <div v-for="step in steps" :class="step.class">
          <img :src="step.img" class="mx-auto" />
          <p class="font-semibold mt-4 mb-1">{{ step.step }}</p>
          <p class="text-xs">{{ step.action }}</p>
        </div>
      </div>
      <div
        class="hidden lg:flex justify-center items-center lg:gap-24 xl:gap-36 absolute lg:right-[0] lg:top-[25%] lg:w-[70%] xl:w-max xl:top-[20%] xl:right-[16%] 2xl:top-[20%]"
      >
        <div v-for="arrow in arrows" :class="arrow.divClass">
          <img :src="arrow.arrow" alt="" :class="arrow.imgClass" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const steps = [
  {
    class: "md:mb-12",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688901793/website/refer-a-business/connect-share_w4lkqq.png",
    step: "Connect & Share",
    action:
      "Fill out our simple referral form and share your contact's details with us. We'll take it from there!",
  },
  {
    class: "md:mt-12",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688901793/website/refer-a-business/fast-evaluation_fhsgdw.png",
    step: "Fast Evaluation",
    action:
      " Our expert team quickly evaluates the eligibility of referred companies. We waste no time in assessing their potential.",
  },
  {
    class: "md:mb-12",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688901793/website/refer-a-business/get-rewards_o0rbde.png",
    step: "Get Your Rewards",
    action:
      "Once the referred company is approved, you'll be rewarded up to RM5,000 with cash straight to your pocket. It's that easy to reap the benefits of your valuable connections!",
  },
];

const arrows = [
  {
    divClass: "lg:mb-[3.5rem] lg:ml-[4.5rem] xl:mb-16 2xl:mb-16",
    imgClass: "lg:w-[80%] xl:w-[100%]",
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688904738/website/refer-a-business/arrow-1_c4gsf2.png",
  },
  {
    divClass: "lg:mt-[3.5rem] xl:mt-16 2xl:mt-16",
    imgClass: "lg:w-[80%] xl:w-[100%]",
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688904738/website/refer-a-business/arrow-2_dwhro9.png",
  },
];
</script>
