<template>
  <div>
    <div
      v-if="isLoading"
      class="md:grid md:grid-cols-3 md:mx-auto md:max-w-7xl mb-4 animate-pulse"
    >
      <CompanyCard v-for="company in showPerPage" :company="{}" />
    </div>
    <div v-if="!isLoading && companiesList?.length == 0" class="text-center">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1673322682/website/candidate/undraw_void_-3-ggu_btd4h6.svg"
        class="w-1/3 mx-auto animate__animated animate__fadeIn py-8"
      />
      <p>No Companies found. Try a different keyword or a broader search.</p>
      <a href="/company">
        <button class="btn btn-accent mt-8 px-12">Reset Search</button>
      </a>
    </div>
    <div
      v-else
      class="md:grid lg:grid-cols-2 xl:grid-cols-3 md:mx-auto md:max-w-7xl mb-4 animate__animated animate__fadeIn"
    >
      <CompanyCard v-for="company in companiesList" :company="company" />
    </div>
  </div>
</template>

<script setup>
// const { companiesList } = defineProps({
//   companiesList: Array,
//   isLoading: {
//     type: Boolean,
//     default: true,
//   },
//   showPerPage: {
//     type: Number,
//     default: 3,
//   },
// });

// type propsType = {
//   companiesList: Company[] | null;
//   isLoading: boolean;
//   showPerPage?: number;
//   showPagination: boolean;
//   lastPage?: number;
// }

const props = defineProps({
  companiesList: Array,
  isLoading: {
    type: Boolean,
    default: true,
  },
  showPerPage: {
    type: Number,
    default: 3,
  },
  // showPagination: true,
  // lastPage: 10
});
</script>
