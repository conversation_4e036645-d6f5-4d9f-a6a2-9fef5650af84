<template>
  <div class="md:max-w-screen-2xl md:mx-auto my-40 mx-4">
    <div class="text-center">
      <h3 class="text-3xl mb-2">Recruiter Insight</h3>
      <p>
        We guide freelance recruiters to find and hire specialist talents across
        different digital and technological fields of expertise.
      </p>
    </div>
    <div class="grid md:grid-cols-4 gap-12 items-center my-12">
      <div v-for="insight in insights">
        <img :src="insight.img" :alt="insight.img + 'image'" />
        <nuxt-link
          :to="insight.to"
          class="btn btn-primary rounded-full flex w-1/2 mx-auto my-4"
          >{{ insight.button }}
          <Icon name="heroicons:arrow-down-tray-20-solid" class="ml-2" />
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script setup>
const insights = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675158786/website/sharing-knowledge%20page/insight-1_ab7goo.jpg",
    button: "Download",
    to: "#",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675158786/website/sharing-knowledge%20page/insight-2_kjcyub.jpg",
    button: "Download",
    to: "#",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675158786/website/sharing-knowledge%20page/insight-3_ikzzje.jpg",
    button: "Download",
    to: "#",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675158786/website/sharing-knowledge%20page/insight-4_edle6l.jpg",
    button: "Download",
    to: "#",
  },
];
</script>
