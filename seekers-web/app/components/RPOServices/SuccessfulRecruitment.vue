<template>
  <div
    id="successful-recruitment"
    class="max-w-screen-xl md:mx-auto mx-4 text-center mb-12 my-24 relative"
  >
    <div class="grid gap-3 mb-6">
      <h2 class="text-[#8119D2] text-sm font-semibold">
        COMPREHENSIVE PROCESS COVERAGE FOR
      </h2>
      <p class="text-3xl">Successful Recruitment</p>
    </div>

    <!-- steps desktop -->
    <div id="steps" class="mt-12 hidden lg:block">
      <!-- 1 to 5 -->
      <div class="grid md:grid-cols-3 lg:grid-cols-5 gap-16 mx-4">
        <div
          v-for="(process, index) in oneToFive"
          class="p-[1px] rounded-2xl"
          :class="process.borderClass"
        >
          <div
            class="grid relative justify-center items-center bg-white rounded-2xl p-4 h-[215px]"
          >
            <img :src="process.img" class="mx-auto w-[123px] h-[123px]" />
            <div class="flex items-center gap-2 md:gap-0">
              <div
                class="h-6 w-6 bg-[#8119D2] rounded-full flex justify-center items-center"
              >
                <span class="text-white text-xs font-semibold">
                  {{ process.no }}
                </span>
              </div>
              <p class="font-semibold text-sm w-fit">
                {{ process.process }}
              </p>
            </div>

            <img
              v-if="index != 4"
              src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691068707/website/rpo-services/guide-right_rxxznj.svg"
              class="absolute top-1/2 -translate-y-[50%] -right-12"
            />

            <img
              v-if="index == 4"
              src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691072016/website/rpo-services/arrow-long-down_ee74sa.svg"
              class="absolute left-1/2 -translate-x-[50%] -bottom-12"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 6 to 10 -->
    <div
      class="md:grid-cols-3 lg:grid-cols-5 gap-16 mx-4 md:mt-12 hidden lg:grid"
    >
      <div
        v-for="(process, index) in sixToTen"
        class="p-[1px] rounded-2xl"
        :class="process.classes"
      >
        <div
          class="grid relative justify-center items-center bg-white rounded-2xl p-4"
        >
          <img :src="process.img" class="mx-auto w-[123px] h-[123px]" />
          <div class="flex items-center gap-2 md:gap-0">
            <div
              class="h-6 w-6 bg-[#8119D2] rounded-full flex justify-center items-center"
            >
              <span class="text-white text-xs font-semibold">
                {{ process.no }}
              </span>
            </div>
            <p class="font-semibold text-sm w-fit">
              {{ process.process }}
            </p>
          </div>
          <img
            v-if="index != 4"
            src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691073460/website/rpo-services/guide-left_rhzat8.svg"
            class="absolute top-1/2 -translate-y-[50%] -right-12"
          />
        </div>
      </div>
    </div>

    <!-- steps md size screen -->
    <div id="steps-mobile">
      <div class="grid sm:grid-cols-2 md:grid-cols-3 lg:hidden gap-16 mx-4">
        <div
          v-for="process in responsiveFromMobileToMd"
          class="p-[1px] rounded-2xl"
          :class="process.borderClass"
        >
          <div
            class="grid justify-center items-center bg-white rounded-2xl p-4 h-[215px]"
          >
            <img :src="process.img" class="mx-auto w-[123px] h-[123px]" />
            <div class="flex items-center gap-2">
              <div
                class="h-6 w-6 bg-[#8119D2] rounded-full flex justify-center items-center"
              >
                <span class="text-white text-xs font-semibold">
                  {{ process.no }}
                </span>
              </div>
              <p class="font-semibold text-sm w-fit">
                {{ process.process }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const oneToFive = [
  {
    borderClass: "bg-gradient-to-b from-[#8119D2]/60 to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/1-drafting-job_1_dvxghf.gif",
    no: "1",
    process: "Drafting job descriptions",
  },
  {
    borderClass: "bg-gradient-to-b from-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048595/website/rpo-services/2-design-hiring_1_blvtal.gif",
    no: "2",
    process: "Design hiring process",
  },
  {
    borderClass: "bg-gradient-to-b from-[#8119D2]/60 to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048595/website/rpo-services/3-plan-hiring_1_yy8mes.gif",
    no: "3",
    process: "Planning hiring schedules",
  },
  {
    borderClass: "bg-gradient-to-b from-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/4-recruitment-plan_1_xkvx8d.gif",
    no: "4",
    process: "Recruitment Marketing Plan",
  },
  {
    borderClass: "bg-gradient-to-b from-[#8119D2]/60 to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1690967432/website/rpo-services/5-hiring-partner_1_jdva7b.gif",
    no: "5",
    process: "Managing job advertisements and portals",
  },
];

const sixToTen = [
  {
    classes: "order-5 lg:order-1",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048597/website/rpo-services/10-job-offers_1_mh91rk.gif",
    no: "10",
    process: "Negotiating Job Offers",
  },
  {
    classes: "bg-gradient-to-b from-[#8119D2]/60 to-white order-4 lg:order-2",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/9-schedule-interview_1_qpxwey.gif",
    no: "9",
    process: "Scheduling Interviews",
  },
  {
    classes: "order-3",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/8-manage-resumes_1_amqjvj.gif",
    no: "8",
    process: "Managing Resumes",
  },
  {
    classes: "bg-gradient-to-b from-[#8119D2]/60 to-whit order-2 lg:order-4",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/7-screen-applicants_1_q5vjse.gif",
    no: "7",
    process: "Screening applicants",
  },
  {
    classes: "order-1 lg:order-5",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/6-collect-candidates_1_pkqxqb.gif",
    no: "6",
    process: "Collecting a pool of candidates",
  },
];

const responsiveFromMobileToMd = [
  {
    borderClass: "bg-gradient-to-b from-[#8119D2]/60 to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/1-drafting-job_1_dvxghf.gif",
    no: "1",
    process: "Drafting job descriptions",
  },
  {
    borderClass: "bg-gradient-to-b from-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048595/website/rpo-services/2-design-hiring_1_blvtal.gif",
    no: "2",
    process: "Design hiring process",
  },
  {
    borderClass:
      "bg-gradient-to-b from-[#8119D2]/60 to-white sm:from-white md:from-[#8119D2]/60 md:to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048595/website/rpo-services/3-plan-hiring_1_yy8mes.gif",
    no: "3",
    process: "Planning hiring schedules",
  },
  {
    borderClass:
      "bg-gradient-to-b from-white sm:bg-gradient-to-b sm:from-[#8119D2]/60 sm:to-white md:from-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/4-recruitment-plan_1_xkvx8d.gif",
    no: "4",
    process: "Recruitment Marketing Plan",
  },
  {
    borderClass: "bg-gradient-to-b from-[#8119D2]/60 to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1690967432/website/rpo-services/5-hiring-partner_1_jdva7b.gif",
    no: "5",
    process: "Managing job advertisements and portals",
  },
  {
    borderClass: "",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/6-collect-candidates_1_pkqxqb.gif",
    no: "6",
    process: "Collecting a pool of candidates",
  },
  {
    borderClass:
      "bg-gradient-to-b from-[#8119D2]/60 to-white sm:from-white md:from-[#8119D2]/60 md:to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/7-screen-applicants_1_q5vjse.gif",
    no: "7",
    process: "Screening applicants",
  },
  {
    borderClass:
      "bg-gradient-to-b from-white sm:bg-gradient-to-b sm:from-[#8119D2]/60 sm:to-white md:from-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/8-manage-resumes_1_amqjvj.gif",
    no: "8",
    process: "Managing Resumes",
  },
  {
    borderClass: "bg-gradient-to-b from-[#8119D2]/60 to-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048596/website/rpo-services/9-schedule-interview_1_qpxwey.gif",
    no: "9",
    process: "Scheduling Interviews",
  },
  {
    borderClass: "",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691048597/website/rpo-services/10-job-offers_1_mh91rk.gif",
    no: "10",
    process: "Negotiating Job Offers",
  },
];
</script>
