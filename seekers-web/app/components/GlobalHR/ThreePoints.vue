<template>
  <div
    id="three-points"
    class="-translate-y-12 md:-translate-y-24 lg:-translate-y-32 xl:-translate-y-36"
  >
    <div
      v-if="locale == 'en'"
      class="grid md:grid-cols-3 gap-4 max-w-screen-xl md:mx-auto mx-4 shadow-lg rounded-md p-6"
    >
      <div
        v-for="item in itemsEn"
        class="grid justify-items-center items-center text-center pt-8 pb-8"
      >
        <div class="rounded-full bg-[#BB2033]/20 mb-4">
          <img :src="item.img" alt="" class="h-20 w-20 p-4" />
        </div>
        <p class="text-xl font-semibold">
          {{ item.title }}
        </p>
        <p class="text-lg">{{ item.text }}</p>
      </div>
    </div>
    <div
      v-if="$i18n.locale == 'ja'"
      class="grid md:grid-cols-3 gap-4 max-w-screen-xl md:mx-auto mx-4 shadow-lg rounded-md p-6"
    >
      <div
        v-for="item in itemsJa"
        class="grid justify-items-center items-center text-center pt-8 pb-8"
      >
        <div class="rounded-full bg-[#D4E1F6] mb-4">
          <img :src="item.img" alt="" class="h-20 w-20 p-4" />
        </div>
        <p class="text-xl font-semibold">
          {{ item.title }}
        </p>
        <p class="text-md">{{ item.text }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { locale } = useI18n();

const itemsEn = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678222935/website/global-hr%20page/worldwide-employment_vmxs29.svg",
    title: "Worldwide Recruitment",
    text: "Let's get you set up to hire your remote team globally.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678222968/website/global-hr%20page/hr-management_tubstv.svg",
    title: "HR Management",
    text: "Our global-first solution manages HR for your entire team and every worker.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678223014/website/global-hr%20page/global-payroll_ipm6v0.svg",
    title: "Global Payroll",
    text: "Pay all employees promptly and in their respective local currencies.",
  },
];

const itemsJa = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678222935/website/global-hr%20page/worldwide-employment_vmxs29.svg",
    title: "グローバル採用",
    text: "海外で法人設立せずにグローバル人材雇用が可能に",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678222968/website/global-hr%20page/hr-management_tubstv.svg",
    title: "雇用手続き",
    text: "現地での面倒な給与支払いや納税、社保はお任せ",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678223014/website/global-hr%20page/global-payroll_ipm6v0.svg",
    title: "給与支払い",
    text: "人材の採用から毎月の手続きまで全ておまかせ",
  },
];
</script>
