<template>
  <div id="global-hr-reach-out" class="max-w-5xl mx-auto p-12">
    <div class="">
      <h2
        class="font-semibold text-4xl text-center mb-6"
        :class="locale == 'en' ? 'text-[#BB2033]' : 'text-[#1967D2]'"
      >
        {{ t("h2Title") }}
      </h2>
      <p class="text-center font-semibold mb-12">
        {{ t("paragraph") }}
      </p>
    </div>

    <CustomZohoFormGlobalHrEng v-if="locale == 'en'" />
    <CustomZohoFormGlobalHrJpn v-if="locale == 'ja'" />
  </div>
</template>

<script setup>
const { t, locale } = useI18n({
  useScope: "local",
});
</script>

<i18n lang="json">
{
  "en": {
    "h2Title": "Contact Us For FREE Consultation",
    "paragraph": "If you are considering hiring global talent, please feel free to contact us."
  },
  "ja": {
    "h2Title": "まずは無料相談をする",
    "paragraph": "グローバル人材の採用を検討されている方は是非お気軽にお問い合わせください。"
  }
}
</i18n>
