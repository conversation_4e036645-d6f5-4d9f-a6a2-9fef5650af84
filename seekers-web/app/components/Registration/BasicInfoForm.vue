<template>
  <div>
    <FormKit
      ref="basicInfoForm"
      type="form"
      id="basicInfoForm"
      v-model="formData"
      :actions="false"
      :form-class="'hide'"
      submit-label="Next"
      @submit="$emit('submit', formData)"
      :incomplete-message="false"
      :submit-attrs="{
        inputClass: 'btn btn-primary ',
        outerClass: 'max-w-xl mx-auto text-center',
      }"
    >
      <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
        <FormKit
          type="text"
          name="name"
          label="Full name (as in IC/Passport)"
          validation="required|length:5"
          placeholder="<PERSON>"
        />
        <FormKit
          type="email"
          name="email"
          label="Email address"
          validation="required|email"
          placeholder="<EMAIL>"
        />
        <FormKit type="group" name="password_group">
          <FormKit
            type="password"
            name="password"
            value=""
            label="Password"
            help="Enter a new password"
            validation="required|length:8"
            validation-visibility="dirty"
          />
          <FormKit
            type="password"
            name="password_confirm"
            label="Confirm password"
            help="Confirm your new password"
            validation="required|confirm"
            validation-visibility="dirty"
            validation-label="Password confirmation"
          />
        </FormKit>
        <FormKit
          type="tel"
          name="mobile"
          label="Phone number"
          placeholder="+6011-44448888"
          validation="required|length:11,13"
          :validation-messages="{
            matches: 'Phone number must be in the format +xxxxxxxxxx',
          }"
          validation-visibility="dirty"
        />

        <FormKit
          type="number"
          label="Year of Birth"
          name="birth_year"
          validation="required|min:1950|max:2002"
          placeholder="1999"
          step="1"
        />

        <FormKit
          type="select"
          name="is_local"
          label="Nationality"
          validation="required"
          placeholder="Malaysian"
          :value="1"
          :classes="{ selectIcon: 'px-2' }"
        >
          <option :value="1">Malaysian</option>
          <option :value="0">Expatriate</option>
        </FormKit>

        <FormKit
          type="select"
          name="state"
          label="State"
          validation="required|length:4"
          placeholder="Kuala Lumpur"
          :value="'Kuala Lumpur'"
          :options="statesOptions"
        />

        <FormKit
          type="select"
          label="Gender"
          name="gender"
          :options="[
            {
              value: 'female',
              label: 'Female',
            },
            {
              value: 'male',
              label: 'Male',
            },
          ]"
        />
      </div>
      <p class="text-xs text-center mt-8">
        By pressing next, you agree on Seekers's Privacy Statement, Terms &
        Conditions
      </p>
    </FormKit>
    <button
      class="btn btn-primary w-full"
      @click="(basicInfoForm as any).node.submit()"
    >
      Next
    </button>
  </div>
</template>

<script lang="ts" setup>
const emit = defineEmits(["submit"]);

const props = defineProps<{
  initialData?: BasicInfoForm;
}>();
const basicInfoForm = ref(null);
const statesOptions = useHelper.malaysianStates;
const formData = ref<BasicInfoForm | undefined>(props.initialData);
</script>
