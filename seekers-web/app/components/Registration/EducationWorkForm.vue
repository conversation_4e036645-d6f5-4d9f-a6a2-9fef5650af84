<template>
  <div>
    <FormKit
      ref="educationWorkForm"
      type="form"
      id="educationWorkForm"
      v-model="formData"
      :actions="false"
      :form-class="'hide'"
      submit-label="Next"
      @submit="$emit('submit', formData)"
      :incomplete-message="false"
      :submit-attrs="{
        inputClass: 'btn btn-primary ',
        outerClass: 'max-w-xl mx-auto text-center',
      }"
    >
      <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
        <div id="education-background" class="flex flex-col gap-2">
          <h3 class="text-lg font-semibold">Education Background</h3>
          <!-- Qualification select -->
          <FormKit
            type="select"
            label="Qualification"
            name="education_qualification"
            :options="qualificationsList"
          />

          <!-- Field Of Study text -->
          <FormKit
            type="text"
            name="education_title"
            label="Field of Study"
            validation="required|length:4"
            placeholder="Bachelor of Computer Science"
          />
          <!-- School Name text -->
          <FormKit
            type="text"
            name="education_institute"
            label="Institute"
            validation="required|length:4"
            placeholder="University of Malaya"
          />
          <!-- Education Period -->
          <FormKit type="group" name="education_period">
            <div class="grid grid-cols-2 gap-x-2">
              <p class="col-span-2 mt-1 text-sm">Education Period</p>
              <FormKit
                data-form-type="other"
                type="date"
                name="start"
                label="From"
                validation="required  "
                validation-visibility="blur"
                :label-class="'text-xs'"
                :input-class="'mt-0'"
              />
              <FormKit
                data-form-type="other"
                type="date"
                name="end"
                label="To"
                validation="required"
                validation-visibility="blur"
                :label-class="'text-xs'"
                :input-class="'mt-0'"
              />
            </div>
          </FormKit>
          <!-- <div>
                  <p class="mb-1 mt-1 text-sm">
                    Are you actively looking for a job?
                  </p>
                  <FormKit
                    type="checkbox"
                    :label="
                      educationWorkForm?.open_to_work == 'false'
                        ? 'No, Not Open to Work'
                        : 'Yes, Open to Work'
                    "
                    name="open_to_work"
                    :value="true"
                    :classes="{
                      outer: 'w-full',
                      wrapper: 'flex items-center border rounded-lg p-2',
                      inner: 'p-2',
                    }"
                  />
                </div> -->
        </div>
        <div id="work-background" class="flex flex-col gap-2">
          <h3 class="text-lg font-semibold mt-8 lg:mt-0">
            Latest Working Experience
          </h3>
          <FormKit
            type="text"
            name="work_role"
            label="Designation"
            validation="required|length:4"
            placeholder="Senior Manager"
          />
          <FormKit
            type="text"
            name="work_company_name"
            label="Employer"
            validation="required|length:5"
            placeholder="Seekers Sdn Bhd"
          />
          <FormKit
            type="text"
            name="work_responsibilities"
            label="Relevant Job Experience"
            validation="required|length:10"
            placeholder="Managing team, Programming, Sales"
          />
          <!-- Working Period -->
          <FormKit type="group" name="working_period">
            <div class="grid grid-cols-2 gap-x-2">
              <p class="col-span-2 mt-1 text-sm">Working Period</p>
              <FormKit
                type="date"
                name="start"
                label="From"
                format="DD/MM/YYYY"
                validation="required"
                validation-visibility="blur"
                :label-class="'text-xs'"
                :input-class="'mt-0'"
              />
              <FormKit
                type="date"
                name="end"
                label="To"
                validation="required"
                validation-visibility="blur"
                :label-class="'text-xs'"
                :input-class="'mt-0'"
              />
            </div>
          </FormKit>
          <FormKit
            type="file"
            size
            name="cv"
            label="Upload Latest Resume (optional)"
            accept=".pdf"
            @change="checkFilesize"
            :classes="{
              noFiles: 'hidden',
              fileName: 'hidden',
              fileRemove: 'hidden',
            }"
          />
        </div>
      </div>
      <p class="text-xs text-center mt-8">
        By pressing next, you agree on Seekers's Privacy Statement, Terms &
        Conditions
      </p>
    </FormKit>
    <button
      class="btn btn-primary w-full"
      @click="(educationWorkForm as any).node.submit()"
    >
      Next
    </button>
  </div>
</template>

<script lang="ts" setup>
import Swal from "sweetalert2";
const qualificationsList = useHelper.qualificationsList;

const emit = defineEmits(["submit"]);
const props = defineProps<{
  initialData?: EducationWorkForm;
}>();
const educationWorkForm = ref(null);
const formData = ref<EducationWorkForm | undefined>(props.initialData);

function checkFilesize(e: any) {
  const file = e.target.files[0];
  if (file.size > 2097152) {
    Swal.fire({
      title: "File too large",
      html: "<p>Please upload a file less than 2MB.</p><p class='text-sm text-accent'>Try using <a class='text-blue font-semibold' style='color: rgb(29 78 216);' href='https://smallpdf.com/compress-pdf' target='_blank'>CompressPDF</a><p>",
      icon: "info",
      confirmButtonText: "OK",
    });
    e.target.value = "";
    (formData as any).value.cv = [];
  }
}
</script>
