<template>
  <div
    class="flex flex-col md:flex-row justify-center items-center gap-8 md:gap-20 p-8 my-4"
  >
    <div v-for="stat in stats">
      <h3 class="text-3xl font-semibold text-primary text-center">
        {{ stat.number }}
      </h3>
      <p class="text-sm mt-2">{{ stat.details }}</p>
    </div>
  </div>

  <!-- ABOUT-->
  <div class="p-10 my-4">
    <h3 class="text-primary font-semibold text-2xl mb-4">
      About Seekers Malaysia
    </h3>
    <p class="text-sm">
      Established in 2014, Seekers is developed by Seekers Technology Sdn. Bhd.,
      and operated by the HR licensed subsidiary Agensi Pekerjaan Job Search
      Asia Sdn. Bhd. They aim to resolve the root issues of Human Resources
      through their Headhunting Technology.
    </p>
  </div>
</template>

<script setup>
const stats = [
  {
    number: "500+",
    details: "Over 500+ open job positions",
  },
  {
    number: "5,000+",
    details: "Over 5,000+ active clients",
  },
  {
    number: "25,000+",
    details: "Over 25,000 active recruiters",
  },
];
</script>
