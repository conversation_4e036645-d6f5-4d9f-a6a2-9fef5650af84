<template>
  <div class="flex w-full">
    <div
      id="candidate-sidebar"
      class="flex flex-col gap-2 bg-base-neutral text-gray-500 border-r border-gray-200 lg:p-14"
    >
      <div class="sticky top-20">
        <div
          class="lg:hidden cursor-pointer p-3 pb-0"
          @click="sidebarExpanded = !sidebarExpanded"
        >
          <Icon
            v-if="!sidebarExpanded"
            name="heroicons:chevron-double-right-20-solid"
            class="w-5 h-5"
          />
          <Icon
            v-if="sidebarExpanded"
            name="heroicons:chevron-double-left-20-solid"
            class="w-5 h-5"
          />
        </div>

        <nuxt-link
          v-for="menu in candidateSideMenus"
          :to="menu.link"
          @click="collapseSidebar"
        >
          <div
            class="cursor-pointer p-3 grid items-center gap-3 lg:rounded-lg lg:px-8"
            :class="{
              'bg-success': $route.path === menu.link,
              'grid-cols-[1fr,5fr]': sidebarExpanded,
            }"
          >
            <Icon
              :name="menu.icon"
              class="w-5 h-5"
              :class="{ 'text-primary': $route.path === menu.link }"
            />
            <span
              class="text-xs animate__animated animate__fadeIn animate__faster"
              v-if="sidebarExpanded"
              >{{ menu.title }}</span
            >
          </div>
        </nuxt-link>
        <div
          class="cursor-pointer p-3 grid items-center gap-3 lg:rounded-lg lg:px-8"
          :class="{
            'grid-cols-[1fr,5fr]': sidebarExpanded,
          }"
          @click="logout"
        >
          <Icon
            name="heroicons:arrow-left-on-rectangle-solid"
            class="w-5 h-5"
          />
          <span
            class="text-xs animate__animated animate__fadeIn animate__faster"
            v-if="sidebarExpanded"
            >Logout</span
          >
        </div>
      </div>
    </div>
    <div class="w-full bg-info bg-opacity-50 md:p-12">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
const sidebarExpanded = useState(() => true);

function logout() {
  if (confirm("Are you sure you want to logout?") == true) {
    useRecruiter().logout();
  }
}

function collapseSidebar() {
  if (window.innerWidth < 1024) {
    sidebarExpanded.value = false;
  } else {
    sidebarExpanded.value = true;
  }
}

const candidateSideMenus = [
  {
    title: "Dashboard",
    icon: "heroicons:user",
    link: "/recruiter/dashboard/",
  },
  {
    title: "My Profile",
    icon: "heroicons:user",
    link: "/recruiter/dashboard/profile",
  },
  {
    title: "Digital / Technical Jobs",
    icon: "heroicons:newspaper",
    link: "/recruiter/dashboard/latest-jobs",
  },
  {
    title: "Recommended Jobs",
    icon: "heroicons:academic-cap",
    link: "/recruiter/dashboard/recommended-jobs",
  },
  {
    title: "All Jobs",
    icon: "heroicons:briefcase",
    link: "/recruiter/dashboard/jobs",
  },
  {
    title: "Candidates",
    icon: "heroicons:view-columns",
    link: "/recruiter/dashboard/candidates",
  },
  {
    title: "Wallet",
    icon: "heroicons:trophy",
    link: "/recruiter/dashboard/wallet",
  },
  {
    title: "Change Password",
    icon: "heroicons:key",
    link: "/recruiter/dashboard/change-password",
  },
];
</script>
