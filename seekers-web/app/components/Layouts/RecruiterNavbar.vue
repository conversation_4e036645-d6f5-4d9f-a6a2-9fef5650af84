<template>
  <header class="navbar bg-base-100 p-4 xl:px-8 h-20">
    <!-- logo -->
    <div class="">
      <NuxtLink to="/">
        <img
          src="@/static/seekers-logo.png"
          alt="logo"
          class="h-8 w-24 object-contain"
        />
      </NuxtLink>
    </div>

    <!-- desktop nav start -->
    <div class="hidden lg:block">
      <div
        v-for="link in menuLinks"
        :key="link.title"
        class="dropdown dropdown-hover"
      >
        <NuxtLink
          :to="link.to"
          tabindex="0"
          class="m-1 text-accent lg:text-sm lg:ml-6 xl:ml-10"
          >{{ link.title
          }}<Icon
            v-if="
              ['Employer', 'HR Trends', 'Referral Program'].includes(link.title)
            "
            name="heroicons:chevron-down-solid"
            class="h-4 w-4 ml-1"
        /></NuxtLink>
        <ul
          v-if="link.submenu"
          tabindex="0"
          class="dropdown-content bg-base-100 rounded z-[1] w-max shadow ml-8"
        >
          <li v-for="subItem in link.submenu" :key="subItem.title">
            <NuxtLink
              :to="subItem.to"
              class="text-accent text-sm lg:text-sm block p-3 hover:bg-gray-100"
              >{{ subItem.title }}</NuxtLink
            >
          </li>
        </ul>
      </div>
    </div>

    <!-- right buttons -->
    <div id="right-buttons" class="hidden lg:flex lg:gap-4 xl:gap-8 ml-auto">
      <NuxtLink v-if="!recruiterToken" to="/recruiter/login">
        <button
          type="button"
          class="hidden lg:block btn btn-accent btn-rounded lg:px-4 xl:px-6 py-2 h-full text-sm lg:text-xs xl:text-sm"
        >
          Login / Register
        </button>
      </NuxtLink>

      <NuxtLink v-else to="/recruiter/dashboard">
        <button
          type="button"
          class="hidden lg:block btn btn-accent btn-rounded lg:px-4 xl:px-6 py-2 h-full text-sm lg:text-xs xl:text-sm"
        >
          My Profile
        </button>
      </NuxtLink>

      <NuxtLink
        to="/employer"
        :class="route.path.includes('/employer') ? 'hidden' : 'block'"
      >
        <button
          type="button"
          class="btn btn-primary btn-rounded lg:px-4 xl:px-6 py-2 h-full text-sm lg:text-xs xl:text-sm"
        >
          Post Job
        </button>
      </NuxtLink>
    </div>

    <!-- mobile menu button -->
    <button class="ml-auto lg:hidden cursor-pointer">
      <Icon
        v-if="!showMobileMenu"
        name="heroicons:bars-3-bottom-right"
        class="h-6 w-6 animate__animated animate__fadeIn animate__faster"
        @click="showMobileMenu = !showMobileMenu"
      ></Icon>
      <Icon
        v-if="showMobileMenu"
        name="heroicons:x-mark"
        class="h-6 w-6 animate__animated animate__fadeIn animate__faster"
        @click="showMobileMenu = !showMobileMenu"
      ></Icon>
    </button>

    <div
      id="mobile-menu"
      v-if="showMobileMenu"
      class="bg-transparent flex flex-col w-full h-full absolute top-20 z-30 backdrop-blur lg:hidden"
      @click="showMobileMenu = false"
    >
      <NuxtLink
        :to="menuItem.link"
        id="mobile-menu-item"
        class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-[0.99]"
        v-for="menuItem in mobileMenuList"
        @click="showMobileMenu = false"
      >
        {{ menuItem.text }}
      </NuxtLink>
    </div>
  </header>
</template>

<script setup lang="ts">
const route = useRoute();
const showMobileMenu = useState(() => false);
const recruiterToken = useRecruiter().token;

const menuLinks = [
  { title: "Find Jobs", to: "/jobs" },
  { title: "Candidates", to: "/candidate/dashboard/profile" },
  { title: "Recruiter", to: "/recruiter" },
  {
    title: "Employer",
    to: "/employer",
    submenu: [
      { title: "Global HR", to: "/global-hr" },
      {
        title: "Hire Digital & Tech Talent",
        to: "/hire-digital-tech-talent",
      },
      { title: "RPO Services", to: "/rpo-services" },
    ],
  },
  {
    title: "HR Trends",
    to: "/hrtrends",
    submenu: [{ title: "Salary Guide", to: "/salary-guide" }],
  },
  {
    title: "Referral Program",
    to: "",
    submenu: [
      { title: "Refer-A-Business", to: "/refer-a-business" },
      { title: "Refer-A-Talent", to: "/refer-a-talent" },
    ],
  },
];

const mobileMenuList = [
  {
    text: "Find Jobs",
    link: "/jobs",
  },
  {
    text: "Candidates",
    link: "/candidate/dashboard/profile",
  },
  {
    text: "Recruiter",
    link: "/recruiter",
  },
  {
    text: "Employer",
    link: "/employer",
  },
  {
    text: "Global HR",
    link: "/global-hr",
  },
  {
    text: "Hire Digital & Tech Talent",
    link: "/hire-digital-tech-talent",
  },
  {
    text: "RPO Services",
    link: "/rpo-services",
  },
  {
    text: "HR Trends",
    link: "/hrtrends",
  },
  {
    text: "Salary Guide",
    link: "/salary-guide",
  },
  {
    text: "Refer-A-Business",
    link: "/refer-a-business",
  },
  {
    text: "Refer-A-Talent",
    link: "/refer-a-talent",
  },
];
</script>
