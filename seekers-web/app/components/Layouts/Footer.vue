<template>
  <footer aria-label="Site Footer" class="bg-accent text-white rounded-t-3xl">
    <div
      class="max-w-screen-xl px-4 py-16 mx-auto space-y-8 sm:px-6 lg:space-y-16 lg:px-8"
    >
      <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div>
          <img
            src="@/static/Seekers-Logo-White.png"
            alt="logo"
            class="max-h-12 object-contain"
          />

          <p class="max-w-xs mt-4 text-base-200 text-sm">
            Agensi Pekerjaan Tech Recruitment Sdn. Bhd.
            (201501031949(1157273-V))
          </p>
          <p class="max-w-xs mt-4 text-base-200 text-sm">
            R24 Level 6, Wisma UOA II, level 6, No.21, Jalan Pinang, 50450 Kuala
            Lumpur, Malaysia
          </p>
          <div>
            <a
              href="https://www.trustedmalaysia.com/best-recruitment-agencies-malaysia/"
              rel="noreferrer"
              target="_blank"
              class=""
            >
              <img
                src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1694704775/website/footer/Trusted-min_ygz3eg.png"
                class="w-auto h-14 mt-4"
              />
            </a>
          </div>
        </div>

        <div
          class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:col-span-2 lg:grid-cols-4"
        >
          <div class="md:col-start-2 md:col-end-3">
            <p class="font-bold text-xl">For Candidates</p>

            <nav aria-label="Footer Navigation - Services" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in candidateMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>

          <div class="md:cols-start-3 md:col-end-4">
            <p class="font-bold text-xl">For Employers</p>

            <nav aria-label="Footer Navigation - Company" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in employerMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                  :no-prefetch="menu.to.includes('srs')"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>

          <!-- <div>
            <p class="font-bold text-xl">Recruiter</p>

            <nav aria-label="Footer Navigation - Company" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in recruiterMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div> -->

          <div class="md:col-start-4 md:col-end-5">
            <p class="font-bold text-xl">About Us</p>

            <nav aria-label="Footer Navigation - Legal" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in otherMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>
        </div>
      </div>

      <div class="pt-8 mt-8 border-t border-gray-100">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <p class="text-xs text-left">
            Agensi Pekerjaan Tech Recruitment Sdn. Bhd.
            (201501031949(1157273-V))<br />© {{ new Date().getFullYear() }}. All
            rights reserved.
          </p>

          <nav aria-label="Footer Navigation - Social Media">
            <div class="flex gap-6 justify-end">
              <a
                v-for="link in socialLinks"
                :href="link.href"
                rel="noreferrer"
                target="_blank"
                class="text-neutral transition hover:opacity-75"
              >
                <span class="sr-only">{{ link.name }}</span>

                <Icon :name="link.icon" class="w-6 h-6" />
              </a>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
const candidateMenus = [
  {
    name: "Browse Jobs",
    to: "/jobs",
  },
  // {
  //   name: "Browse Categories",
  //   to: "/#popular-categories",
  // },
  {
    name: "Candidate Dashboard",
    to: "/candidate/dashboard",
  },
  {
    name: "Find Companies",
    to: "/company",
  },
  { name: "Salary Guide", to: "/salary-guide" },
];

const employerMenus = [
  // {
  //   name: "SRS Login",
  //   to: "https://srs.seekers.my/login",
  // },
  // {
  //   name: "SRS Register",
  //   to: "https://srs.seekers.my/registration",
  // },
  {
    name: "Employer",
    to: "/hire-digital-tech-talent",
  },
  {
    name: "Employer FAQ",
    to: "/employer/faq",
  },
  // { name: "Global HR", to: "/global-hr" },
  // { name: "HR Trends", to: "/hrtrends" },
  // { name: "Hire Digital & Tech Talent", to: "/hire-digital-tech-talent" },
  // { name: "Refer-A-Business", to: "/refer-a-business" },
  // { name: "Refer-A-Talent", to: "/refer-a-talent" },
  // { name: "RPO Services", to: "/rpo-services" },
];

const recruiterMenus = [
  {
    name: "Job List For Recruiter",
    to: "/recruiter/dashboard/jobs",
  },
  {
    name: "Dashboard",
    to: "/recruiter/dashboard",
  },
  {
    name: "Recommended Jobs",
    to: "/recruiter/dashboard/jobs",
  },
  {
    name: "Latest Jobs",
    to: "/recruiter/dashboard/jobs",
  },
  {
    name: "Recruiter Registration",
    to: "/recruiter/register",
  },
  {
    name: "Recruiter Login",
    to: "/recruiter/login",
  },
  {
    name: "Recruiter FAQ",
    to: "/recruiter/faq",
  },
];

const otherMenus = [
  {
    name: "About Seekers.my",
    to: "/about",
  },
  {
    name: "Privacy Policy",
    to: "/privacy",
  },
  {
    name: "Terms of Use",
    to: "/terms",
  },
  {
    name: "Contact Us",
    to: "/contact",
  },
];

const socialLinks = [
  {
    name: "Facebook",
    href: "https://www.facebook.com/seekers.malaysia",
    icon: "bx:bxl-facebook",
  },
  {
    name: "Twitter",
    href: "https://twitter.com/seekersmy?lang=en",
    icon: "bxl:twitter",
  },
  {
    name: "Instagram",
    href: "https://www.instagram.com/seekersmy/",
    icon: "bxl:instagram",
  },
  {
    name: "LinkedIn",
    href: "https://www.linkedin.com/company/seekersmy/",
    icon: "bx:bxl-linkedin",
  },
];
</script>
