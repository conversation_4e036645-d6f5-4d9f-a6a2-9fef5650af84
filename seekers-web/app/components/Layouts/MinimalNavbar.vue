<template>
  <header class="navbar bg-base-100 p-4 xl:px-8 h-20">
    <!-- logo -->
    <div class="">
      <NuxtLink to="/">
        <img
          src="@/static/seekers-logo.png"
          alt="logo"
          class="h-8 w-24 object-contain"
        />
      </NuxtLink>
    </div>

    <!-- desktop nav start -->
    <div class="hidden lg:block">
      <div
        v-for="link in menuLinks"
        :key="link.title"
        class="dropdown dropdown-hover"
      >
        <NuxtLink
          :to="link.to"
          tabindex="0"
          class="m-1 text-[#696969] font-bold lg:text-md lg:ml-20 xl:ml-24"
          >{{ link.title
          }}<Icon
            v-if="
              ['Employer', 'HR Trends', 'Referral Program'].includes(link.title)
            "
            name="heroicons:chevron-down-solid"
            class="h-4 w-4 ml-1"
        /></NuxtLink>
      </div>
    </div>

    <!-- right buttons -->
    <div id="right-buttons" class="hidden lg:flex lg:gap-4 xl:gap-8 ml-auto">
      <!-- candidate buttons -->

      <template v-if="isCandidateRoutes">
        <NuxtLink v-if="!candidateToken" to="/candidate/login">
          <button
            type="button"
            class="hidden lg:block btn btn-primary btn-rounded lg:px-4 xl:px-6 py-2 h-full text-sm lg:text-xs xl:text-sm"
          >
            Login / Register
          </button>
        </NuxtLink>

        <NuxtLink v-else to="/candidate/dashboard/profile">
          <button
            type="button"
            class="hidden lg:block btn btn-success btn-rounded lg:px-4 xl:px-6 py-2 h-full text-sm lg:text-xs xl:text-sm"
          >
            My Profile
          </button>
        </NuxtLink>
      </template>
    </div>

    <!-- mobile menu button -->
    <button class="ml-auto lg:hidden cursor-pointer">
      <Icon
        v-if="!showMobileMenu"
        name="heroicons:bars-3-bottom-right"
        class="h-6 w-6 animate__animated animate__fadeIn animate__faster"
        @click="showMobileMenu = !showMobileMenu"
      ></Icon>
      <Icon
        v-if="showMobileMenu"
        name="heroicons:x-mark"
        class="h-6 w-6 animate__animated animate__fadeIn animate__faster"
        @click="showMobileMenu = !showMobileMenu"
      ></Icon>
    </button>

    <div
      id="mobile-menu"
      v-if="showMobileMenu"
      class="bg-transparent flex flex-col w-full h-full absolute top-20 z-30 backdrop-blur lg:hidden"
      @click="showMobileMenu = false"
    >
      <NuxtLink
        :to="menuItem.link"
        id="mobile-menu-item"
        class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-[0.99]"
        v-for="menuItem in mobileMenuList"
        @click="showMobileMenu = false"
      >
        {{ menuItem.text }}
      </NuxtLink>
    </div>
  </header>
</template>

<script setup lang="ts">
const route = useRoute();
const showMobileMenu = useState(() => false);
const candidateToken = useCandidate().token;

const isCandidateRoutes = computed(
  () =>
    route.path.includes("/candidate") ||
    route.path === "/" ||
    route.path === "/jobs"
);

const menuLinks = [
  { title: "Jobs", to: "/jobs" },
  {
    title: "Employers",
    to: "/hire-digital-tech-talent",
  },
  {
    title: "Blogs",
    to: "https://seekers.my/blog/",
  },
];

const mobileMenuList = [
  {
    text: "Jobs",
    link: "/jobs",
  },
  {
    text: "Employer",
    link: "/hire-digital-tech-talent",
  },
  {
    text: "Blogs",
    link: "https://seekers.my/blog/",
  },
];
</script>
