<template>
  <div class="flex w-full">
    <div
      id="candidate-sidebar"
      class="flex flex-col gap-2 bg-base-neutral text-gray-500 border-r border-gray-200 lg:p-12 pb-20"
    >
      <div class="sticky top-20">
        <nuxt-link
          v-for="menu in candidateSideMenus"
          :to="menu.link"
          @click="collapseSidebar"
        >
          <div
            class="cursor-pointer p-3 grid items-center gap-3 lg:rounded-lg lg:px-8"
            :transition-style="
              $route.fullPath === menu.link ? 'in:circle:top-left' : ''
            "
            :class="{
              'bg-success': $route.fullPath === menu.link,
              'grid-cols-[1fr,5fr]': sidebarExpanded,
            }"
          >
            <Icon
              :name="menu.icon"
              class="w-5 h-5"
              :class="{ 'text-primary': $route.fullPath === menu.link }"
            />
            <span
              class="text-xs animate__animated animate__fadeIn animate__faster"
              v-if="sidebarExpanded"
              >{{ menu.title }}</span
            >
          </div>
        </nuxt-link>

        <div
          class="lg:hidden cursor-pointer p-3 flex"
          @click="
            sidebarExpanded
              ? (sidebarExpanded = null)
              : (sidebarExpanded = 'yes')
          "
        >
          <Icon
            v-if="!sidebarExpanded"
            name="heroicons:chevron-double-right-20-solid"
            class="w-6 h-6 text-primary"
          />
          <Icon
            v-if="sidebarExpanded"
            name="heroicons:chevron-double-left-20-solid"
            class="ml-auto w-6 h-6 text-primary animate-pulse"
          />
        </div>
        <CustomRadialProgress
          :percentage="profileCompletion || 0"
          class="mt-8 hidden lg:block"
        />
        <div class="p-3 lg:pt-8 lg:grid lg:justify-center">
          <button
            @click="logout"
            class=""
            :class="{
              'btn btn-accent grid-cols-[1fr,5fr]': sidebarExpanded,
            }"
          >
            <Icon
              v-if="!sidebarExpanded"
              name="heroicons:arrow-left-on-rectangle-solid"
              class="w-5 h-5"
            />
            <Icon
              v-if="sidebarExpanded"
              name="heroicons:arrow-left-on-rectangle-solid"
              class="w-5 h-5"
            />
            <span
              class="text-xs animate__animated animate__fadeIn animate__faster"
              v-if="sidebarExpanded"
              >Logout</span
            >
          </button>
        </div>
      </div>
    </div>
    <div class="w-full bg-info bg-opacity-50 min-h-[80vh] p-3 md:p-6 lg:p-12">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
const sidebarExpanded = useCookie("sidebarExpanded");

if (sidebarExpanded.value === null) {
  sidebarExpanded.value = "yes";
}

function collapseSidebar() {
  if (window.innerWidth < 1024) {
    sidebarExpanded.value = null;
  } else {
    sidebarExpanded.value = "yes";
  }
}

const logout = useCandidate().logout;
await useCandidate().syncProfile();
const profileCompletion = useCandidate().profileCompletion;

const candidateSideMenus = [
  {
    title: "My Profile",
    icon: "heroicons:user",
    link: "/candidate/dashboard/profile",
  },
  {
    title: "Applications",
    icon: "heroicons:newspaper",
    link: "/candidate/dashboard/application",
  },
  {
    title: "Education",
    icon: "heroicons:academic-cap",
    link: "/candidate/dashboard/education",
  },
  {
    title: "Work & Experience",
    icon: "heroicons:briefcase",
    link: "/candidate/dashboard/work-experience",
  },
  {
    title: "Portfolio",
    icon: "heroicons:view-columns",
    link: "/candidate/dashboard/portfolio",
  },
  {
    title: "License & Certification",
    icon: "heroicons:trophy",
    link: "/candidate/dashboard/license-certification",
  },
  {
    title: "Saved jobs",
    icon: "heroicons:bookmark",
    link: "/candidate/dashboard/saved-jobs",
  },
  {
    title: "Following",
    icon: "heroicons:bell",
    link: "/candidate/dashboard/following",
  },
  {
    title: "Upload CV",
    icon: "heroicons:cog-6-tooth",
    link: "/candidate/dashboard/settings",
  },
  {
    title: "Change Password",
    icon: "heroicons:key",
    link: "/candidate/dashboard/change-password",
  },
];
</script>
