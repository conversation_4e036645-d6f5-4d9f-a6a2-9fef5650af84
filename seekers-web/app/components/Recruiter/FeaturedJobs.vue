<template>
  <section id="featured-jobs" class="bg-success bg-opacity-60 py-12">
    <div class="max-w-7xl md:mx-auto m-4">
      <h2 class="font-semibold">Featured Jobs</h2>

      <div class="flex md:items-center mb-8">
        <p class="md:mr-auto">
          Work as much or as little as you like, and you can choose projects
          that are significant for you.
        </p>
        <select class="select" v-model="activeDropdown">
          <option v-for="drop in drops" :value="drop">
            {{ drop }}
          </option>
        </select>
      </div>

      <JobCardsList
        v-if="activeDropdown == 'Featured'"
        :jobsList="highlightedJobs?.data"
        :isLoading="pendingHighlighted"
        :showPagination="false"
      />
      <JobCardsList
        v-if="activeDropdown == 'Recent'"
        :jobsList="latestJobs?.data"
        :isLoading="pendingLatest"
        :showPagination="false"
      />
      <JobCardsList
        v-if="activeDropdown == 'Popular'"
        :jobsList="latestTechJobs?.data"
        :isLoading="pendingTech"
        :showPagination="false"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
const { data: highlightedJobs, pending: pendingHighlighted } = useFetch<any>(
  "/api/jobs/highlighted?limit=6",{ key: "highlighted-jobs" }
);

const { data: latestJobs, pending: pendingLatest } =
  useFetch<any>("/api/jobs/latest",{ key: "latest-jobs" });
  
const { data: latestTechJobs, pending: pendingTech } = useFetch<any>(
  "/api/jobs/latest-tech",
  { key: "latest-tech" }
);

const activeDropdown = ref("Featured");
const drops = ["Featured", "Recent", "Popular"];
</script>
