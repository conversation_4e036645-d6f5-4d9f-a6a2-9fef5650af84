<template>
  <section id="freelance-recruiter">
    <div
      class="grid md:grid-cols-5 justify-items-center items-center max-w-7xl mx-auto p-12 my-24"
    >
      <div class="md:col-span-2">
        <h2 class="font-semibold text-4xl mb-8">Become Freelance Recruiter</h2>
        <p class="font-semibold mb-4">
          Here's the platform to begin your freelance recruitment journey
        </p>
        <ul class="mb-4">
          <li class="flex items-center mb-4">
            <span
              ><div class="h-10 w-10 rounded-full bg-pink-100 mr-2"></div
            ></span>
            Are you an experienced recruitment specialist or talent acquisition
            specialist?
          </li>
          <li class="flex items-center">
            <span
              ><div class="h-10 w-10 rounded-full bg-cyan-100 mr-2"></div
            ></span>
            Are you able to provide the best fit candidate that matches the
            hiring manager's requirement?
          </li>
        </ul>
        <nuxt-link
          to="recruiter/register"
          v-if="!auth"
          class="btn btn-primary w-max"
          >Register Now</nuxt-link
        >
        <nuxt-link
          to="/recruiter/dashboard"
          v-if="auth"
          class="btn btn-primary w-max"
          >Go to Dashboard</nuxt-link
        >
      </div>
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1671073800/website/recruiterpage/index/freelance-recruiter_xueq1r.png"
        class="md:col-span-3 mt-8 md:mt-0"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
const auth = useRecruiter().token;
</script>
