<template>
  <div id="latest-jobs-list" class="p-4 border rounded-lg bg-white h-full flex flex-col">
    <div class="rounded-t-md">
      <div class="flex p-4 justify-between items-center">
        <p class="text-sm font-semibold">{{ title }}</p>
        <div>
          <button
            class="btn btn-sm rounded-full"
            @click="navigateTo('/recruiter/dashboard/jobs')"
          >
            <Icon name="heroicons:magnifying-glass" class="w-4 h-4" />
            <span class="hidden md:inline ml-2"> Search </span>
          </button>
          <!-- <select name="job-age" id="job-age-select" class="btn p-2">
              <option
                v-for="option in options"
                :value="option"
                @click="switchOption(option)"
              >
                {{ option }}
              </option>
            </select> -->
        </div>
      </div>
    </div>

    <div class="overflow-x-scroll md:overflow-x-hidden">
      <div
        class="grid md:grid-cols-6 bg-info bg-opacity-60 text-primary text-sm py-6"
      >
        <p class="col-span-4 pl-6 hidden md:block">Job Title</p>
        <p class="col-span-1 text-center hidden md:block">Date Posted</p>
        <p class="col-span-1 text-center hidden md:block">Status</p>
      </div>
    </div>

    <div class="overflow-x-scroll md:overflow-x-hidden flex-grow">
      <div
        class="grid grid-rows-1 md:grid-cols-6"
        v-if="jobs.length > 0"
        v-for="recruiterJob in jobs.slice(
          (currentPage - 1) * limit,
          limit * currentPage
        )"
      >
        <nuxt-link
          :to="'/recruiter/dashboard/jobs/' + recruiterJob.slug"
          class="col-span-4 flex p-6 items-center"
        >
          <div class="border rounded-md h-10 w-10">
            <img
              :src="recruiterJob?.company?.logo_url_xs"
              alt=""
              class="object-cover h-full w-full"
            />
          </div>
          <div class="grid ml-4 w-full">
            <p class="text-sm font-semibold">
              {{ recruiterJob?.title }}
            </p>
            <div class="flex flex-wrap md:gap-x-4 items-center text-xs">
              <p class="text-gray-500 flex items-center gap-1 mr-2 md:mr-0">
                <Icon name="heroicons:briefcase" class="w-4 h-4" />{{
                  recruiterJob?.industry?.parent?.name
                }}
              </p>
              <p class="text-gray-500 flex items-center gap-1 mr-2 md:mr-0">
                <Icon name="heroicons:map-pin" class="w-4 h-4" />{{
                  recruiterJob?.company?.city
                }}
              </p>
              <p class="flex items-center gap-1">
                <Icon
                  name="heroicons:banknotes"
                  class="w-5 h-5 text-gray-400"
                /><span class="bg-primary text-white px-1"
                  >RM{{
                    recruiterJob?.reward_max == null
                      ? "0"
                      : recruiterJob?.reward_max
                  }}
                  Max Reward</span
                >
              </p>
            </div>
          </div>
        </nuxt-link>

        <div
          class="hidden md:flex justify-center items-center md:col-span-1 text-sm text-center py-8 px-4"
        >
          {{ dayjs(recruiterJob?.created_at).format("DD-MMM-YYYY") }}
        </div>
        <div
          class="hidden md:flex justify-center items-center md:col-span-1 text-primary text-sm text-center p-8 capitalize"
          :class="{
            'text-error-content': recruiterJob?.status === 'closed',
          }"
        >
          {{ recruiterJob?.status }}
        </div>
      </div>
      <div v-else class="bg-white p-2 grid gap-4 animate-pulse">
        <div v-for="n in 4" class="p-6 border-b-2">
          <div class="h-4 rounded-lg w-64 bg-slate-100 border-b-2"></div>
          <div class="h-4 mt-2 w-96 rounded-lg bg-slate-100 border-b-2"></div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="flex p-4 gap-4 justify-end">
      <button class="btn btn-ghost btn-circle btn-sm" v-if="currentPage > 1">
        <Icon name="heroicons:arrow-long-left-20-solid" />
      </button>
      <button
        class="btn btn-circle btn-sm"
        v-if="totalPages > 1"
        v-for="i in totalPages"
        :class="{ 'btn-success': currentPage === i }"
        @click="currentPage = i"
      >
        {{ i }}
      </button>
      <button
        class="btn btn-ghost btn-circle btn-sm"
        v-if="currentPage < totalPages"
        @click="currentPage += 1"
      >
        <Icon name="heroicons:arrow-long-right-20-solid" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
const props = defineProps({
  title: {
    type: String,
    default: "My Latest Jobs",
  },
  jobs: {
    type: Array<Job>,
    default: [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  limit: {
    type: Number,
    default: 4,
  },
});

const activeOption = ref("Last Week");
const options = ["Last Week", "2 Weeks Ago"];
const totalJobs = computed(() => props.jobs.length);
const totalPages = computed(() => Math.ceil(totalJobs.value / props.limit));
const currentPage = ref(1);
</script>
