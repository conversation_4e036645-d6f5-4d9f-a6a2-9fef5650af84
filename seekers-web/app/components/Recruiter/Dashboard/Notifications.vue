<template>
  <div
    id="recruiter-notifications-list"
    class="h-full border rounded-lg bg-white"
  >
    <h3 class="text-sm font-semibold bg-white p-4 pt-7 sticky top-0">
      Notifications
    </h3>
    <div class="flex flex-col gap-4 p-4">
      <div v-for="notification in props.notifications">
        <p class="text-xs">
          {{ notification.updated_at? formatDate(notification.updated_at): "System" }}
        </p>
        <p class="text-sm">
          {{ notification.heading }}
        </p>
        <p class="text-xs text-gray-500">
          {{ notification.subheading.replace("Tap for more info", "") }}
        </p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from "dayjs";

const props = defineProps({
  notifications: {
    type: Array<any>,
    default: [
      {
        updated_at: null ,
        id: 1,
        heading: "You have no notifications",
        subheading: "",
      },
    ],
  },
});

const formatDate = (date: string) => {
  return dayjs(date).format("DD MMM YYYY");
};
</script>
