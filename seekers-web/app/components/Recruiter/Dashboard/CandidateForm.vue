<template>
  <div class="text-sm bg-white rounded-lg p-8">
    <FormKit
      ref="infoFormRef"
      type="form"
      :disabled="loading"
      :actions="false"
      :form-class="'grid lg:grid-cols-2 gap-4 lg:gap-8'"
      @submit="submitInfoForm"
    >
      <div>
        <label for="name"
          >Candidate's Name : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          type="text"
          id="name"
          name="name"
          validation="required"
          :value="candidate?.name"
        />
      </div>

      <div>
        <label for="email"
          >Email Address : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          type="email"
          id="email"
          name="email"
          validation="required|email"
          :value="candidate?.email"
        />
      </div>

      <div>
        <label for="phone"
          >Mobile Number : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          type="tel"
          id="phone"
          name="mobile"
          placeholder="+60xxxxxxxxxx"
          validation="required|length:12,13"
          :validation-messages="{
            length: 'Phone number must be formatted: +60xxxxxxxxxx',
          }"
          :value="candidate?.mobile"
        />
      </div>

      <div>
        <label for="ic">Identification Card Number :</label><br />
        <FormKit
          type="number"
          id="ic"
          name="ic"
          maxlength="14"
          :value="props.candidate?.ic"
        />
      </div>
      <div>
        <label for="is_local">Malaysian Citizen:</label>
        <FormKit
          id="is_local"
          name="is_local"
          type="select"
          validation="required"
          :classes="{ input: 'appearance-none' }"
          :value="candidate?.is_local || 1"
        >
          <option value="1">Yes</option>
          <option value="0">No</option>
        </FormKit>
      </div>
      <div>
        <label for="job-role"
          >Job Role : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          id="job-role"
          name="role_id"
          type="select"
          validation="required"
          :classes="{ input: 'appearance-none' }"
          :value="candidate?.role_id"
          placeholder="Select One"
          :validation-messages="{
            required: 'Please select a job role',
          }"
        >
          <option disabled selected>Select One</option>
          <option v-for="role in roles" :value="role.id">
            {{ role?.name }}
          </option>
        </FormKit>
      </div>

      <div>
        <label for="gender">Gender : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          type="select"
          name="gender"
          id="gender"
          :classes="{ input: 'appearance-none' }"
          validation="required"
          :value="candidate?.gender || 'male'"
        >
          <option value="male">Male</option>
          <option value="female">Female</option>
        </FormKit>
      </div>

      <div>
        <label for="birth year"
          >Year of Birth : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          type="number"
          name="birth_year"
          id="birth year"
          placeholder="1999"
          validation="required|min:1940|max:2023"
          :value="props.candidate.birth_year"
        >
        </FormKit>
      </div>

      <div>
        <label for="race">Race : <span class="text-red-600">*</span></label
        ><br />
        <FormKit
          name="race"
          id="race"
          type="select"
          validation="required"
          :classes="{ input: 'appearance-none' }"
          :value="props.candidate.race"
        >
          <option value="malay">Malay</option>
          <option value="chinese">Chinese</option>
          <option value="indian">Indian</option>
          <option value="japanese">Japanese</option>
          <option value="bumi-nonmalay">Bumiputera</option>
          <option value="others">Others</option>
        </FormKit>
      </div>

      <!-- Newly added -->
      <div>
        <label for="expected salary"
          >Expected Salary (RM) : <span class="text-red-600">*</span></label
        >
        <br />
        <FormKit
          type="number"
          name="expected_salary"
          id="expected salary"
          validation="required"
          :value="props.candidate.expected_salary"
          placeholder="5000"
        />
      </div>

      <div>
        <label for="current salary"
          >Current Salary (RM) : <span class="text-red-600">*</span></label
        >
        <br />
        <FormKit
          type="number"
          name="current_salary"
          id="current salary"
          validation="required"
          :value="props.candidate.current_salary"
          placeholder="3000"
        />
      </div>

      <div>
        <label for="notice period"
          >Notice Period : <span class="text-red-600">*</span></label
        >
        <br />
        <FormKit
          type="select"
          name="notice_period"
          validation="required"
          :value="
            props.candidate?.notice_period
              ? props.candidate?.notice_period
              : '2 Weeks'
          "
          :validation-messages="{
            length: 'Must pick a notice period',
          }"
        >
          <option v-for="period in noticePeriodOptions" :value="period">
            {{ period }}
          </option>
        </FormKit>
      </div>
      <!-- Newly added -->

      <div>
        <label for="location">Current Address</label><br />
        <FormKit
          type="text"
          name="location"
          id="location"
          :value="props.candidate.location"
        />
      </div>
    </FormKit>

    <div class="divider my-12 lg:my-12 max-w-2xl mx-auto"></div>

    <div class="grid lg:grid-cols-2 gap-8">
      <div>
        <h2 class="mb-6">CV | Resume & Portfolio link</h2>
        <CustomCvInput
          :existingCvName="props.candidate.cv"
          @change="(file) => (defaultForm.cv = file)"
          class="mb-4"
        />
        <div>
          <label for="portfolio">Portfolio Link :</label><br />
          <input
            type="text"
            class="input input-bordered w-full"
            id="portfolio"
            name="portfolio"
            placeholder="https://portfolio.com or https://link-to-portfolio-file.docx"
            v-model="defaultForm.portfolio"
          />
        </div>
      </div>

      <div>
        <h2 class="mb-8">Languages</h2>
        <CustomLanguagePicker v-model="defaultForm.languages" />
      </div>
    </div>
    <div class="flex gap-4 mt-12">
      <button class="btn px-4" @click="emit('submit')">Back</button>
      <button
        type="submit"
        class="btn btn-primary capitalize px-12"
        @click="handleSubmitButton"
      >
        {{ props.mode == "edit" ? "Save" : "Add" }} Candidate
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";
const emit = defineEmits(["submit"]);
const props = defineProps({
  recruiterId: {
    type: Number,
  },
  candidate: {
    type: Object,
    required: true,
  },
  mode: {
    type: String,
    required: true,
  },
});

const { data: noticePeriodOptions } = useHelper.noticePeriods();
const { data: roles } = await useHelper.roles();
const infoFormRef = ref<any>(null);
const loading = ref<boolean>(false);

const defaultForm = ref<RecruiterCandidateForm>({
  id: props.candidate.id || null,
  user_id: props.recruiterId || props.candidate.user_id,
  birth_year: props.candidate.birth_year || null,
  cv: null,
  email: props.candidate.email || "",
  gender: props.candidate.gender || "male",
  got_work_permit: props.candidate.got_work_permit || 0,
  ic: props.candidate.ic || null,
  is_local: props.candidate.is_local || 1,
  languages: props.candidate.candidate_languages || [
    { selected: true, language: "English", level: "Native" },
  ],
  location: props.candidate.location || "",
  mobile: props.candidate.mobile || null,
  name: props.candidate.name || "",
  own_transport: props.candidate.own_transport || 0,
  portfolio: props.candidate.portfolio || "",
  race: props.candidate.race || "malay",
  role_id: props.candidate.job_role || null,
  willing_to_travel: props.candidate.willing_to_travel || 0,
  expected_salary: props.candidate.expected_salary || null,
  current_salary: props.candidate.current_salary || null,
  notice_period: props.candidate.notice_period || null,
});

const handleSubmitButton = (e: any) => {
  const node = infoFormRef.value.node;
  node.submit();
};

function submitInfoForm(basicFormData: any) {
  loading.value = true;
  let combinedForm = {
    ...defaultForm.value,
    ...basicFormData,
    languages: JSON.stringify(defaultForm.value.languages),
  };

  if (props.mode == "edit") {
    updateCandidate(combinedForm);
  } else {
    addCandidate(combinedForm);
  }
  loading.value = false;
}

async function addCandidate(combinedForm: RecruiterCandidateForm) {
  // CV is REQUIRED for Adding New Candidate
  if (typeof combinedForm.cv?.name == "string") {
    delete combinedForm.id;
    try {
      const { data: res }: any = await useRecruiter().addCandidate(
        combinedForm
      );
      useHelper.successToast(res.value.message);
      emit("submit", {
        candidate_id: res.value.data.id,
        current_salary: combinedForm.current_salary,
        expected_salary: combinedForm.expected_salary,
        notice_period: combinedForm.notice_period,
      });
    } catch (err: any) {
      useHelper.errorToast(err as string);
    }
  } else {
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: "CV | Resume is required. Please choose a file to upload",
    });
  }
}

async function updateCandidate(combinedForm: RecruiterCandidateForm) {
  if (props.candidate.cv || combinedForm.cv) {
    try {
      const { data: res } = await useRecruiter().updateCandidate(combinedForm);
      useHelper.successToast(res.value.message);
      navigateTo("/recruiter/dashboard/candidates");
    } catch (err: any) {
      useHelper.errorToast(err as string);
    }
  } else {
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: "CV is required",
    });
  }
}
</script>
