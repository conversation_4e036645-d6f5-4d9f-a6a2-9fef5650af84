<template>
  <div
    class="border rounded-md px-4 py-5 cursor-pointer hover:border-gray-400 bg-white transition-all duration-300"
    :class="{
      'border-error-content animate-pulse':
        !props.candidate.profile_completed && props.candidate.name,
    }"
    @click="handleclick"
  >
    <div class="flex gap-4 items-center">
      <div class="flex flex-col gap-1 text-left">
        <p
          v-if="!props.candidate.profile_completed && props.candidate.name"
          class="text-error-content text-xs"
        >
          Profile Incomplete, please update.
        </p>
        <p class="font-semibold">
          {{ props.candidate.name }}
        </p>
        <p class="text-xs text-primary">
          {{ props.candidate.role?.name || "Unknown" }}
        </p>

        <div class="flex gap-4 text-xs">
          <p class="text-gray-500">
            <Icon name="heroicons:map-pin" class="w-4 h-4 mr-2" />{{
              props.candidate.location
            }}
          </p>
          <p class="text-gray-500">
            <Icon
              name="heroicons:banknotes"
              class="w-5 h-5 mr-2 text-gray-400"
            />RM{{ props.candidate.expected_salary }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  candidate: {
    type: Object,
    required: true,
  },
  navigation: {
    type: Boolean,
    default: true,
  },
});

function handleclick() {
  if (props.navigation === false) return;
  navigateTo("/recruiter/dashboard/candidates/" + props.candidate.id);
}
</script>
