<template>
  <div>
    <div
      v-if="isLoading"
      class="md:grid md:grid-cols-3 md:mx-auto md:max-w-7xl mb-4 animate-pulse"
    >
      <JobCard v-for="job in showPerPage" :job="{}" />
    </div>
    <div v-if="!isLoading && filteredJobsList?.length == 0" class="text-center">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1673322682/website/candidate/undraw_void_-3-ggu_btd4h6.svg"
        class="w-1/3 lg:max-h-72 object-contain mx-auto animate__animated animate__fadeIn py-8"
      />
      <p>No Jobs found. Try a different keyword or a broader search.</p>
      <a
        :href="
          $route.fullPath.includes('recruiter')
            ? '/recruiter/dashboard/jobs'
            : '/jobs'
        "
      >
      </a>
    </div>
    <div
      v-if="!isLoading && filteredJobsList && filteredJobsList?.length > 0"
      class="md:grid lg:grid-cols-2 xl:grid-cols-3 md:mx-auto md:max-w-7xl mb-4 animate__animated animate__fadeIn"
    >
      <JobCard v-for="job in filteredJobsList" :job="job" />
    </div>

    <!-- pagination -->
    <div
      class="grid gap-8 grid-cols-2 max-w-lg mx-auto mt-4"
      v-if="jobsList && jobsList?.length > 0 && showPagination"
    >
      <div>
        <nuxt-link
          v-if="searchFilter.page > 1"
          :to="`/jobs?page=${prevPageNo}`"
          class="btn btn-info w-full"
          type="button"
          >Prev
        </nuxt-link>
        <div v-else class="btn btn-disabled w-full">Prev</div>
      </div>
      <div>
        <nuxt-link
          v-if="searchFilter.page < lastPage"
          :to="`/jobs?page=${nextPageNo}`"
          class="btn btn-info w-full"
          type="button"
          >Next</nuxt-link
        >
        <div v-else class="btn btn-disabled w-full">Next</div>
      </div>
    </div>

    <!-- <DevOnly>
      <pre>{{ filteredJobsList }}</pre>
    </DevOnly> -->
  </div>
</template>

<script setup lang="ts">
type propsType = {
  jobsList: Job[] | null;
  isLoading: boolean;
  showPerPage?: number;
  showPagination: boolean;
  lastPage?: number;
};
const props = withDefaults(defineProps<propsType>(), {
  jobsList: null,
  isLoading: false,
  showPerPage: 6,
  showPagination: true,
  lastPage: 10,
});
const searchFilter = useJobs().searchFilter();
const lastPage = props.lastPage;
const nextPageNo = computed(() => {
  return searchFilter.value.page + 1;
});
const prevPageNo = computed(() => {
  return searchFilter.value.page - 1;
});

const excludedJobIds = [9545];

const filteredJobsList = computed(() => {
  if (!props.jobsList) return null;
  return props.jobsList.filter((job) => !excludedJobIds.includes(job.id));
});
</script>
