<template>
  <div id="seekers-hr-trends" class="md:max-w-7xl md:mx-auto mx-4 my-24">
    <h2 class="text-[#FF1D03] text-4xl font-semibold text-center mb-8">
      Seekers HR Trends
    </h2>
    <div class="flex justify-end">
      <nuxt-link
        to="/hrtrends"
        class="flex items-center gap-4 text-[#D93025] text-sm font-semibold"
      >
        More Articles
        <div
          class="bg-[#E71D36] h-6 w-6 rounded text-white flex justify-center items-center"
        >
          <Icon name="heroicons:chevron-right-20-solid" />
        </div>
      </nuxt-link>
    </div>

    <div class="grid md:grid-cols-2 gap-x-32 gap-y-8 mt-12 mb-24">
      <div
        v-for="post in blogData?.posts"
        class="rounded-2xl border odd:bg-gradient-to-l odd:from-[#E71D36]/60 odd:to-white even:bg-gradient-to-l even:from-[#2EC4B6]/60 even:to-white p-[1px]"
      >
        <NuxtLink
          :to="post.url"
          target="blank"
          class="grid md:grid-cols-[2fr_3fr] gap-4 rounded-2xl bg-white/90 p-2"
        >
          <div class="border-[#E71D36] md:w-52 md:h-60">
            <img
              v-if="post.feature_image || post.twitter_image"
              :src="
                post.feature_image
                  ? post.feature_image.replace('/images/', '/images/size/w300/')
                  : post.twitter_image.replace('/images/', '/images/size/w300/')
              "
              alt=""
              class="rounded-2xl w-full h-full object-contain"
            />
            <img
              v-else
              src="@/static/seekers-malaysia.png"
              alt=""
              class="rounded-2xl w-full h-full object-contain"
            />
          </div>
          <div class="grid md:pr-12 md:py-6 gap-2 md:gap-0">
            <p class="text-xs md:pt-2">
              {{ dayjs(post.updated_at).format("DD MMMM YYYY") }}/ By Seekers
            </p>
            <p class="text-base font-semibold">
              {{ post.title }}
            </p>
            <p class="text-xs line-clamp-3 h-12">{{ post.meta_description }}</p>
            <p class="text-[#E71D36] text-sm self-end text-end md:text-start">
              Read more
              <Icon name="heroicons:arrow-right-20-solid" class="ml-4" />
            </p>
          </div>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const { data: blogData } = useBlog().featuredPosts();
</script>
