<template>
  <div
    id="inquire-today-hire-tomorrow"
    class="bg-[#E71D36] text-white pt-10 pb-8 mb-24"
  >
    <div class="md:max-w-7xl md:mx-auto text-center mx-4">
      <details class="collapse group">
        <summary
          class="!flex flex-col md:flex-row md:justify-center !items-center"
        >
          <div class="my-0 mx-auto">
            <h2 v-if="locale === 'en'" class="mb-2 text-4xl">
              See Our 5-Step Process to Hire Fast
            </h2>
            <h2 v-if="locale === 'zh'" class="mb-2 text-4xl">
              只需以下几个步骤，即可找到合适的人才。
            </h2>
            <p>
              {{ t("subtitle") }}
            </p>
          </div>
          <div
            class="transition group-open:hidden bg-white rounded-full md:p-2 p-1 w-fit mt-4 md:mt-0"
          >
            <svg
              fill="none"
              height="24"
              shape-rendering="geometricPrecision"
              stroke="black"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              width="24"
            >
              <path d="M9 18l6-6-6-6" class="hidden md:block"></path>
              <path d="M6 9l6 6 6-6" class="md:hidden"></path>
            </svg>
          </div>
        </summary>

        <div class="md:max-w-xl mx-auto grid gap-8 mt-12 collapse-content">
          <div
            v-for="process in processes"
            class="grid grid-cols-[1fr_3fr] items-center bg-white rounded-lg py-3 md:py-4 drop-shadow-lg"
          >
            <div class="border-r border-black/25">
              <img
                :src="process.img"
                :alt="process.img + 'image'"
                class="mx-auto w-20 h-20"
              />
            </div>

            <div class="text-[#FF1D03] text-sm text-start m-4">
              <p class="mb-2 font-bold">
                {{ process.step }}
              </p>
              <p class="font-semibold leading-tight">{{ process.action }}</p>
            </div>
          </div>
        </div>
      </details>
    </div>
    <!-- <div class="py-5">
      <details class="group">
        <summary
          class="flex justify-between items-center font-medium cursor-pointer list-none"
        >
          <span> What is a SAAS platform?</span>
          <span class="transition group-open:rotate-180">
            <svg
              fill="none"
              height="24"
              shape-rendering="geometricPrecision"
              stroke="currentColor"
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="1.5"
              viewBox="0 0 24 24"
              width="24"
            >
              <path d="M6 9l6 6 6-6"></path>
            </svg>
          </span>
        </summary>
        <p class="text-neutral-600 mt-3 group-open:animate-fadeIn">
          SAAS platform is a cloud-based software service that allows users to
          access and use a variety of tools and functionality.
        </p>
      </details>
    </div> -->
  </div>
</template>

<script setup>
const { t, locale } = useI18n({
  useScope: "local",
});

const processes = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749131222/website/hire-digital-tech-talent/schedule_j1ozlg.png",
    step: `${t("step1")}`,
    action: `${t("action1")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749131222/website/hire-digital-tech-talent/checklist_agkgv8.png",
    step: `${t("step2")}`,
    action: `${t("action2")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749131222/website/hire-digital-tech-talent/match_vj9eqp.png",
    step: `${t("step3")}`,
    action: `${t("action3")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749131222/website/hire-digital-tech-talent/handshake_guhkyg.png",
    step: `${t("step4")}`,
    action: `${t("action4")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749131222/website/hire-digital-tech-talent/shuttle_oh8dmj.png",
    step: `${t("step5")}`,
    action: `${t("action5")}`,
  },
];
</script>

<i18n lang="json">
{
  "en": {
    "subtitle": "Inquire Today, Hire Tomorrow",
    "step1": "STEP 1: Book Free Consultation",
    "action1": "Talk to our hiring experts to define your needs.",
    "step2": "STEP 2: Tell Us Your Requirements",
    "action2": "Share your stack, team size, and budget.",
    "step3": "STEP 3: Receive Matched Profiles",
    "action3": "Only interview pre-screened , tech-tested candidates.",
    "step4": "STEP 4: Make the Offer",
    "action4": "We assist you until the hire is finalized.",
    "step5": "STEP 5: Onboard Smoothly",
    "action5": "We will support you in the onboarding process."
  },
  "zh": {
    "subtitle": "只需以下几个步骤，即可找到合适的人才。",
    "step1": "预约免费顾问",
    "action1": "与我们的团队预约免费顾问，商讨并了解您的招聘需求与目标。",
    "step2": "分享您的招聘需求",
    "action2": "与我们分享您的招聘职位要求，包括您正在寻找的人才所需技能，经验与资历。",
    "step3": "人才搜索与匹配",
    "action3": "我们的招聘人员将筛选，面试候选人，并匹配最适合您公司的人选。",
    "step4": "敲定Offer",
    "action4": "一旦您确定了您感兴趣的候选人，我们将协助您谈判与敲定offer。",
    "step5": "候选人入职",
    "action5": "候选人接受offer之后，我们将协助入职流程。"
  }
}
</i18n>
