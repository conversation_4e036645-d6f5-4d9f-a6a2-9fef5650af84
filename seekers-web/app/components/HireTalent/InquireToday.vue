<template>
  <div
    id="inquire-today-hire-tomorrow"
    class="bg-[#E71D36] text-white pt-12 pb-24"
  >
    <div class="md:max-w-7xl md:mx-auto text-center mx-4 relative">
      <h2 v-if="locale === 'en'" class="mb-2 text-4xl">
        Inquire <span class="font-bold">Today</span>, Hire
        <span class="font-bold">Tomorrow</span>
      </h2>
      <h2 v-if="locale === 'zh'" class="mb-2 text-4xl font-bold">
        今日询问，明日雇用
      </h2>
      <p>
        {{ t("subtitle") }}
      </p>
      <div class="grid md:grid-cols-5 gap-8 mt-12">
        <div v-for="inquiry in inquiries">
          <img
            :src="inquiry.img"
            :alt="inquiry.img + 'image'"
            class="mx-auto w-24 h-24"
          />
          <div>
            <p class="font-semibold text-lg mt-8 mb-2">{{ inquiry.step }}</p>
            <p class="text-sm">{{ inquiry.action }}</p>
          </div>
        </div>
      </div>
      <div
        class="hidden lg:flex md:max-w-7xl mx-auto justify-center items-center lg:gap-32 xl:gap-28 absolute lg:right-[15%] lg:w-[70%] xl:w-max lg:top-[38%] xl:right-[13%]"
      >
        <div v-for="arrow in arrows">
          <img :src="arrow.arrow" alt="" class="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { t, locale } = useI18n({
  useScope: "local",
});

const inquiries = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/book-consultation_zd5yc1.png",
    step: `${t("step1")}`,
    action: `${t("action1")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/share-requirement_vxh8uq.png",
    step: `${t("step2")}`,
    action: `${t("action2")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/match-candidate_g7rbpe.png",
    step: `${t("step3")}`,
    action: `${t("action3")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/finalize-offer_qlceep.png",
    step: `${t("step4")}`,
    action: `${t("action4")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/new-hire_jwd6gv.png",
    step: `${t("step5")}`,
    action: `${t("action5")}`,
  },
];

const arrows = [
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-up_jnw0oa.png",
  },
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-down_krznmz.png",
  },
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-up_jnw0oa.png",
  },
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-down_krznmz.png",
  },
];
</script>

<i18n lang="json">
{
  "en": {
    "subtitle": "Find Out What Happens After You Inquire with Seekers. Learn More Here",
    "step1": "Book FREE Consultation",
    "action1": "Schedule a complimentary consultation with our team to discuss your hiring needs and goals.",
    "step2": "Share Your Requirements",
    "action2": "Share your detailed job requirements, including the skills, experience, and qualifications you are seeking in a candidate.",
    "step3": "Matching Candidate Profiles",
    "action3": "Our recruiters will screen, interview candidates, and matching the best fit for your organization.",
    "step4": "Finalize Job Offer",
    "action4": "Once you have identified the candidates you are interested in, we will assist you in finalizing the job offer.",
    "step5": "Onboard New Hire",
    "action5": "After the candidate accepts the job offer, we will support you in the onboarding process."
  },
  "zh": {
    "subtitle": "只需以下几个步骤，即可找到合适的人才。",
    "step1": "预约免费顾问",
    "action1": "与我们的团队预约免费顾问，商讨并了解您的招聘需求与目标。",
    "step2": "分享您的招聘需求",
    "action2": "与我们分享您的招聘职位要求，包括您正在寻找的人才所需技能，经验与资历。",
    "step3": "人才搜索与匹配",
    "action3": "我们的招聘人员将筛选，面试候选人，并匹配最适合您公司的人选。",
    "step4": "敲定Offer",
    "action4": "一旦您确定了您感兴趣的候选人，我们将协助您谈判与敲定offer。",
    "step5": "候选人入职",
    "action5": "候选人接受offer之后，我们将协助入职流程。"
  }
}
</i18n>
