<template>
  <div
    id="discover-digital-tech-positions"
    class="grid md:grid-cols-[2fr_3fr] gap-8 md:max-w-7xl mx-8 md:mx-auto my-48"
  >
    <div class="text-center md:text-start md:py-12">
      <h2 v-if="locale == 'en'" class="text-4xl font-semibold">
        Discover <span class="text-[#E71D36]">IT/Tech</span> Talent Positions
        here
      </h2>
      <h2 v-if="locale == 'zh'" class="text-4xl font-semibold">
        发掘<span class="text-[#E71D36]">数字营销&信息技术</span>人才职位
      </h2>
      <p class="my-6">
        {{ t("subtitle") }}
      </p>
      <div class="flex justify-center md:justify-start gap-12">
        <div>
          <p class="text-[#FF1D03] text-3xl">{{ t("candidates") }}</p>
          <p>{{ t("totalCandidates") }}</p>
        </div>
        <div>
          <p class="text-[#2EC4B6] text-3xl">{{ t("clients") }}</p>
          <p>{{ t("totalClients") }}</p>
        </div>
      </div>
      <nuxt-link
        to="#hire-talent-free-consultation"
        class="btn bg-[#E71D36] text-white mt-6"
        >{{ t("button") }}</nuxt-link
      >
    </div>
    <div class="grid md:grid-cols-2 lg:grid-cols-3 justify-center gap-8">
      <div
        v-for="position in positions"
        class="p-[1px] rounded-2xl shadow-[0_4px_13px_0_rgba(0,0,0,0.06)] w-64 md:w-fit"
        :class="position.borderClass"
      >
        <div class="p-4 rounded-2xl h-full" :class="position.bgClass">
          <img
            :src="position.img"
            :alt="position.img + 'image'"
            class="mx-auto"
          />
          <div class="">
            <h3 class="text-center text-lg leading-6 font-semibold mt-2 mb-4">
              {{ position.title }}
            </h3>
            <p class="text-sm">{{ position.list }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { t, locale } = useI18n({
  useScope: "local",
});

const positions = [
  {
    borderClass: "bg-gradient-to-b from-[#E71D36]/60 to-white",
    bgClass: "bg-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749195088/website/hire-digital-tech-talent/tech_lead_bc44cw.png",
    title: `${t("positonTitle1")}`,
    list: `${t("positonList1")}`,
  },
  {
    borderClass: "bg-gradient-to-b from-[#2EC4B6]/60 to-white",
    bgClass: "bg-[#F6FFFE]",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749195088/website/hire-digital-tech-talent/cybersecurity_cyococ.png",
    title: `${t("positonTitle2")}`,
    list: `${t("positonList2")}`,
  },
  {
    borderClass: "bg-gradient-to-b from-[#E71D36]/60 to-white",
    bgClass: "bg-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749195088/website/hire-digital-tech-talent/devops_sre_lqvwpg.png",
    title: `${t("positonTitle3")}`,
    list: `${t("positonList3")}`,
  },
  {
    borderClass: "bg-gradient-to-b from-[#2EC4B6]/60 to-white",
    bgClass: "bg-[#F6FFFE]",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749195290/website/hire-digital-tech-talent/ai_ml_c10u2o.png",
    title: `${t("positonTitle4")}`,
    list: `${t("positonList4")}`,
  },
  {
    borderClass: "bg-gradient-to-b from-[#E71D36]/60 to-white",
    bgClass: "bg-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749195087/website/hire-digital-tech-talent/data_xpdrpg.png",
    title: `${t("positonTitle5")}`,
    list: `${t("positonList5")}`,
  },
  {
    borderClass: "bg-gradient-to-b from-[#2EC4B6]/60 to-white",
    bgClass: "bg-[#F6FFFE]",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749195087/website/hire-digital-tech-talent/em_fremah.png",
    title: `${t("positonTitle6")}`,
    list: `${t("positonList6")}`,
  },
];
</script>

<i18n lang="json">
{
  "en": {
    "subtitle": "Seekers offers a pool of talented individuals specializing in Tech fields.",
    "totalCandidates": "Total Candidates",
    "candidates": "70k",
    "totalClients": "Total Clients",
    "clients": "5.5k",
    "button": "Book Free Consultation",
    "positonTitle1": "Tech Lead",
    "positonList1": "Frontend Tech Lead, Engineering Manager, Head of Engineering and..",
    "positonTitle2": "Cybersecurity",
    "positonList2": "Cybersecurity Analyst, DevSecOps Engineer, Application Security Engineer and..",
    "positonTitle3": "DevOps / SRE",
    "positonList3": "DevOps Engineer (AWS + Kubernetes), Site Reliability Engineer, Cloud Infrastructure Engineer..",
    "positonTitle4": "AI / Machine Learning Engineer",
    "positonList4": "NLP Engineer, Machine Learning Researcher, AI Developer for Chatbot Systems and more..",
    "positonTitle5": "Data Scientist / Data Engineer",
    "positonList5": "Data Scientist - AI Personalization, Machine Learning Analyst, Business Intelligence Data Scientist",
    "positonTitle6": "Engineering Manager (EM)",
    "positonList6": "Engineering Manager - Web Platform, Software Development Manager, Head of Engineering - Fintech"
  },
  "zh": {
    "subtitle": "Seekers提供专门从事数字营销&信息技术领域的人才。",
    "totalCandidates": "名人才",
    "candidates": "70万",
    "totalClients": "所客户公司",
    "clients": "5,500",
    "button": "免费咨询",
    "positonTitle1": "软件开发",
    "positonList1": "PHP, Laravel, DotNet, JavaScript, iOS, Android, Python, Java, 更多",
    "positonTitle2": "数字营销",
    "positonList2": "市场营销，SEO专员，SEM专员， 更多",
    "positonTitle3": "数据相关职位",
    "positonList3": "数据科学家，数据分析师，数据工程师， 更多",
    "positonTitle4": "信息技术",
    "positonList4": "信息技术项目经理，网络工程师，开发运营工程师，更多",
    "positonTitle5": "创意",
    "positonList5": "平面设计师，UI/UX设计师，视频编辑员，更多",
    "positonTitle6": "销售/业务",
    "positonList6": "业务开发，产品经理，业务分析师，更多"
  }
}
</i18n>
