<template>
  <div id="hire-talent-free-consultation" class="max-w-5xl mx-auto p-12">
    <div class="text-center">
      <h2 class="text-[#FF1D03] font-semibold text-4xl mb-6">
        {{ t("title") }}
      </h2>
      <p class="font-semibold mb-12">
        {{ t("subtitle") }}
      </p>
    </div>

    <CustomZohoFormHireTalent />
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n({
  useScope: "local",
});
</script>

<i18n lang="json">
{
  "en": {
    "title": "Contact Us For FREE Consultation",
    "subtitle": "Ready to Hire Digital & Tech talent? Please feel free to contact us today!"
  },
  "zh": {
    "title": "联系我们免费顾问",
    "subtitle": "准备雇用数字营销&信息技术人才？欢迎联系我们！"
  }
}
</i18n>
