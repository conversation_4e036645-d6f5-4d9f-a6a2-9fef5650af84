<template>
  <div class="text-center md:max-w-7xl md:mx-auto m-12">
    <h2 v-if="locale == 'en'" class="text-4xl font-semibold mb-8">
      Why Hire
      <span class="text-[#FF1D03]">IT Developers</span> With Seekers Malaysia?
    </h2>
    <h2 v-if="locale == 'zh'" class="text-4xl font-semibold mb-8">
      Seekers - <span class="text-[#FF1D03]">数字营销&信息技术</span>的招聘优势
    </h2>
    <div class="grid md:grid-cols-4 gap-8">
      <div
        v-for="reason in reasons"
        class="flex flex-col bg-gradient-to-b from-[#E71D36]/60 to-white p-[1px] rounded-2xl"
      >
        <div class="p-4 rounded-2xl h-full bg-white">
          <img
            :src="reason.img"
            :alt="reason.title + 'icon'"
            class="mx-auto h-[91px] w-[91px]"
          />
          <h3 class="grid items-center text-xl font-semibold h-20">
            {{ reason.title }}
          </h3>
          <p class="text-sm text-justify text-[#696969]">
            {{ reason.subtitle }}
          </p>
          <!-- <p v-html="t('reasonSubtitle1')"></p> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const { t, locale } = useI18n({
  useScope: "local",
});

const reasons = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749143318/website/hire-digital-tech-talent/hourglass_l0muog.png",
    title: `${t("reasonTitle1")}`,
    subtitle: `${t("reasonSubtitle1")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749143319/website/hire-digital-tech-talent/target_rlx2yb.png",
    title: `${t("reasonTitle2")}`,
    subtitle: `${t("reasonSubtitle2")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749143318/website/hire-digital-tech-talent/pin_lilkzr.png",
    title: `${t("reasonTitle3")}`,
    subtitle: `${t("reasonSubtitle3")}`,
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1749143319/website/hire-digital-tech-talent/smartphone_rt6h94.png",
    title: `${t("reasonTitle4")}`,
    subtitle: `${t("reasonSubtitle4")}`,
  },
];
</script>

<i18n lang="json">
{
  "en": {
    "reasonTitle1": "Hire in Just Days",
    "reasonSubtitle1": "Our curated talent pool lets you skip lengthy sourcing and go straight to interview-ready candidates.",
    "reasonTitle2": "Tech Hiring Is Our Speciality",
    "reasonSubtitle2": "We understand frameworks, languages, stacks, and soft skills like communication and teamwork.",
    "reasonTitle3": "Massive Talent Reach",
    "reasonSubtitle3": "Over 70,000 candidates across Malaysia including remote-ready talents.",
    "reasonTitle4": "Only Pay When You Hire",
    "reasonSubtitle4": "With our success-based model, you only pay after the perfect candidate joins your team."
  },
  "zh": {
    "reasonTitle1": "多元化工作性质",
    "reasonSubtitle1": "无论您正在招聘长期，自由职业或是合同形式人才，我们都能为您搜索。",
    "reasonTitle2": "着重于技术性领域",
    "reasonSubtitle2": "我们熟知数字营销&信息技术领域市场及人才所需技能，使我们能够了解您的招聘需求，并提供高素质与经验丰富的人才。",
    "reasonTitle3": "深度与广泛人才搜索",
    "reasonSubtitle3": "我们拥有广泛的本地与全球人才数据库，并能够让您与本地和来自世界各地的顶尖人才建立联系",
    "reasonTitle4": "按招聘结果付费",
    "reasonSubtitle4": "通过我们以结果为导向的付款模式，只有当我们成功为您的公司找到并安置合格的人才时，您才需要向我们付款。"
  }
}
</i18n>
