<template>
  <div id="hire-talent-hero">
    <div
      class="bg-[url('https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1687936215/website/hire-digital-tech-talent/hero_amv068.svg')]"
    >
      <div class="text-right pr-4 pt-4">
        <label class="ml-auto cursor-pointer label w-min gap-2">
          <span class="label-text"
            ><Icon name="circle-flags:cn" class="text-xl"
          /></span>
          <input
            type="checkbox"
            class="toggle toggle-sm"
            :checked="locale === 'en'"
            @change="toggleLanguage"
          />
          <span class="label-text"
            ><Icon name="circle-flags:en" class="text-xl"
          /></span>
        </label>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-4 p-12 md:pb-20 md:px-28 justify-items-center items-center mx-auto"
      >
        <div
          id="hero-text"
          class="col-span-2 animate__animated animate__fadeIn"
        >
          <i18n-t
            v-if="locale == 'en'"
            keypath="h1Title"
            tag="h1"
            class="font-semibold text-6xl"
          >
            <template #action>
              <span class="text-[#FF1D03]">TOP DEVELOPERS</span>
            </template>
          </i18n-t>
          <i18n-t
            v-if="locale == 'zh'"
            keypath="h1Title"
            tag="h1"
            class="font-semibold text-6xl"
          >
            <template #action>
              <span class="text-[#FF1D03]">数字营销&信息技术</span>
            </template>
          </i18n-t>
          <!-- <h1 class="font-semibold text-6xl">
            HIRE <span class="text-[#FF1D03]">DIGITAL & TECH</span> TALENT
          </h1> -->
          <p class="font-semibold text-2xl my-4 md:my-8">
            {{ t("subtitle") }}
          </p>
          <p>
            {{ t("paragraph") }}
          </p>
          <div class="mt-4 md:mt-8 hidden md:block">
            <nuxt-link
              to="#hire-talent-free-consultation"
              class="btn bg-[#E71D36] text-white !text-lg !font-semibold rounded-full px-8"
              >{{ t("button") }}</nuxt-link
            >
          </div>
        </div>
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1687937449/website/hire-digital-tech-talent/hero-image_kaxbop.svg"
          alt="hero-image"
          class="col-span-2 mt-10 md:mt-0 md:ml-48 hidden md:block"
        />
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1749221238/website/hire-digital-tech-talent/hero-image-mobile_got7ta.png"
          alt="hero-image"
          class="col-span-2 mt-10 md:mt-0 md:ml-48 md:hidden"
        />
        <div class="md:hidden">
          <nuxt-link
            to="#hire-talent-free-consultation"
            class="btn bg-[#E71D36] text-white !text-lg !font-semibold rounded-full px-8"
            >{{ t("button") }}</nuxt-link
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t, locale, setLocale } = useI18n({
  useScope: "local",
});

const toggleLanguage = () => {
  setLocale(locale.value == "en" ? "zh" : "en");
};
</script>

<i18n lang="json">
{
  "en": {
    "h1Title": "HIRE MALAYSIA'S {action} — FAST & RISK-FREE",
    "subtitle": "Your Trusted Tech Recruitment Agency",
    "paragraph": "From DevOps to Data Scientists, get matched with elite, pre-screened developers - tailored to your stack.",
    "button": "Get Developer Talent Now"
  },
  "zh": {
    "h1Title": "{action}人才招聘",
    "subtitle": "您信赖的数字营销&信息技术人才招聘合作伙伴",
    "paragraph": "如果您正在为公司寻找本地或国外的数字营销&信息技术人才，Seekers是您最佳的合作伙伴！",
    "button": "联系我们"
  }
}
</i18n>
