<template>
  <div class="multi-select">
    <div :class="props.outerClass">
      <div
        class="grid grid-cols-[8fr_1fr] lg:grid-cols-[12fr_1fr] gap-2 items-center"
      >
        <!-- List of selected items -->
        <div class="flex w-full items-center gap-2 flex-wrap">
          <div
            v-for="item in props.modelValue"
            :class="props.innerClass"
            @click="removeItem(item)"
          >
            {{ item.label }} <Icon name="heroicons:x-mark" />
          </div>
          <div
            class="text-xs p-2 w-full cursor-pointer"
            v-if="props.modelValue.length == 0"
            @click="toggleDropdown"
          >
            {{ placeholder }}
          </div>
        </div>

        <!-- Dropdown Icon -->
        <div
          class="text-xs p-2 bg-gray-100 rounded-lg cursor-pointer text-center"
          @click="toggleDropdown"
        >
          <Icon name="heroicons:chevron-up" v-if="showOptions" />
          <Icon name="heroicons:chevron-down" v-else />
        </div>

        <!-- Dropdown List -->
        <div
          v-if="showOptions"
          class="h-min w-full col-span-2 max-h-96 overflow-y-auto"
        >
          <div
            v-for="option in uniqueOptions"
            @click="updateValue(option)"
            class="p-2 border-b cursor-pointer"
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
type Option = {
  label: string;
  value: string;
};
const props = defineProps({
  modelValue: {
    type: Array<any>,
    default: [],
  },
  options: {
    type: Array<any>,
    default: [],
  },
  placeholder: {
    type: String,
    default: "Select",
  },
  noMaxCount: {
    type: Number,
    default: 5,
  },
  outerClass: {
    type: String,
    default: "p-2 border rounded-lg w-full",
  },
  innerClass: {
    type: String,
    default:
      "text-xs p-2 bg-gray-100 rounded-lg hover:bg-error hover:text-error-content hover:cursor-pointer",
  },
  sort: {
    type: Boolean,
    default: true,
  },
});
const showOptions = ref(false);

const emit = defineEmits(["update:modelValue"]);

const uniqueOptions = computed(() => {
  let uniqueOptions: Option[] = [];
  props.options.forEach((option: Option) => {
    let isUnique = true;
    props.modelValue.forEach((item: Option) => {
      if (item.value == option.value) {
        isUnique = false;
      }
    });
    if (isUnique) {
      uniqueOptions.push(option);
    }
  });

  //sort the array by label alphabetically
  if (props.sort) {
    uniqueOptions.sort((a, b) => {
      if (a.label < b.label) {
        return -1;
      }
      if (a.label > b.label) {
        return 1;
      }
      return 0;
    });
  }
  return uniqueOptions;
});

function toggleDropdown() {
  if (props.modelValue.length < props.noMaxCount) {
    showOptions.value = !showOptions.value;
  }
}

const updateValue = (optionObject: Option) => {
  showOptions.value = false;
  let newData = [...props.modelValue];
  newData?.push(optionObject);
  emit("update:modelValue", newData);
};

const removeItem = (optionObject: Option) => {
  let newData = [...props.modelValue];
  props.modelValue.forEach((item: Option) => {
    if (item.value == optionObject.value) {
      newData.splice(newData.indexOf(item), 1);
    }
  });
  emit("update:modelValue", newData);
};
</script>
