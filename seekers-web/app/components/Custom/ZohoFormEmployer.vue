<template>
  <div class="-mt-12">
    <FormKit
      type="form"
      action="https://crm.zoho.com/crm/WebToLeadForm"
      name="WebToLeads5550753000000613127"
      method="post"
      enctype="multipart/form-data"
      accept-charset="UTF-8"
      :form-class="'grid lg:grid-cols-2 gap-4 lg:gap-8'"
      :submit-attrs="{
        inputClass: 'btn btn-primary ',
        outerClass: 'max-w-xl mx-auto text-center mt-4',
      }"
      :actions="false"
      incomplete-message="Please fill in all required fields."
    >
      <!-- START: Do not modify this code. -->
      <FormKit
        type="text"
        style="display: none"
        name="xnQsjsdp"
        value="4248f013feb7618574c6d4ad305ed61dfcda44d040b3a5f24e29517f1c11c978"
      />

      <FormKit type="hidden" name="zc_gad" id="zc_gad" value="" />
      <FormKit
        type="text"
        style="display: none"
        name="xmIwtLD"
        value="803ac109a10fe4a76477444ba344c924fe208a297b2486df88a80e73d1e06037"
      />
      <FormKit
        type="text"
        style="display: none"
        name="actionType"
        value="TGVhZHM="
      />
      <FormKit
        type="text"
        style="display: none"
        name="returnURL"
        value="https&#x3a;&#x2f;&#x2f;seekers.my&#x2f;employer&#x2f;thankyou"
      />
      <!-- END: Do not modify this code. -->

      <FormKit
        type="select"
        id="LEADCF5"
        name="LEADCF5"
        label="Are you Employer or Job Seekers ?*"
        validation="required"
        :validation-messages="{
          required: 'Please select a value',
        }"
        :classes="{ input: 'appearance-none' }"
      >
        <option value="" selected disabled>Select One</option>
        <option value="Employer">Employer</option>
        <option value="Job&#x20;Seeker">Job Seeker</option>
      </FormKit>
      <FormKit
        type="text"
        id="First_Name"
        name="First Name"
        label="First Name*"
        maxlength="40"
        validation="required"
      />
      <FormKit
        type="text"
        id="Last_Name"
        name="Last Name"
        label="Last Name*"
        maxlength="80"
        validation="required"
      />
      <FormKit
        type="text"
        id="Company"
        name="Company"
        label="Company*"
        maxlength="200"
        validation="required"
      />
      <FormKit
        type="email"
        ftype="email"
        id="Email"
        name="Email"
        label="Email*"
        maxlength="100"
        validation="required"
      />
      <FormKit
        type="text"
        id="Phone"
        name="Phone"
        label="Phone*"
        maxlength="30"
        validation="required"
      />
      <FormKit
        type="text"
        id="LEADCF2"
        name="LEADCF2"
        maxlength="255"
        label="Message"
      />
      <FormKit
        type="select"
        class="zcwf_col_fld_slt"
        id="LEADCF4"
        name="LEADCF4"
        label="Vacant Positions*"
        validation="required"
        :validation-messages="{
          required: 'Please select a value',
        }"
        :classes="{ input: 'appearance-none' }"
      >
        <option value="" selected disabled>Select One</option>
        <option
          value="Sales,&#x20;Business&#x20;Development&#x20;&amp;&#x20;Account&#x20;Management"
        >
          Sales, Business Development &amp; Account Management
        </option>
        <option value="Accounting&#x20;&amp;&#x20;Finance">
          Accounting &amp; Finance
        </option>
        <option value="Information&#x20;Technology">
          Information Technology
        </option>
        <option value="Administration&#x20;Support">
          Administration Support
        </option>
        <option
          value="Digital&#x20;Marketing&#x20;&amp;&#x20;Social&#x20;Media"
        >
          Digital Marketing &amp; Social Media
        </option>
        <option value="Human&#x20;Resource&#x20;&amp;&#x20;Recruitment">
          Human Resource &amp; Recruitment
        </option>
        <option value="Customer&#x20;Service">Customer Service</option>
        <option value="Others&#x20;&#x28;please&#x20;share&#x29;">
          Others &#x28;please share&#x29;
        </option>
      </FormKit>
      <FormKit
        type="select"
        id="LEADCF1"
        name="LEADCF1"
        label="Salary Range for your vacancy"
        :classes="{ input: 'appearance-none' }"
      >
        <option value="" selected disabled>Select One</option>
        <option value="Below&#x20;RM2999">Below RM2999</option>
        <option value="RM3000&#x20;-&#x20;RM3999">RM3000 - RM3999</option>
        <option value="RM4000&#x20;-&#x20;RM4999">RM4000 - RM4999</option>
        <option value="RM5000&#x20;-&#x20;RM5999">RM5000 - RM5999</option>
        <option value="RM6000&#x20;-&#x20;RM7999">RM6000 - RM7999</option>
        <option value="RM8000&#x20;-&#x20;RM9999">RM8000 - RM9999</option>
        <option value="RM10000&#x20;and&#x20;above">RM10000 and above</option>
      </FormKit>
      <FormKit
        type="file"
        name="theFile"
        label="Upload a File"
        help="Job description, if available. Max 20MB"
        id="theFile5550753000000613127"
        :classes="{
          noFiles: 'hidden',
          fileName: 'hidden',
          fileRemove: 'hidden',
        }"
      />
      <FormKit
        type="text"
        id="Website"
        name="Website"
        maxlength="255"
        label="Website"
      />
      <FormKit
        type="select"
        id="LEADCF3"
        name="LEADCF3"
        label="What do you expect Seekers to provide you"
        :classes="{ input: 'appearance-none' }"
      >
        <option
          value="General&#x20;salary&#x20;rates&#x20;for&#x20;your&#x20;hiring&#x20;position"
        >
          General salary rates for your hiring position
        </option>
        <option
          value="What&#x20;kind&#x20;of&#x20;candidates&#x20;are&#x20;in&#x20;Seekers&#x27;s&#x20;database"
        >
          What kind of candidates are in Seekers&#x27;s database
        </option>
        <option value="Fee&#x20;structure&#x20;and&#x20;Service">
          Fee structure and Service
        </option>
        <option value="Assist&#x20;to&#x20;prepare&#x20;Job&#x20;Description">
          Assist to prepare Job Description
        </option>
        <option
          value="I&#x20;want&#x20;to&#x20;start&#x20;hiring&#x20;right&#x20;away"
        >
          I want to start hiring right away
        </option>
      </FormKit>
      <div class="py-4 lg:col-span-2">
        <FormKit
          autocomplete="off"
          id="privacyTool5550753000000613127"
          type="checkbox"
          name="privacyTool"
          validation="accepted"
          label="Agree to the Privacy Policy and Terms of Service."
          :value="true"
          :validation-messages="{
            accepted:
              'Please agree to the Privacy Policy and Terms of Service. Visit Seekers.my/Terms for more information.',
          }"
          :classes="{ wrapper: 'flex gap-4 items-center', input: 'checkbox' }"
        />
      </div>
      <div></div>
      <button
        class="btn lg:col-span-2 -mt-6 bg-[#CE643A] text-white"
        type="submit"
      >
        Submit
      </button>
    </FormKit>
  </div>
</template>
