<template>
  <div class="-mt-12">
    <ClientOnly>
      <FormKit
        type="form"
        action="/api/validateTurnstile"
        name="WebToLeads5550753000005085011"
        method="post"
        enctype="multipart/form-data"
        accept-charset="UTF-8"
        :form-class="'grid lg:grid-cols-2 gap-4 lg:gap-8'"
        :submit-attrs="{
          inputClass: 'btn btn-primary ',
          outerClass: 'max-w-xl mx-auto text-center mt-4',
        }"
        :actions="false"
        incomplete-message="Please fill in all required fields."
      >
        <!-- START: Do not modify this code. -->
        <FormKit
          type="text"
          style="display: none"
          name="xnQsjsdp"
          value="4248f013feb7618574c6d4ad305ed61dfcda44d040b3a5f24e29517f1c11c978"
        />

        <FormKit type="hidden" name="zc_gad" id="zc_gad" value="" />
        <FormKit
          type="text"
          style="display: none"
          name="xmIwtLD"
          value="803ac109a10fe4a76477444ba344c924e4ad35aa79ffaf3d9f7a1d5304743db8"
        />
        <FormKit
          type="text"
          style="display: none"
          name="actionType"
          value="TGVhZHM="
        />
        <FormKit
          type="text"
          style="display: none"
          name="returnURL"
          value="https&#x3a;&#x2f;&#x2f;seekers.my&#x2f;hire-digital-tech-talent&#x2f;thankyou"
        />
        <!-- END: Do not modify this code. -->

        <FormKit
          type="text"
          id="Last_Name"
          name="Last Name"
          :label="t('name')"
          maxlength="80"
          validation="required"
        />
        <FormKit
          type="text"
          id="Company"
          name="Company"
          :label="t('companyName')"
          maxlength="200"
          validation="required"
        />
        <FormKit
          type="email"
          ftype="email"
          id="Email"
          name="Email"
          :label="t('email')"
          maxlength="100"
          validation="required"
        />
        <FormKit
          type="tel"
          id="Mobile"
          name="Mobile"
          :label="t('mobilePhone')"
          maxlength="30"
          validation="required"
          help="Please include your country code in your mobile number"
        />
        <FormKit
          type="text"
          id="Website"
          name="Website"
          :label="t('companyWebsite')"
          maxlength="255"
          validation="required"
        />
        <FormKit
          type="text"
          id="LEADCF2"
          name="LEADCF2"
          maxlength="255"
          :label="t('message')"
        />
        <div class="py-4 lg:col-span-2">
          <FormKit
            autocomplete="off"
            id="privacyTool5550753000005085011"
            type="checkbox"
            name="privacyTool"
            validation="accepted"
            :label="t('privacyPolicy')"
            :value="true"
            :validation-messages="{
              accepted:
                'Please agree to the Privacy Policy and Terms of Service. Visit Seekers.my/Terms for more information.',
            }"
            :classes="{ wrapper: 'flex gap-4 items-center', input: 'checkbox' }"
          />
        </div>
        <NuxtTurnstile v-model="token" ref="turnstile" />
        <button class="btn bg-[#E71D36] text-white lg:col-span-2" type="submit">
          {{ t("button") }}
        </button>
      </FormKit>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n({
  useScope: "local",
});

const token = useState("token", () => "");

// async function handleSubmit(event: any) {
//   console.log("Test form submission");

//   const turnstileResponse = await $fetch("/api/validateTurnstile", {
//     method: "POST",
//     body: {
//       token: token.value,
//     },
//   });

//   console.log("Turnstile response", turnstileResponse);

//   if (turnstileResponse.success === true) {
//     const formResponse = await $fetch(
//       "https://crm.zoho.com/crm/WebToLeadForm",
//       {
//         method: "POST",
//         headers: {
//           "Content-Type": "multipart/form-data",
//         },
//         body: {
//           formData: event.formData,
//         },
//       }
//     );

//     console.log("Form response", formResponse);
//   } else {
//     console.log("Turnstile response is false");
//   }
// }
</script>

<i18n lang="json">
{
  "en": {
    "name": "Name*",
    "companyName": "Company Name*",
    "email": "Email Address*",
    "mobilePhone": "Mobile Phone*",
    "companyWebsite": "Company Website*",
    "message": "Message",
    "privacyPolicy": "Agree to the Privacy Policy and Terms of Service.",
    "button": "Submit"
  },
  "zh": {
    "name": "姓名",
    "companyName": "公司名称",
    "email": "邮箱",
    "mobilePhone": "联系电话",
    "companyWebsite": "公司网站",
    "message": "信息内容",
    "privacyPolicy": "向本网站提交您的邮件地址和其他个人信息表示您同意本网站依据其隐私政策、条款和条件，收集、持有、使用和披露这些信息。",
    "button": "提交"
  }
}
</i18n>
