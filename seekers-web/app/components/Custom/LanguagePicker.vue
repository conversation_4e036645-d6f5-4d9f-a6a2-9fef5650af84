<template>
  <div class="grid gap-4">
    <div
      class="grid grid-cols-[4fr_4fr_1fr] gap-4"
      v-for="(l, index) in languages"
      :key="index"
    >
      <select
        v-model="languages[index].language"
        class="select select-bordered"
      >
        <option v-for="lang in languageOptions" :value="lang.label">
          {{ lang.label }}
        </option>
      </select>
      <select v-model="languages[index].level" class="select select-bordered">
        <option v-for="level in languageLevels" :value="level">
          {{ level }}
        </option>
      </select>
      <button
        :disabled="index == 0"
        @click="removeFromList(index)"
        class="btn btn-error"
      >
        x
      </button>
    </div>
    <button
      class="btn"
      @click="
        languages.push({
          selected: true,
          language: '',
          level: '',
        })
      "
      type="button"
      style="width: fit-content"
    >
      Add New Language
    </button>
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
  },
});
const emit = defineEmits(["update:modelValue"]);

const languageOptions = useHelper.languageOptions;
const languageLevels = useHelper.languageLevels;
const languages = ref(props.modelValue as any);

const removeFromList = (index: number) => languages.value.splice(index, 1);

watch(
  languages,
  (val) => {
    emit("update:modelValue", val);
  },
  { deep: true }
);
</script>
