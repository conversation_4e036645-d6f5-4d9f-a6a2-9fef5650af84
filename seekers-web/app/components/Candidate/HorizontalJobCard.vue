<template>
  <nuxt-link
    :to="`/jobs/${job?.slug}`"
    class="flex items-center shadow rounded-md p-4 gap-4 text-sm mb-3"
  >
    <img
      :src="job?.company.logo_url"
      alt=""
      class="w-10 h-10 rounded-md object-contain"
    />
    <div>
      <div class="flex items-center">
        <p class="font-semibold">
          {{ job?.title }}
        </p>
      </div>
      <div class="grid md:flex md:items-center md:gap-4 text-xs my-2">
        <p class="text-gray-500 capitalize">
          <Icon name="heroicons:briefcase" class="w-4 h-4 mr-1" />{{
            job?.parent_role_slug
          }}
        </p>
        <p class="text-gray-500 my-1 md:my-0" v-if="job?.city !== null">
          <Icon name="heroicons:map-pin" class="w-4 h-4 mr-1" />{{ job?.city }}
        </p>
        <p class="text-gray-500 mb-1 md:mb-0">
          <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />{{
            getRelatedDate(job?.updated_at)
          }}
        </p>
        <p class="text-gray-500">
          <Icon name="heroicons:banknotes" class="w-4 h-4 mr-1" />{{
            job?.salary_range
          }}
        </p>
      </div>
      <div class="flex items-center gap-4">
        <p class="badge badge-success text-xs">Full Time</p>
        <p class="badge badge-info text-xs">Permanent</p>
        <p
          v-if="closingSoon(job?.created_at || '')"
          class="badge badge-warning text-xs"
        >
          Urgent
        </p>
      </div>
    </div>
  </nuxt-link>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

defineProps({
  job: {
    type: Object,
    default: {},
  },
});
const getRelatedDate = useHelper.getRelatedDate;

function closingSoon(date: string) {
  // return true if date is more than 21 days old from today
  return dayjs().diff(dayjs(date), "day") > 21;
}
</script>
