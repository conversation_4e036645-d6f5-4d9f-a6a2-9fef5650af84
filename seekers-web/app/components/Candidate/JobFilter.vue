<template>
  <div id="job-filter">
    <div class="sticky top-12 flex flex-col gap-4">
      <!-- Keywords -->
      <div>
        <p class="mb-2 font-semibold">
          Search by Keywords<span
            class="text-xs font-light ml-2 text-error-content cursor-pointer"
            @click="searchKeyword = ''"
            v-if="searchFilter.keywords && searchFilter.keywords.length > 0"
            >Clear</span
          >
        </p>
        <input
          type="text"
          name="search"
          placeholder="Job title, keywords, or company"
          class="input w-full text-xs"
          v-model="searchKeyword"
        />
      </div>

      <!-- States -->
      <!-- <div
        v-if="
          (!$route.fullPath.includes('recruiter/dashboard') &&
            filterExpanded) ||
          !isMobile
        "
      >
        <p class="mb-2 font-semibold">Location</p>
        <select
          class="select w-full outline-0"
          v-model="searchState"
          placeholder=""
        >
          <option class="options" :value="''">All</option>
          <option class="options" v-for="state in malaysianStates">
            {{ state }}
          </option>
        </select>
      </div> -->

      <!-- Categories -->
      <div>
        <p class="mb-2 font-semibold">Category</p>
        <select
          class="select w-full outline-0"
          @change="handleCategoryChange"
          placeholder=""
          v-model="selectedCategory"
        >
          <option class="options" disabled selected :value="''">
            Choose a category
          </option>
          <option
            class="options"
            v-for="category in jobCategories"
            :value="category.name"
          >
            {{ category.name }}
          </option>
        </select>
      </div>

      <!-- Salary slider -->
      <div id="salary-slider" v-if="filterExpanded || !isMobile">
        <p class="mb-2 font-semibold">Salary</p>
        <CustomRangeSlider
          :min="0"
          :max="15000"
          rangeColor="#34A853"
          sliderColor="#e0e0e0"
          :from="salarySliderMin"
          :to="salarySliderMax"
          @fromChange="salarySliderMin = $event"
          @toChange="salarySliderMax = $event"
        />
        <p
          class="rounded-lg p-2 bg-gray-300 w-max mx-auto text-xs font-semibold text-accent px-2"
        >
          RM{{ salarySliderMin }} - RM{{ salarySliderMax }}
        </p>
      </div>

      <div id="job-type-toggles" v-if="filterExpanded || !isMobile">
        <p class="mb-2 font-semibold">Job Type</p>
        <div class="grid gap-4">
          <div class="flex gap-4 items-center text-sm">
            <input
              type="checkbox"
              class="toggle scale-75 toggle-primary"
              v-model="searchFilter.includeFullTime"
              @change="handleToggleJobTypes"
            />
            <span>Full Time</span>
          </div>
          <div class="flex gap-4 items-center text-sm">
            <input
              type="checkbox"
              class="toggle scale-75 toggle-primary"
              v-model="searchFilter.includeContract"
              @change="handleToggleJobTypes"
            />
            <span>Contract</span>
          </div>
          <div class="flex gap-4 items-center text-sm">
            <input
              type="checkbox"
              class="toggle scale-75 toggle-primary"
              v-model="searchFilter.includeInternship"
              @click="handleToggleJobTypes"
            />
            <span>Internship</span>
          </div>
        </div>
      </div>

      <!-- <div id="date-posted">
        <p class="mb-2 font-semibold">Date Posted</p>
        <div class="grid gap-4">
          <div
            class="flex gap-4 items-center text-sm"
            v-for="option in postedDateOptions"
          >
            <input
              type="radio"
              class="radio bg-white checked:radio-primary radio-sm md:radio-xs"
              v-model="searchPostedDate"
              :value="option.value"
              name="PostedDate"
            />
            <span>{{ option.text }}</span>
          </div>
        </div>
      </div> -->

      <div id="exp-level" v-if="filterExpanded || !isMobile">
        <p class="mb-2 font-semibold">Experience Level</p>
        <div class="grid gap-4">
          <div class="flex gap-4 items-center text-sm">
            <input
              type="checkbox"
              class="checkbox rounded bg-white checked:checkbox-primary checkbox-sm md:checkbox-xs"
              @click="clearOtherLevelCheckboxes"
              v-model="selectedAllExperienceLevels"
            />
            <span>All</span>
          </div>
          <div
            class="flex gap-4 items-center text-sm"
            v-for="option in experienceLevels"
          >
            <input
              type="checkbox"
              class="checkbox rounded bg-white checked:checkbox-primary checkbox-sm md:checkbox-xs"
              v-model="option.checked"
            />
            <span>{{ option.text }}</span>
          </div>
        </div>
      </div>
      <button
        class="mt-2 md:hidden btn"
        @click="filterExpanded = true"
        v-if="!filterExpanded && isMobile"
      >
        More Filters
      </button>
      <button
        v-if="filterExpanded && isMobile"
        class="mt-8 md:hidden btn btn-primary"
        @click="applyFilterMobile"
      >
        Apply Filter
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["search"]);
const { isMobile } = useDevice();
const route = useRoute();
const router = useRouter();
const searchKeyword = ref("");
const malaysianStates = useHelper.malaysianStates;
const jobCategories = useHelper.jobCategories;
const searchFilter = useJobs().searchFilter();
const filterExpanded = ref(false);
const selectedAllExperienceLevels = ref(true);
const salarySliderMin = ref(0);
const salarySliderMax = ref(15000);
const selectedCategory = ref("");
const experienceLevels = ref(useHelper.experienceLevels);

// get route query and override search filter
const queries = route.query;
if (queries.keywords) {
  searchKeyword.value = queries.keywords as string;
}
if (queries.levels) {
  const levels = (queries.levels as string).split(",");
  experienceLevels.value.forEach((level) => {
    selectedAllExperienceLevels.value = false;
    level.checked = levels.includes(level.value);
  });
}
if (queries.category) {
  const category = useHelper.jobCategoryLookupByName(
    queries.category as string
  );
  searchFilter.value.roles = category ? category.roles.join(",") : "";
  selectedCategory.value = queries.category as string;
}

function updateUrlQuery(key: string, value: string | null) {
  const queries = { ...route.query };
  if (value == null || value == "0" || value == "") {
    delete queries[key];
    router.replace({
      query: queries,
    });
  } else {
    router.replace({
      query: {
        ...route.query,
        [key]: value,
      },
    });
  }
}

function handleCategoryChange(e: any) {
  const categoryName = e.target.value;
  if (categoryName == "All") {
    searchFilter.value.roles = "";
    updateUrlQuery("category", null);
    emit("search");
  } else {
    updateUrlQuery("category", categoryName);
    const category = useHelper.jobCategoryLookupByName(categoryName);
    searchFilter.value.roles = category ? category.roles.join(",") : "";
    emit("search");
  }
}

function handleToggleJobTypes() {
  let arr = [];
  if (searchFilter.value.includeFullTime) {
    arr.push("Permanent");
  }
  if (searchFilter.value.includeContract) {
    arr.push("Contract");
  }
  searchFilter.value.job_types = arr.join(",");
  emit("search");
}

function clearOtherLevelCheckboxes() {
  if (!selectedAllExperienceLevels.value) {
    experienceLevels.value.forEach((level) => {
      level.checked = false;
    });
  }
  emit("search");
}

watchDebounced(
  searchKeyword,
  async () => {
    updateUrlQuery("keywords", searchKeyword.value);
    searchFilter.value.keywords = searchKeyword.value;
    emit("search")
  },
  { debounce: 500, maxWait: 1000 },
)

watchDebounced(
  salarySliderMin,
  () => {
    updateUrlQuery("salary_min", salarySliderMin.value.toString());
    searchFilter.value.salary_min = salarySliderMin.value;
    emit("search")
  },
  { debounce: 500, maxWait: 100 },
)

watchDebounced(
  salarySliderMax,
  () => {
    updateUrlQuery("salary_max", salarySliderMax.value.toString());
    searchFilter.value.salary_max = salarySliderMax.value;
  },
  { debounce: 500, maxWait: 1500 },
)

watch(
  experienceLevels,
  (newVal) => {
    let str = "";
    newVal.forEach((level) => {
      if (level.checked) {
        selectedAllExperienceLevels.value = false;
        str += level.value + ",";
      }
    });
    // remove last comma
    str = str.slice(0, str.length - 1);
    // if no checkbox is checked, set to all
    if (str === "") {
      selectedAllExperienceLevels.value = true;
      str = "";
    }
    searchFilter.value.levels = str;
    updateUrlQuery("levels", str);
    emit("search");
  },
  { deep: true }
);

const applyFilterMobile = () => {
  useHelper.scrollToTop();
  filterExpanded.value = false;
};
</script>
