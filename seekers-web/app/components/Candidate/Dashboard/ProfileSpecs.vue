<template>
  <div
    class="flex text-sm gap-6"
    v-if="candidate?.work_experiences?.length > 0"
  >
    <Icon name="heroicons:calendar" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Experience</p>
      <p class="font-light text-accent capitalize">
        {{
          dayjs(
            candidate?.work_experiences[candidate?.work_experiences?.length - 1]
              .end
          ).diff(dayjs(candidate?.work_experiences[0]?.start), "year")
        }}
        Years
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6">
    <Icon name="heroicons:academic-cap" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Education Level</p>
      <p class="font-light text-accent capitalize">
        {{ candidate?.education_level }}
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6" v-if="candidate?.current_salary">
    <Icon name="hugeicons:coins-01" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Current Salary</p>
      <p class="font-light text-accent capitalize">
        RM{{ candidate?.current_salary }}
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6" v-if="candidate?.expected_salary">
    <Icon name="cil:cash" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Expected Salary</p>
      <p class="font-light text-accent capitalize">
        RM{{ candidate?.expected_salary }}
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6">
    <Icon name="heroicons:finger-print-solid" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Gender</p>
      <p class="font-light text-accent capitalize">{{ candidate?.gender }}</p>
    </div>
  </div>
  <div class="flex text-sm gap-6">
    <Icon name="heroicons:phone" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Mobile</p>
      <p class="font-light text-accent capitalize">{{ candidate?.mobile }}</p>
    </div>
  </div>
  <div class="flex text-sm gap-6">
    <Icon name="heroicons:clock" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Age</p>
      <p class="font-light text-accent capitalize">
        {{ calculateAge(candidate?.birth_year) }}
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6">
    <Icon name="heroicons:language" class="w-6 h-6 text-primary min-w-6" />
    <div>
      <p class="mb-1">Languages</p>
      <p
        class="font-light text-accent capitalize break-word"
        v-if="candidate.language"
      >
        {{ candidate?.language.replaceAll(",", ", ") }}
      </p>
      <!-- <p
        v-else
        class="btn btn-text mt-4"
        @click="navigateTo('/candidate/dashboard/settings')"
      >
        Add Profile Details
      </p> -->
    </div>
  </div>
  <div class="flex text-sm gap-6" v-if="candidate?.is_local">
    <Icon name="heroicons:flag" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Nationality</p>
      <p class="font-light text-accent capitalize">
        {{ candidate?.is_local === 1 ? "Malaysian" : "Expatriate" }}
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6" v-if="candidate?.state">
    <Icon name="heroicons:building-library" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">State</p>
      <p class="font-light text-accent capitalize">
        {{ candidate?.state }}
      </p>
    </div>
  </div>
  <!-- <pre>{{ candidate }}</pre> -->
  <div
    class="justify-self-center"
    v-if="
      !candidate?.gender ||
      !candidate?.mobile ||
      !candidate?.birth_year ||
      !candidate?.state
    "
  >
    <p
      class="btn btn-text"
      @click="navigateTo('/candidate/dashboard/settings')"
    >
      Add More Details
    </p>
  </div>
  <div class="flex text-sm gap-6" v-if="candidate?.nationality">
    <Icon name="humbleicons:flag" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Nationality</p>
      <p class="font-light text-accent capitalize">
        {{ candidate?.nationality }}
      </p>
    </div>
  </div>
  <div class="flex text-sm gap-6" v-if="candidate?.location">
    <Icon name="humbleicons:map" class="w-6 h-6 text-primary" />
    <div>
      <p class="mb-1">Location</p>
      <p class="font-light text-accent capitalize">
        {{ candidate?.location }}
      </p>
    </div>
  </div>
</template>

<script setup>
import dayjs from "dayjs";
const candidate = useCandidate().profile;

// function calculate age based on birth year
function calculateAge(birthYear) {
  return new Date().getFullYear() - birthYear;
}
</script>
