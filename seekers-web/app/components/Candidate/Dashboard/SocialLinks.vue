<template>
  <div class="bg-info p-3 md:p-6 xl:p-8 px-6 rounded-lg text-sm">
    <div class="flex gap-3 items-center">
      <p class="mr-auto">
        Social Media
        <span>
          <Icon
            v-if="!editMode"
            name="heroicons:pencil"
            class="ml-2 w-4 h-4 animate__animated animate__fadeIn cursor-pointer"
            @click="editSocialLinks"
        /></span>
      </p>

      <div v-if="!editMode" class="flex gap-3">
        <a
          v-if="profile?.facebook"
          :href="appendhttps(profile?.facebook)"
          target="_blank"
          ><Icon name="bxl:facebook" class="w-6 h-6 text-[#4267B2]"
        /></a>
        <a
          v-if="profile?.twitter"
          :href="appendhttps(profile?.twitter)"
          target="_blank"
          ><Icon name="bxl:twitter" class="w-6 h-6 text-[#1DA1F2]"
        /></a>
        <a
          v-if="profile?.instagram"
          :href="appendhttps(profile?.instagram)"
          target="_blank"
          ><Icon name="bxl:instagram" class="w-6 h-6 text-[#833AB4]"
        /></a>
        <a
          v-if="profile?.linkedin"
          :href="appendhttps(profile?.linkedin)"
          target="_blank"
          ><Icon name="bxl:linkedin" class="w-6 h-6"
        /></a>
        <a
          v-if="profile?.github"
          :href="appendhttps(profile?.github)"
          target="_blank"
          ><Icon name="bxl:github" class="w-6 h-6"
        /></a>
        <a
          v-if="profile?.website"
          :href="appendhttps(profile?.website)"
          target="_blank"
          ><Icon name="heroicons:globe-alt" class="w-6 h-6"
        /></a>
      </div>
    </div>
    <div
      v-if="editMode"
      class="mt-6 grid grid-cols-[1fr_16fr] gap-4 items-center text-accent text-xs"
    >
      <Icon name="bxl:facebook" class="h-5 w-5" /><input
        class="input input-sm py-5"
        type="text"
        name="facebook"
        v-model="profile.facebook"
        v-on:keyup.enter="save"
      />
      <Icon name="bxl:twitter" class="h-5 w-5" /><input
        class="input input-sm py-5"
        type="text"
        name="twitter"
        v-model="profile.twitter"
        v-on:keyup.enter="save"
      />
      <Icon name="bxl:instagram" class="h-5 w-5" /><input
        class="input input-sm py-5"
        type="text"
        name="instagram"
        v-model="profile.instagram"
        v-on:keyup.enter="save"
      />
      <Icon name="bxl:linkedin" class="h-5 w-5" /><input
        class="input input-sm py-5"
        type="text"
        name="linkedin"
        v-model="profile.linkedin"
        v-on:keyup.enter="save"
      />
      <Icon name="bxl:github" class="h-5 w-5" /><input
        class="input input-sm py-5"
        type="text"
        name="github"
        v-model="profile.github"
        v-on:keyup.enter="save"
      />
      <Icon name="heroicons:globe-alt" class="h-5 w-5" /><input
        class="input input-sm py-5"
        type="text"
        name="website"
        v-model="profile.website"
        v-on:keyup.enter="save"
      />
      <button class="btn btn-primary col-span-2" @click="save">Save</button>
    </div>
  </div>
</template>

<script setup lang="ts">
const updateProfile = useCandidate().updateProfile;
const editMode = useState<boolean>();
const profile = useCandidate().profile;

function editSocialLinks() {
  editMode.value = true;
}
function appendhttps(url: string) {
  // check if url has https, if not append.
  let newUrl = url;
  if (!url.includes("https")) {
    newUrl = "https://" + url;
  }
  //if url has http, not https, replace http with https
  if (url.includes("http") && !url.includes("https")) {
    newUrl = url.replace("http", "https");
  }
  return newUrl;
}

async function save() {
  await updateProfile({
    facebook: profile.value.facebook || "",
    twitter: profile.value.twitter || "",
    instagram: profile.value.instagram || "",
    linkedin: profile.value.linkedin || "",
    github: profile.value.github || "",
    website: profile.value.website || "",
  });
  editMode.value = false;
}
</script>
