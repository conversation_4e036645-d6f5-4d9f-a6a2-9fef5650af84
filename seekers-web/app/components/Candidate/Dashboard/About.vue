<template>
  <div id="about-candidate">
    <div class="flex gap-4">
      <h3 class="mb-2 font-semibold">About Me</h3>
      <span class="cursor-pointer">
        <Icon
          v-if="!editMode"
          name="heroicons:pencil"
          class="w-5 h-5 animate__animated animate__fadeIn"
          @click="editText"
        />
        <Icon
          v-if="editMode"
          name="heroicons:check-solid"
          class="w-6 h-6 text-primary animate__animated animate__fadeIn"
          @click="save"
        />
        <Icon
          v-if="false"
          name="humbleicons:upload"
          class="w-6 h-6 animate__animated animate__fadeIn"
          @click="save"
        />
      </span>
    </div>
    <div
      id="about-text"
      v-if="!editMode"
      class="flex flex-col gap-4 whitespace-pre-line"
    >
      {{
        profile?.about_me
          ? profile?.about_me
          : "Write something about yourself so others can get to know you better."
      }}
    </div>
    <textarea
      id="about-editor"
      v-else
      class="p-2 w-full outline outline-primary rounded min-h-16"
      :style="`height: ${getAboutHeight()! + 20}px`"
      :value="profile?.about_me"
      @input="autoGrow($event.target)"
    ></textarea>
  </div>
</template>

<script setup lang="ts">
const editMode = ref(false);
const profile = useCandidate().profile;

function editText() {
  editMode.value = true;
}

function save() {
  const about_me = document.getElementById(
    "about-editor"
  ) as HTMLTextAreaElement;

  profile.value = {
    ...profile.value,
    about_me: about_me.value,
  };

  useCandidate().updateProfile({
    about_me: about_me.value,
  });

  editMode.value = false;
}

const getAboutHeight = () => {
  // get height of about-text div in px
  const aboutTextDiv = document.getElementById("about-text");
  const aboutTextHeight = aboutTextDiv?.clientHeight;
  return aboutTextHeight;
};

function autoGrow(oField: any) {
  if (oField.scrollHeight > oField.clientHeight) {
    oField.style.height = `${oField.scrollHeight}px`;
  }
}
</script>
