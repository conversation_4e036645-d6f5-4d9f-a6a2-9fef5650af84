<template>
  <CustomVerticalStep
    :char="addNew ? '+' : data?.company_name?.charAt(0).toUpperCase()"
    :lastStep="lastStep"
    bgColor="success"
    contentColor="success-content"
    :class="{ 'opacity-50': isLoading }"
  >
    <!-- Display mode -->
    <div
      id="education-item"
      class="grid pl-6 pb-6 gap-1"
      v-if="!editMode && !addNew"
    >
      <div class="flex md:items-center w-full gap-1 md:gap-3">
        <div class="flex flex-col md:items-center md:flex-row gap-1 md:gap-3">
          <div class="md:hidden text-xs -mb-1">
            {{ dayjs(data.start).format("YYYY") }} -
            {{ dayjs(data.end).format("YYYY") }}
          </div>
          <p class="text-lg">{{ data.role }}</p>
          <div class="hidden md:block">
            <div
              class="bg-success text-succes-content text-xs rounded-full p-1 px-2 w-max"
            >
              {{ dayjs(data.start).format("YYYY") }} -
              {{ dayjs(data.end).format("YYYY") }}
            </div>
          </div>
        </div>
        <button
          class="btn btn-circle btn-sm md:btn-xs ml-auto md:ml-3"
          v-if="editAllowed"
          @click="editMode = true"
        >
          <Icon
            name="heroicons:pencil"
            class="h-4 w-4 md:w-3 md:h-3 animate__animated animate__fadeIn"
          />
        </button>
      </div>
      <p class="text-success-content text-sm">
        {{ data.company_name }}
      </p>
      <p
        class="mt-3 text-gray-500 text-sm"
        v-html="replaceNewline(data.responsibilities as string)"
      ></p>
    </div>

    <!-- Add new -->
    <p
      v-if="addNew && !editMode"
      class="text-success-content p-1 pl-6 cursor-pointer"
      @click="editNew"
    >
      Add New
    </p>

    <!-- Edit mode -->
    <div class="grid gap-4 pl-6 pb-12 animate__animated animate__fadeIn animate__fast" v-if="editMode">
      <input
        placeholder="Role title or Position"
        type="text"
        class="input text-sm outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96"
        v-model="data.role"
      />
      <input
        v-model="data.company_name"
        placeholder="Company Name"
        type="text"
        class="input text-sm outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96"
      />

      <div class="flex flex-col md:flex-row md:items-center gap-3 text-xs">
        <span> From: </span>
        <FormKit
          type="date"
          :value="dayjs(data.start).format('YYYY-MM-DD')"
          @change="data.start = $event.target.value"
          :input-class="'p-4 mt-0 text-xs'"
        />
        <span>To:</span>
        <FormKit
          type="date"
          :value="dayjs(data.start).format('YYYY-MM-DD')"
          @change="data.end = $event.target.value"
          :input-class="'p-4 mt-0 text-xs'"
        />
      </div>
      <textarea
        v-model="data.responsibilities"
        placeholder="Describe your responsibilities and achievements"
        type="textarea"
        class="outline outline-gray-200 focus:outline-primary rounded p-2 w-full h-40 md:h-24 text-sm"
      ></textarea>

      <div class="flex gap-4 flex-wrap" v-if="editMode">
        <button
          class="btn btn-primary px-6 md:px-12 py-2"
          @click="addNew ? create() : save()"
        >
          Save
        </button>
        <button class="btn px-6 md:px-12 py-2" @click="editMode = false">
          Cancel
        </button>
        <button class="btn" v-if="!addNew" @click="deleteWorkExperience">
          <Icon name="heroicons:trash" />
        </button>
      </div>
    </div>
  </CustomVerticalStep>
</template>
<script setup lang="ts">
import dayjs from "dayjs";

const { value, lastStep } = defineProps<{
  value: WorkExperienceItem;
  lastStep?: boolean;
  addNew?: boolean;
  editAllowed?: boolean;
}>();
const isLoading = useState(() => false);

const editMode = ref(false);
const data = ref({ ...value });

function validateData() {
  if (
    !data.value.start ||
    !data.value.end ||
    !data.value.role ||
    !data.value.company_name ||
    !data.value.responsibilities
  ) {
    alert("Please fill all the fields");
    return false;
  } else {
    return true;
  }
}

function replaceNewline(str: string) {
  return str.replace(/\r\n/g, "<br>");
}

function editNew() {
  data.value = {
    start: dayjs().format("YYYY-MM-DD"),
    end: dayjs().format("YYYY-MM-DD"),
    company_name: "",
    role: "",
    responsibilities: "",
  };
  editMode.value = true;
}
async function create() {
  if (validateData()) {
    isLoading.value = true;
    await useCandidate().createWorkExperience(data.value);
    await useCandidate().getProfile();
    isLoading.value = false;
    editMode.value = false;
  }
}

async function save() {
  if (validateData()) {
    isLoading.value = true;
    await useCandidate().updateWorkExperience(data.value);
    location.reload();
  }
}

async function deleteWorkExperience() {
  if (confirm(`Confirm Delete ${data.value.company_name} ?`) && data.value.id) {
    isLoading.value = true;
    await useCandidate().deleteWorkExperience(data.value.id);
    await useCandidate().getProfile();
    isLoading.value = false;
  }
}
</script>
