<template>
  <!-- background linier gradient -->
  <div
    id="profile-header"
    class="grid items-center gap-6 bg-gradient-to-r from-blue-100 to-green-100 py-12 animate__animated animate__fadeIn"
  >
    <div>
      <div class="flex flex-col items-center">
        <!-- avatar -->
        <img
          class="w-24 h-24 rounded-full mx-auto object-cover"
          :src="profileImg"
          alt="profile avatar"
          transition-style="in:circle:bottom-left"
        />
        <!-- opentowork tag -->
        <p
          class="py-0.5 px-2 text-xs -mt-2 z-10 relative cursor-pointer"
          :class="
            openToWork ? 'bg-primary text-white' : 'bg-error text-error-content'
          "
          @click="navigateTo('/candidate/dashboard/settings')"
        >
          {{ openToWork === 1 ? "Open To Work" : "Not Open To Work" }}
        </p>
        <!-- name and designation -->
        <h1 class="text-accent font-semibold">{{ name }}</h1>
        <p class="text-sm text-primary">{{ designation }}</p>
      </div>
    </div>

    <!-- tags location and cv generation-->
    <div
      class="grid gap-4 lg:grid-cols-[2fr_4fr_2fr] justify-center w-4/5 mx-auto"
    >
      <div id="profile-tags" class="flex gap-2 justify-center items-center">
        <div
          class="btn btn-success btn-xs rounded-full"
          v-for="tag in tags.slice(0, 3)"
        >
          {{ tag }}
        </div>
      </div>

      <div
        id="profile-location"
        class="flex gap-2 lg:gap-4 items-center justify-center text-accent text-xs w-full flex-wrap"
      >
        <div class="flex gap-1 flex-nowrap justify-center">
          <Icon name="heroicons:map-pin" class="h-4 w-4" />
          <span v-if="state">{{ state }}</span>
          <nuxt-link v-else to="/candidate/dashboard/settings"
            >Click to set State</nuxt-link
          >
        </div>
        <!-- email -->
        <div class="flex gap-1 flex-nowrap justify-center">
          <Icon name="heroicons:at-symbol" class="h-4 w-4" />
          <span>{{ email }}</span>
        </div>
        <!-- memeber since -->
        <div class="flex gap-1 flex-nowrap justify-center">
          <Icon name="heroicons:calendar" class="h-4 w-4" />
          <span>Joined {{ dayjs(createdAt).format("MMMM YYYY") }}</span>
        </div>
      </div>
    </div>

    <!-- update cv status -->
    <div class="flex justify-center">
      <!-- <button class="btn btn-primary px-8 btn-sm" @click="clickDownloadCV">
          Download CV
        </button> -->
      <button
        class="flex items-center gap-1 text-xs text-accent"
        @click="navigateTo('/candidate/dashboard/settings')"
      >
        <Icon
          name="heroicons:cog-6-tooth"
          class="w-5 h-5 animate__animated animate__fadeIn"
        /><span class="">Update CV & Status</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
const { name, designation, email, profileImg, openToWork, createdAt, tags } =
  defineProps({
    profileImg: {
      type: String,
      default:
        "https://seekers.my/blog/content/images/size/w100/2020/07/seekers_favicon.jpg",
    },
    name: {
      type: String,
      default: "Seekers Joe",
    },
    designation: {
      type: String,
      default: "Fresh Graduate",
    },
    openToWork: {
      type: Number,
      default: 1,
    },
    createdAt: {
      type: String,
      default: "Jan 2024",
    },
    tags: {
      type: Array,
      default: ["Marketing", "Design", "Digital"],
    },
    email: {
      type: String,
      default: "Your Email",
    },
    state: {
      type: String,
    },
  });

const profileCompletion = useCandidate().profileCompletion;

function clickDownloadCV() {
  if (profileCompletion.value != 100) {
    alert("Please complete profile to 100% first");
  } else {
    alert("Feature coming soon");
  }
}
</script>
