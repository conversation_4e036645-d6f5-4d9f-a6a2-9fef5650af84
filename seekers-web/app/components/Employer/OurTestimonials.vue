<template>
  <section id="our-testimonials" class="bg-[#CE643A] lg:bg-inherit">
    <div class="md:relative w-full -z-">
      <div class="md:h-full md:w-full md:absolute -z-10 md:overflow-hidden">
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1694568316/website/employerpage/testimonial-bg_zkiklq.svg"
          class="hidden md:block"
        />
      </div>

      <div class="max-w-7xl mx-auto md:py-40 py-12">
        <div
          class="md:grid md:grid-cols-[2fr_5fr] lg:grid-cols-[2fr_6fr] flex flex-col-reverse md:gap-x-8 gap-y-4 mx-8"
        >
          <div class="mt-auto border rounded-xl shadow-2xl p-12 mb-6">
            <div
              v-for="role in roles"
              class="btn btn-F5F7FB hover:bg-black hover:text-white w-full mt-2"
              @click="currentSlide = role"
              :class="{ 'bg-black text-white': currentSlide.cat == role.cat }"
            >
              {{ role.cat }}
            </div>
          </div>

          <div>
            <h2 class="font-semibold text-white">Our Testimonials</h2>
            <p class="text-sm text-white mb-8">
              We can serve better thanks to all the clients.
            </p>
            <div class="bg-[#F5F7FB] border rounded-md px-6 py-12">
              <h3 class="font-semibold mb-4">{{ currentSlide.industry }}</h3>
              <img :src="currentSlide.img" alt="" class="h-6 scale-y-75 mb-3" />
              <p>
                {{ currentSlide.description }}
              </p>
            </div>
          </div>
        </div>

        <div class="grid md:grid-cols-[2fr_6fr] md:gap-x-8 gap-y-4 mx-8">
          <div
            class="flex justify-center items-center gap-4 bg-[#2D1717]/75 rounded-xl p-2 shadow-[10px_10px_0_rgb(45,23,23,0.26)]"
          >
            <div
              v-for="client in clients"
              class="text-center text-white font-semibold"
            >
              <p class="text-2xl">{{ client.numbers }}</p>
              <p class="text-[8px]">{{ client.text }}</p>
            </div>
          </div>

          <div class="text-white text-2xl font-bold md:w-2/5 mt-6 md:mt-0">
            <p>“Elevate your business to new heights.”</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const clients = [
  {
    numbers: "500+",
    text: "IT & DIGITAL CLIENTS",
  },
  {
    numbers: "5,000+",
    text: "MALAYSIAN CLIENTS",
  },
  {
    numbers: "100+",
    text: "GLOBAL CLIENTS",
  },
];

const roles = [
  {
    cat: "Information Technology",
    industry: "Software Industry",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674495677/website/employerpage/rating-4star_anxnqo.svg",
    description:
      "Collaborating with Seekers MY is really good and comfortable as we have been using their headhunting service since 2020. They have helped us secure handful of candidates as our business has been expanding over the years.",
  },
  {
    cat: "Digital Agency",
    industry: "Digital Agency",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674495678/website/employerpage/rating-5star_ib7iqj.svg",
    description:
      "Seekers helped us expand our IT team in Malaysia, we started with remote talents based in Johor Bahru before proceeding with talents based in Kuala Lumpur. Our company has more hiring needs coming up and we are glad to continue this collaboration with Seekers MY.",
  },
  {
    cat: "BPO",
    industry: "BPO",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674495677/website/employerpage/rating-4star_anxnqo.svg",
    description:
      "We have successful collaboration with Seekers MY over the years for foreign language-based talents. The candidates sourced by the team come with great knowledge and background, helping us fulfill our commercial needs ASAP.",
  },
  {
    cat: "Advertising /Marketing",
    industry: "Advertising / Marketing Industry",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674495678/website/employerpage/rating-5star_ib7iqj.svg",
    description:
      "We started reaching out to Seekers MY to support our Sales hirings . The team's ability to engage with passive candidates and enhancing our job description according to market situation, is valuable for employers to stay ahead of competitors.",
  },
  {
    cat: "Customer Service",
    industry: "Consulting Industry",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674495677/website/employerpage/rating-4star_anxnqo.svg",
    description:
      "Seekers MY helped us find a talent that would not only fit the job description, but also fit into our company culture. It's crucial to provide information about the company's environment and also previous hiring challenges so Seekers could offer the best solution.",
  },
];

const currentSlide = ref(roles[0]);
</script>
