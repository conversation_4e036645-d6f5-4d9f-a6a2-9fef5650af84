<template>
  <section id="latest-news" class="md:max-w-7xl md:mx-auto m-4 my-12 md:my-24">
    <h2 class="font-semibold text-[#CE643A] mb-4">Latest News</h2>
    <div class="md:flex mb-8">
      <p>
        Perform like a professional HR? It's all about the modern knowledge of
        smart tools.<br />Let's catch up the new trend & latest news here.
      </p>
      <NuxtLink to="/hrtrends" no-prefetch class="md:ml-auto text-blue-600"
        >Browse All ></NuxtLink
      >
    </div>

    <div class="grid md:grid-cols-4 md:max-w-7xl md:mx-auto gap-4">
      <div v-for="post in blogData?.posts">
        <NuxtLink :to="post.url" target="blank">
          <div>
            <img
              v-if="post.feature_image || post.twitter_image"
              :src="
                post.feature_image
                  ? post.feature_image.replace('/images/', '/images/size/w300/')
                  : post.twitter_image.replace('/images/', '/images/size/w300/')
              "
              alt=""
              class="rounded-2xl w-full h-full object-contain"
            />
            <img
              v-else
              src="@/static/seekers-malaysia.png"
              alt=""
              class="rounded-2xl w-full h-full object-contain"
            />
          </div>
          <p class="text-xs pt-2">
            {{ dayjs(post.updated_at).format("DD MMMM YYYY") }}
          </p>
          <p class="pt-1">{{ post.title }}</p>
        </NuxtLink>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const { data: blogData } = useBlog().latestPosts();
</script>
