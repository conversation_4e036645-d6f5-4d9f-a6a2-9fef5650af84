<template>
  <section
    id="leverage-top-talent"
    class="bg-[#34A853] bg-opacity-[.04] py-20 md:pt-24"
  >
    <div class="max-w-7xl mx-4 md:mx-auto">
      <div class="text-center grid gap-6 mb-8">
        <h2 class="text-4xl font-semibold">
          Leverage Top IT & Digital
          <span class="font-bold italic">Talent</span>
        </h2>
        <div class="font-semibold">
          <p>
            We specialize in connecting top IT and digital talent with
            businesses.
          </p>
          <p>
            If you're ready to apply for your next role, we're here to help.
          </p>
          <p>Connect with us today!</p>
        </div>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        <div
          v-for="item in items"
          class="rounded-xl p-10 grid h-[20rem] border drop-shadow-2xl"
          :class="item.bgClass"
        >
          <Icon :name="item.icon" class="text-primary w-20 h-20" />
          <p class="font-bold text-xl italic">{{ item.title }}</p>
          <p class="text-sm">
            {{ item.text }}
          </p>
        </div>
      </div>

      <div class="grid justify-center">
        <NuxtLink to="/candidate/login" class="btn btn-primary px-12"
          >Get Offer</NuxtLink
        >
      </div>
    </div>
  </section>
</template>

<script setup>
const items = [
  {
    icon: "material-symbols:code-rounded",
    title: "Software Developers",
    text: "PHP, Python, Laravel, ASP.NET, Javascript, iOS, Android, Python, JavaScript, SQL, AWS, Flutter, HTML, C/C++, Golang, Kotlin, and more..",
  },
  {
    bgClass: "bg-gradient-to-bl from-[#9DD1AB] to-[#CCE5D3]/40",
    icon: "material-symbols:design-services-outline",
    title: "Digital Marketing",
    text: "Digital Marketing, SEO Specialist, SEM Specialist, SEMRUSH, Email Campaign, Google Analytics, Paid Ads, Content Marketing and more..",
  },
  {
    icon: "material-symbols:manage-accounts-outline",
    title: "Data-Related Jobs",
    text: "Data Scientist, Data Analyst, Data Engineer, Business Inteligence (BI) Analyst, Machine Learning Engineer, Data Architect, Big Data Engineer and more..",
  },
  {
    bgClass: "bg-gradient-to-br from-[#9DD1AB] to-[#CCE5D3]/40",
    icon: "material-symbols:finance-chip-outline",
    title: "Business",
    text: "Business Development, Product Manager, Business Analyst and more..",
  },
  {
    icon: "mdi:account-multiple-check-outline",
    title: "Creative",
    text: "Graphic Designer, UI/UX Designer, Video Editor and more..",
  },
  {
    bgClass: "bg-gradient-to-tl from-[#9DD1AB] to-[#CCE5D3]/40",
    icon: "material-symbols:trending-up-rounded",
    title: "Technical",
    text: "IT Project Manager, Network Engineer, DevOps Engineer and more..",
  },
];
</script>
