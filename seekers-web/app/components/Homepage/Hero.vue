<template>
  <div id="homepage-hero">
    <div id="hero-text" class="grid text-center">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1729343473/website/homepage/hero-bg_ndjxgr.svg"
        class="col-start-1 row-start-1 h-80 md:h-[26rem] lg:h-auto object-cover md:object-none w-full"
      />
      <div
        class="max-w-7xl mx-auto col-start-1 row-start-1 pt-6 md:pt-24 2xl:pt-48"
      >
        <div class="grid gap-8 xl:gap-20">
          <h1 class="md:text-5xl xl:text-8xl">
            Your <span class="font-bold italic">IT & DIGITAL</span
            ><br />Recruitment Specialist
          </h1>
          <div class="grid gap-10">
            <p class="text-lg xl:text-3xl opacity-50">
              Software Engineer | Digital | Technical
            </p>
            <div class="flex justify-center gap-4">
              <NuxtLink to="/jobs" class="btn btn-primary rounded-full px-8"
                >Find A Job</NuxtLink
              >
              <NuxtLink
                to="/hire-digital-tech-talent"
                class="btn btn-primary rounded-full px-8"
                >Hire A Talent</NuxtLink
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// const keywords = ref("");
// const states = useHelper.malaysianStates;
// const selectedState = useJobs().searchFilter().location;

// const { data: popularTagJobs, pending: popularTagJobsPending } =
//   useFetch("/api/jobs/by-tag");

// function search() {
//   navigateTo(`/jobs?keywords=${keywords.value}`);
// }
</script>
