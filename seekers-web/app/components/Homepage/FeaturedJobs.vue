<template>
  <section id="featured-jobs" class="py-12 my-12">
    <div class="text-center">
      <h2 class="font-semibold mb-2">{{ activeTab }}</h2>
      <p class="mb-6">
        Know your worth and find the job that qualify your life
      </p>

      <div
        class="grid grid-cols-3 gap-2 max-w-lg justify-between border rounded-full p-1 mx-auto mb-4"
      >
        <button
          class="btn border-0 rounded-full p-2 cursor-pointer"
          :class="{
            'btn-primary text-white': activeTab == tab.name,
            'bg-transparent': activeTab != tab.name,
          }"
          v-for="tab in tabs"
          :disabled="tab.disabled"
          @click="switchTab(tab.name)"
        >
          {{ tab.name }}
        </button>
      </div>
      <JobCardsList
        v-if="activeTab == 'Popular'"
        :jobsList="highlightedJobs?.data"
        :isLoading="highlightedJobsPending"
        :showPagination="false"
      />
      <JobCardsList
        v-if="activeTab == 'Recent'"
        :jobsList="latestJobs?.data"
        :isLoading="latestJobsPending"
        :showPagination="false"
      />
      <JobCardsList
        v-if="activeTab == 'Digital Talent'"
        :jobsList="latestTechJobs?.data"
        :isLoading="latestTechJobsPending"
        :showPagination="false"
      />
      <nuxt-link to="/jobs" class="btn btn-primary px-8" type="button"
        >View All</nuxt-link
      >
    </div>
  </section>
</template>

<script setup lang="ts">
type props = {
  highlightedJobs?: { data: Job[] } | null;
  latestJobs?: { data: Job[] } | null;
  latestTechJobs?: { data: Job[] } | null;
  highlightedJobsPending: boolean;
  latestJobsPending: boolean;
  latestTechJobsPending: boolean;
};
const props = defineProps<props>();

const activeTab = ref("Popular");
const tabs = [
  { name: "Popular", disabled: props.highlightedJobs?.data?.length === 0},
  { name: "Recent", disabled: props.latestJobs?.data?.length === 0},
  { name: "Digital Talent", disabled: props.latestTechJobs?.data?.length === 0 },
];

function switchTab(tab: string) {
  activeTab.value = tab;
}
</script>
