<template>
  <section id="dream-job" class="my-20 md:mt-24">
    <div class="grid md:grid-cols-2 md:mx-auto md:max-w-7xl">
      <!--LEFT CONTENT-->
      <div class="md:h-max h-72 mx-4">
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1681353791/website/homepage/image_225-min_j45oex.jpg"
          alt=""
          class="rounded-md"
        />
        <!-- <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670721593/website/homepage/dream-job_h1bhaw.jpg"
          alt=""
          class="rounded-md"
        />
        <div
          class="absolute md:-bottom-22 md:-right-36 -bottom-28 -right-16 border rounded-md bg-white"
        >
          <p
            class="text-center bg-primary rounded-t-md text-white text-sm py-2"
          >
            Applicant List
          </p>
          <div class="grid gap-2 p-2">
            <div v-for="item in items" class="flex items-center gap-2">
              <img
                :src="item.img"
                alt=""
                class="row-span-4 place-self-center rounded-full"
              />
              <div class="">
                <p class="font-semibold text-sm">
                  {{ item.name }}
                </p>
                <p class="text-xs">
                  {{ item.position }}
                </p>
              </div>
            </div>
          </div>
        </div> -->
      </div>

      <!--RIGHT CONTENT-->
      <div class="mx-4">
        <h3 class="text-primary font-bold">
          Let Us Help You To Find Your Next Hire
        </h3>
        <h2 class="font-semibold mb-6">Find Your Dream Job With Seekers</h2>
        <p class="mb-6">
          Having trouble securing a job and want us to find a job adapted to
          your needs?
        </p>
        <p class="mb-8">
          Create an account and tell us a little more about your career
          aspirations and dream job, or simply upload your CV.
        </p>
        <nuxt-link to="/register" class="btn btn-primary"
          >Register Now</nuxt-link
        >
      </div>
    </div>
  </section>
</template>

<script setup>
const items = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674494672/website/homepage/applicant-1_j0hdal.jpg",
    name: "Efzal Rifqi",
    position: "Web Developer",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674494748/website/homepage/applicant-2_fjmtq4.jpg",
    name: "Michael Ng",
    position: "Digital Marketing Manager",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674494747/website/homepage/applicant-3_qkdz0t.jpg",
    name: "Feyra Rayfa",
    position: "Sales Marketing Executive",
  },
];
</script>
