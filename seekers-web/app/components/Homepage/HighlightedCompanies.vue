<template>
  <section id="highlighted-companies" class="mb-32 mx-4">
    <div class="carousel md:max-w-7xl md:mx-auto justify-center">
      <div class="carousel-item md:gap-16 gap-8">
        <div v-for="job in uniqueJobs" :key="job.company.id">
          <img
            :src="job.company?.logo_url"
            alt=""
            class="object-contain h-20 w-28"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
type props = {
  highlightedJobs?: { data: Job[] } | null;
  latestJobs?: { data: Job[] } | null;
  latestTechJobs?: { data: Job[] } | null;
  highlightedJobsPending: boolean;
  latestJobsPending: boolean;
  latestTechJobsPending: boolean;
};
const props = defineProps<props>();

const uniqueJobs: any = computed(() => {
  const uniqueCompanies = new Set();
  const arrays = [
    props.highlightedJobs?.data,
    props.latestJobs?.data,
    props.latestTechJobs?.data,
  ];

  const result = arrays.reduce((acc, array) => {
    array?.forEach((job) => {
      const company = job.company.id;
      if (!uniqueCompanies.has(company)) {
        acc?.push(job);
        uniqueCompanies.add(company);
      }
    });
    return acc;
  }, []);

  let filteredJobs: any = [];
  result?.forEach((job: any) => {
    if (job?.company?.name.toLowerCase().includes("confidential")) return;
    filteredJobs.push(job);
  });
  return filteredJobs;
});
</script>
