<template>
  <section id="popular-categories" class="bg-success bg-opacity-50 py-24">
    <h2 class="text-center font-semibold mb-8">Popular Job Categories</h2>

    <div
      class="grid md:grid-cols-3 md:mx-auto md:max-w-7xl gap-4 mx-4"
      v-if="!pending"
    >
      <nuxt-link
        :to="'/jobs?category=' + urlSafeString(category.name)"
        class="grid grid-rows-1 grid-cols-[2fr_10fr] border hover:drop-shadow transition-all cursor-pointer rounded-md bg-white items-center p-4 gap-4 group hover:text-primary"
        v-for="(category, index) in categoryNameAndImages.slice(1)"
      >
        <img :src="category.img" class="w-full h-full object-contain" />

        <div>
          <h3 class="font-semibold text-base">{{ category.name }}</h3>
          <p class="text-sm group-hover:text-black">
            ({{ popularCategories[index]?.open_jobs }} open positions)
          </p>
        </div>
      </nuxt-link>
    </div>
    <DevOnly>
      <!-- <pre>{{ popularCategories }}</pre> -->
    </DevOnly>
  </section>
</template>

<script setup lang="ts">
const categoryNameAndImages = useHelper.jobCategories;
const baseUrl = useRuntimeConfig().public.baseUrl;

const { data: popularCategories, pending } = useFetch<any>(
  baseUrl + `/popular-job-categories`
);

function urlSafeString(str: string) {
  return str.replace(/&/g, "%26");
}
</script>
