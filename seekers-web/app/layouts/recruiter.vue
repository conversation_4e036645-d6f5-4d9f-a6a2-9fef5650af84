<template>
  <div>
    <NuxtLoadingIndicator />
    <LayoutsRecruiterNavbar />
    <!-- <LayoutsMainNavbar /> -->
    <LayoutsRecruiterSidemenu v-if="isRecruiterDashboard">
      <slot />
    </LayoutsRecruiterSidemenu>
    <div v-else>
      <slot />
    </div>
    <LayoutsFooter />
  </div>
</template>

<script setup>
const route = useRoute();

//return true if route full path includes 'recruiter/dashboard'
const isRecruiterDashboard = computed(() => {
  return route.fullPath.includes("recruiter/dashboard");
});
</script>
