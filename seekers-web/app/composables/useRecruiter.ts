export default function useRecruiter() {
  const baseUrl = useRuntimeConfig().public.baseUrl;
  const token = useState<string | null>("recruiter_token", () =>
    useCookie("recruiter_token")
  );

  const authedHeader = computed(() => ({
    Authorization: `Bearer ${token.value}`,
  }));
  // METHODS

  const register = async (form: RecruiterRegistrationForm) => {
    const formData = useHelper.JSONToFormData(form);
    return await $fetch(baseUrl + "/register", {
      method: "POST",
      body: formData,
    });
  };

  const login = async (email: string, password: string) => {
    const { data, error } = await $fetch<any>(baseUrl + "/login", {
      method: "POST",
      body: JSON.stringify({ email, password }),
    });
    if (data) {
      token.value = data.value.data.token;
      await navigateTo("/recruiter/dashboard");
    } else {
      return error;
    }
  };

  const resetPassword = async (recruiterEmail: string) => {
    const formData = useHelper.JSONToFormData({ email: recruiterEmail });

    return await $fetch(baseUrl + "/reset_password", {
      body: formData,
      headers: authedHeader.value,
      method: "POST",
    });
  };

  const updatePassword = async (
    newPassword: string,
    newConfirmPassword: string
  ) => {
    const formData = useHelper.JSONToFormData({
      password: newPassword,
      confirm_password: newConfirmPassword,
    });
    await $fetch(baseUrl + `/change_password`, {
      method: "POST",
      body: formData,
      headers: authedHeader.value,
    });
  };

  const logout = () => {
    token.value = null;
    const cookie = useCookie("recruiter_token");
    cookie.value = null;
    navigateTo("/");
    useHelper.successToast("Safely logged out");
  };

  const getProfile = () => {
    return useFetch<any>(baseUrl + `/profile`, {
      key: "recruiter_profile",
      headers: { Authorization: `Bearer ${token.value}` },
    });
  };

  const updateLastSeen = () => {
    if (!token.value) return;
    $fetch<any>(baseUrl + `/ping/recruiter`, {
      method: "POST",
      headers: authedHeader.value,
    });
  };

  // JOBS
  const recommendedJobs = () => {
    return useFetch<any>(baseUrl + `/user_jobs/recommended?is_recruiter=true`, {
      key: "recruiter_recommended_jobs",
      headers: authedHeader.value,
    });
  };

  // PROFILE

  const saveJob = async (jobId: number) => {
    if (!token.value) {
      alert("Please login to save jobs");
      return;
    }
    await $fetch(baseUrl + `/user_jobs/store_favourite/${jobId}`, {
      headers: authedHeader.value,
      method: "POST",
    });
  };

  const unsaveJob = async (jobId: number) => {
    await $fetch(baseUrl + `/user_jobs/remove_favourite/${jobId}`, {
      headers: { Authorization: `Bearer ${token.value}` },
      method: "POST",
    });
  };

  const updateProfile = async (formData: RecruiterProfileForm) =>
    $fetch(baseUrl + "/update_profile", {
      method: "POST",
      body: useHelper.JSONToFormData(formData),
      headers: authedHeader.value,
    });

  // CANDIDATES

  const getCandidates = () =>
    useFetch<any>(baseUrl + "/manage_candidates?limit=200", {
      headers: authedHeader.value,
      deep: false,
    });

  const getCandidateById = (id: number | string) =>
    useFetch<any>(baseUrl + `/manage_candidates/${id}`, {
      headers: authedHeader.value,
    });

  const addCandidate = async (form: RecruiterCandidateForm) => {
    const formData = useHelper.JSONToFormData(form);
    return $fetch<any>(baseUrl + `/manage_candidates/register`, {
      method: "POST",
      body: formData,
      headers: authedHeader.value,
    });
  };

  const updateCandidate = async (form: RecruiterCandidateForm) => {
    const formData = useHelper.JSONToFormData(form);
    return $fetch<any>(baseUrl + `/manage_candidates/${form.id}/update`, {
      method: "POST",
      body: formData,
      headers: authedHeader.value,
    });
  };

  const removeCandidate = async (candidateId: number) =>
    $fetch(baseUrl + `/manage_candidates/${candidateId}/remove`, {
      method: "POST",
      headers: authedHeader.value,
    });

  return {
    addCandidate,
    getCandidateById,
    getCandidates,
    getProfile,
    login,
    logout,
    recommendedJobs,
    register,
    removeCandidate,
    resetPassword,
    saveJob,
    token,
    unsaveJob,
    updateCandidate,
    updateLastSeen,
    updatePassword,
    updateProfile,
  };
}
