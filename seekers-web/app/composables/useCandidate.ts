export default function () {
  const baseUrl = useRuntimeConfig().public.baseUrl;

  // INITIALIZED STATES
  const token = useCookie("candidate_token");
  const profile = useState<any>("candidate_profile", () => null);
  const sidebarExpanded = useCookie("sidebarExpanded");

  // COMPUTED
  const authedHeader = computed(() => {
    return { Authorization: `Bearer ${token?.value}` };
  });

  // QUERIES
  const register = (data: any) => {
    const formData = useHelper.JSONToFormData(data);
    return $fetch<any>(baseUrl + "/auth_candidate/register", {
      method: "POST",
      body: formData,
    });
  };

  const resetPassword = async (candidateEmail: string) => {
    const formData = useHelper.JSONToFormData({ email: candidateEmail });

    return await $fetch(baseUrl + "/auth_candidate/reset_password", {
      body: formData,
      method: "POST",
    });
  };

  const getProfile = () => {
    return useFetch<any>(`/api/candidate/profile?token=${token.value}`, {
      key: "candidate_profile",
      deep: false,
      method: "POST",
    });
  };

  const syncProfile = async () => {
    const res = await getProfile();
    profile.value = res.data;
    return profile;
  };

  const master_login = async (email: string, password: string) => {
    if (!email || !password) {
      useHelper.errorToast("Please fill up properly");
      return;
    }

    const formData = new FormData();
    formData.append("email", email);
    formData.append("password", password);

    const res = await (
      await fetch(baseUrl + "/auth_candidate/master_login", {
        method: "POST",
        body: formData,
      })
    ).json();

    if (res.success) {
      token.value = res.data.token;
      sidebarExpanded.value = "yes";
      await navigateTo("/candidate/dashboard/profile");
    } else {
      useHelper.errorToast("Not getting in");
    }
  };

  const login = async (email: string, password: string) => {
    if (!email || !password) {
      useHelper.errorToast("Please fill in all fields");
      return;
    }

    const formData = new FormData();
    formData.append("email", email);
    formData.append("password", password);

    const res = await (
      await fetch(baseUrl + "/auth_candidate/login", {
        method: "POST",
        body: formData,
      })
    ).json();

    if (res.success) {
      token.value = res.data.token;
      sidebarExpanded.value = "yes";
      await navigateTo(
        useHelper.leftOffUrl().value || "/candidate/dashboard/profile"
      );
    } else {
      useHelper.errorToast(
        "Please make sure email/password you entered is correct."
      );
    }
  };

  const logout = () => {
    token.value = null;
    const cookie = useCookie("candidate_token");
    cookie.value = null;
    navigateTo("/");
  };

  const downloadFile = async (id: number) =>
    await fetch(baseUrl + `/candidate_portfolio/download/${id}`, {
      headers: { Authorization: `Bearer ${token.value}` },
    });

  const profileCompletion = computed(() => {
    let total = 10;
    if (profile.value) {
      if (profile.value.profile_photo) {
        total += 10;
      }
      if (profile.value.cv) {
        total += 10;
      }
      if (profile.value.about_me) {
        total += 10;
      }
      if (profile.value.candidate_education?.length > 0) {
        total += 15;
      }
      if (profile.value.work_experiences?.length > 0) {
        total += 15;
      }
      if (profile.value.candidate_portfolio?.length > 0) {
        total += 15;
      }
      if (profile.value.candidate_certification?.length > 0) {
        total += 15;
      }
      if (
        profile.value.facebook ||
        profile.value.linkedin ||
        profile.value.twitter ||
        profile.value.instagram ||
        profile.value.github ||
        profile.value.website
      ) {
        total += 10;
      }
    }
    return total;
  });

  const updateLastSeen = () => {
    if (!token.value) return;
    $fetch<any>(baseUrl + `/ping/candidate`, {
      method: "POST",
      headers: authedHeader.value,
    });
  };

  // MUTATIONS

  const updateProfile = async (data: any) => {
    const formData = useHelper.JSONToFormData(data);
    const updatedData = await $fetch<unknown>(
      baseUrl + `/auth_candidate/update_profile`,
      {
        method: "POST",
        headers: authedHeader.value,
        body: formData,
      }
    );
    profile.value = updatedData.data;
  };

  const updatePassword = (newPassword: string, newConfirmPassword: string) => {
    const formData = useHelper.JSONToFormData({
      password: newPassword,
      confirm_password: newConfirmPassword,
    });
    return $fetch(baseUrl + `/auth_candidate/change_password`, {
      method: "POST",
      headers: authedHeader.value,
      body: formData,
    });
  };

  const createPortfolio = (data: FormData) => {
    return $fetch(baseUrl + `/candidate_portfolio/create`, {
      method: "POST",
      headers: authedHeader.value,
      body: data,
    });
  };

  const deletePortfolio = async (id: number) =>
    $fetch(baseUrl + `/candidate_portfolio/delete/${id}`, {
      headers: authedHeader.value,
      method: "POST",
    });

  const createEducation = (data: any) => {
    const update = useHelper.JSONToFormData(data);
    return $fetch(baseUrl + `/candidate_education/create`, {
      headers: authedHeader.value,
      method: "POST",
      body: update,
    });
  };

  const updateEducation = (data: any) => {
    const update = useHelper.JSONToFormData(data);
    return $fetch(baseUrl + `/candidate_education/update`, {
      headers: authedHeader.value,
      method: "POST",
      body: update,
    });
  };

  const deleteEducation = async (id: number) =>
    $fetch(baseUrl + `/candidate_education/delete/${id}`, {
      headers: authedHeader.value,
      method: "POST",
    });

  const createLicenseCertification = (data: any) => {
    const update = useHelper.JSONToFormData(data);
    return $fetch(baseUrl + `/candidate_certification/create`, {
      method: "POST",
      headers: authedHeader.value,
      body: update,
    });
  };

  const updateLicenseCertification = (data: any) => {
    const update = useHelper.JSONToFormData(data);
    return $fetch(baseUrl + `/candidate_certification/update`, {
      method: "POST",
      headers: authedHeader.value,
      body: update,
    });
  };

  const deleteLicenseCertification = (id: number) =>
    $fetch(baseUrl + `/candidate_certification/delete/${id}`, {
      headers: authedHeader.value,
      method: "POST",
    });

  const createWorkExperience = (data: any) => {
    const update = useHelper.JSONToFormData(data);
    return $fetch(baseUrl + `/candidate_work/create`, {
      method: "POST",
      headers: authedHeader.value,
      body: update,
    });
  };

  const updateWorkExperience = (data: any) => {
    const update = useHelper.JSONToFormData(data);
    return $fetch(baseUrl + `/candidate_work/update`, {
      headers: authedHeader.value,
      method: "POST",
      body: update,
    });
  };

  const deleteWorkExperience = (id: number) =>
    $fetch(baseUrl + `/candidate_work/delete/${id}`, {
      method: "POST",
    });

  const saveJob = (jobId: number) => {
    if (!token.value) {
      alert("Please Login to save jobs");
      return;
    }
    return $fetch(baseUrl + `/candidate_jobs/favourites/${jobId}`, {
      headers: authedHeader.value,
      method: "POST",
    });
  };

  const unsaveJob = (jobId: number) =>
    $fetch(baseUrl + `/candidate_jobs/favourites/${jobId}/remove`, {
      headers: authedHeader.value,
      method: "POST",
    });

  const followCompany = (companyId: number) =>
    $fetch(baseUrl + "/candidate_following/follow/" + companyId, {
      headers: authedHeader.value,
      method: "POST",
    });

  const unfollowCompany = (companyId: number) =>
    $fetch(baseUrl + "/candidate_following/unfollow/" + companyId, {
      headers: authedHeader.value,
      method: "POST",
    });

  const submitJobApplication = (form: CandidateJobApplicationForm) =>
    useFetch(baseUrl + "/candidate_jobs/apply", {
      headers: authedHeader.value,
      method: "POST",
      body: useHelper.JSONToFormData(form),
    });

  return {
    createEducation,
    createLicenseCertification,
    createPortfolio,
    createWorkExperience,
    deleteEducation,
    deleteLicenseCertification,
    deletePortfolio,
    deleteWorkExperience,
    downloadFile,
    followCompany,
    getProfile,
    master_login,
    login,
    logout,
    profile,
    profileCompletion,
    register,
    resetPassword,
    saveJob,
    sidebarExpanded,
    submitJobApplication,
    syncProfile,
    token,
    unfollowCompany,
    unsaveJob,
    updateEducation,
    updateLastSeen,
    updateLicenseCertification,
    updatePassword,
    updateProfile,
    updateWorkExperience,
  };
}
