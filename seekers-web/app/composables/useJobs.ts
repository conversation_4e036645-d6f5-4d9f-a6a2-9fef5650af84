export default function () {
  const baseUrl = useRuntimeConfig().public.baseUrl;

  //
  // INITIALIZED STATES
  //

  const searchFilter = () =>
    useState<JobSearchFilter>("searchFilter", () => ({
      levels: "",
      includeContract: true,
      includeFullTime: true,
      includeInternship: true,
      jobTypes: "",
      keywords: "",
      limit: 12,
      page: 1,
      postedDate: "",
      salary_min: 0,
      salary_max: 15000,
      sort: "",
      location: "",
      roles: "",
    }));

  ////
  ////  HELPER FUNCTIONS
  ////

  // build FilterObject with all the filters values
  const createJobQueryString = (): string => {
    let queryStr = "";
    const filters: any = searchFilter().value;

    // for each filter that is not null or undefined or empty string, add it to the query string

    Object.keys(filters).forEach((key) => {
      if (
        filters[key] !== null &&
        filters[key] !== undefined &&
        filters[key] !== ""
      ) {
        queryStr += `${key}=${filters[key]}&`;
      }
    });
    queryStr = queryStr.slice(0, -1);

    return queryStr;
  };

  //
  //  FETCH
  //

  const getBySlug = (slug: string) => useFetch<Job>(`/api/jobs/by-slug/${slug}`);

  const recruiterLatestJobs = (
    limit?: number,
    page?: number,
    sort?: string,
    isRecruiter?: string
  ) =>
    useFetch<any>(
      `/api/jobs/latest-tech?limit=${limit || 4}&page=${page || 1}&sort=${sort || "desc"
      }&isRecruiter=${isRecruiter || "true"}`,
    );

  const searchWithFilter = async () => {
    const querystr = createJobQueryString();
    const data = await useFetch<any>(
      baseUrl + `/jobs?${querystr}&package=all&isRecruiter=true`,
      {
        key: querystr,
      }
    );
    return data;
  };

  const recruiterSearchWithFilter = async () => {
    const querystr = createJobQueryString();
    const data = await useFetch<any>(
      baseUrl + `/jobs?${querystr}&package=all&isRecruiter=true`,
      {
        key: querystr,
      }
    );
    return data;
  };

  const getRelatedJobs = (jobId: number) =>
    useFetch<any>(baseUrl + `/jobs/related?job_id=${jobId}`, { lazy: true });

  return {
    getBySlug,
    getRelatedJobs,
    searchFilter,
    createJobQueryString,
    recruiterLatestJobs,
    recruiterSearchWithFilter,
    searchWithFilter,
  };
}
