export default function () {
  const baseUrl = useRuntimeConfig().public.baseUrl;

  const bySlug = (slug: string) =>
    useFetch<any>(baseUrl + `/companies/slug/${slug}`, { key: slug });

  const searchCompanyFilter = () =>
    useState("searchCompanyFilter", () => {
      return {
        keywords: "",
        limit: 12,
        page: 1,
        sort: "",
        location: "",
        roles: "",
        has_open_jobs: "",
        is_highlight: "",
      };
    });

  // build FilterObject with all the filters values
  const createCompanyQueryString = (): string => {
    let queryStr = "";
    const filters: any = searchCompanyFilter().value;

    // for each filter that is not null or undefined or empty string, add it to the query string
    Object.keys(filters).forEach((key) => {
      if (
        filters[key] !== null &&
        filters[key] !== undefined &&
        filters[key] !== ""
      ) {
        queryStr += `${key}=${filters[key]}&`;
      }
    });
    queryStr = queryStr.slice(0, -1);

    return queryStr;
  };

  const searchWithFilter = async () => {
    const querystr = createCompanyQueryString();
    const data = await useFetch<any>(baseUrl + `/companies?${querystr}`, {
      key: querystr,
      deep: false
    });
    return data;
  };

  return {
    bySlug,
    searchCompanyFilter,
    searchWithFilter,
  };
}
