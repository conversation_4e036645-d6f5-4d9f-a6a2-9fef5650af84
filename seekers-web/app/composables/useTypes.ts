type EducationItem = {
  id?: number;
  qualification?: string;
  title?: string;
  institute?: string;
  description?: string;
  start?: string;
  end?: string;
  candidate_id?: number;
  created_at?: string;
  updated_at?: string;
};

type LicenseCertificationItem = {
  id?: number;
  name?: string;
  description?: string;
  start?: string;
  end?: string;
  candidate_id?: number;
  created_at?: string;
  updated_at?: string;
};

type FilterOject = {
  category?: string;
  datePosted?: string;
  experienceLevels?: string;
  includeInternship?: boolean;
  keywords?: string;
  job_types?: string;
  limit?: number;
  page?: number;
  posted_date?: string;
  salary_max?: number;
  salary_min?: number;
  searchState?: string;
  sort?: string;
  type?: string;
};

type WorkExperienceItem = {
  id?: number;
  candidate_id?: number;
  company_name?: string;
  role?: string;
  start: string;
  end: string;
  responsibilities?: string;
  created_at?: string;
  updated_at?: string;
};

type Company = {
  id: number;
  name: string;
  slug: string;
  reg_no: string;
  building: string;
  address: string;
  postal_code: number;
  city: string;
  state: string;
  location: string;
  long: string;
  lat: string;
  industry_id: number;
  size: string;
  overview: string;
  highlight?: any;
  logo: string;
  is_generated_logo: number;
  background_image: string;
  pic_email: string;
  pic_phone: string;
  allowances: string;
  benefits: string;
  annual_leave: string;
  bonus: string;
  culture_text?: any;
  culture_video_url?: any;
  culture_photo?: any;
  registration_data: string;
  tnc_agree: number;
  is_highlight: number;
  is_top_employer: number;
  is_main: number;
  score: string;
  is_srs_premium: number;
  created_at: string;
  updated_at: string;
  url: string;
  video_url?: any;
  url_path: string;
  corpcare_id: number;
  deleted_at?: any;
  remark_dev: string;
  logo_url: string;
  logo_url_xs: string;
  logo_url_medium: string;
  logo_url_seo: string;
  full_address: string;
  street_address: string;
  background_image_url: string;
  is_profile_completed: boolean;
  industry: Industry;
  open_jobs_count: number;
};
type Industry = {
  id: number;
  parent_id: number;
  name: string;
  color?: any;
  icon?: any;
  image_path?: any;
  slug: string;
  priority?: any;
  sort_order: number;
  is_parent: boolean;
  seo_title?: any;
  seo_description?: any;
  seo_image?: any;
  image?: any;
  icon_url?: any;
  parent: Parent;
};
type Parent = {
  id: number;
  parent_id?: any;
  name: string;
  color: string;
  icon: string;
  image_path: string;
  slug: string;
  priority?: any;
  sort_order: number;
  is_parent: boolean;
  seo_title: string;
  seo_description: string;
  seo_image?: any;
  image: string;
  icon_url: string;
  parent?: any;
};

type Job = {
  id: number;
  description?: string;
  req_must_have_arr: any[];
  company_id: number;
  package: string;
  feature: string;
  expiry_date?: any;
  industry_id: number;
  is_remote: number;
  role_id: number;
  title: string;
  slug: string;
  level: string;
  is_highlight: number;
  salary_max: number;
  salary_min: number;
  reward_max: number;
  reward_min: number;
  city?: any;
  long?: any;
  lat?: any;
  state?: string;
  status: string;
  sent_to_client_reward: number;
  shortlist_reward: number;
  created_at: string;
  updated_at: string;
  hide_from_all_recruiter: number;
  show_to_recruiter_group: string;
  no_applied: number;
  no_reviewed: number;
  no_sent_to_client: number;
  no_rejected: number;
  url: string;
  created_at_relative: string;
  images?: any;
  tags_data?: any[] | null;
  tags_data_string: string;
  salary_range: string;
  responsibility_arr: any[];
  parent_industry_slug: string;
  parent_role_slug: string;
  street_address: string;
  industry: Industry;
  company: Company;
  job_visibility?: any[] | null;
  role: Role;
  job_requirement?: any;
  job_condition?: any;
  job_screening_questions?: JobScreeningQuestion[] | null;
  error?: string;
  success?: boolean;
};

type Role = {
  id: number;
  parent_id: number;
  name: string;
  slug: string;
  image?: any;
  is_parent: number;
  created_at?: any;
  updated_at: string;
  icon?: any;
  color?: any;
  seo_title?: any;
  seo_description?: any;
  seo_image?: any;
  is_digital: number;
  icon_url?: any;
  parent: Parent;
};

interface SpecializationOption {
  label: string;
  value: string | number;
}

type RecruiterCandidateForm = {
  user_id: number;
  birth_year: number;
  cv: File | null;
  email: string;
  gender: string;
  got_work_permit: "0" | "1" | 1 | 0;
  ic: string;
  id?: number;
  is_local: "0" | "1" | 1 | 0;
  languages: any;
  location: string;
  mobile: string;
  name: string;
  own_transport: "0" | "1" | 1 | 0;
  portfolio: string;
  race: string;
  role_id: number;
  tags?: string;
  willing_to_travel: "0" | "1" | 1 | 0;
  expected_salary?: number;
  current_salary: number;
  notice_period: string;
};

type RecruiterProfileForm = {
  bank_name: string;
  bank_no: string;
  designation: string;
  facebook: string;
  ic: string;
  instagram: string;
  languages: string;
  linkedin: string;
  location: string;
  mobile: string;
  name: string;
  specialization: string;
  twitter: string;
};

type CandidateJobApplicationForm = {
  birth_year: number;
  cv?: File;
  email?: string;
  gender: string;
  is_local: number;
  job_id?: number | null;
  job_screening_answers?: string;
  location: string;
  mobile: string;
  name: string;
  notice_period: string;
  portfolio?: string;
  salary_current: number | null;
  salary_expected: number | null;
  user_id?: number | null;
};

type JobScreeningQuestion = {
  id: number;
  question: string;
  type: "text" | "education_level";
  answer?: string;
};

type RecruiterRegistrationForm = {
  name: string;
  email: string;
  mobile: string;
  passowrd: string;
  committed_hours_daily?: string | null;
  company_name?: string | null;
  designation?: string | null;
  experience_years?: number | null;
  occupation?: string | null;
  sourcing_methods?: string | null;
  specializations?: string | null;
  is_technical_recruiter?: boolean;
  is_working_fulltime?: boolean | null;
};

type CandidateLanguageForm = {
  selected: string | boolean;
  language: string;
  level: string;
}[];

type Transaction = {
  id: number;
  type: "in" | "out";
  status: "completed" | "requested" | "rejected";
  amount: number;
  note: string | null;
  bank_name?: string | null;
  bank_no?: string | null;
  created_at: string;
  updated_at: string;
  transaction_ref?: string | null;
  transaction_date?: string | null;
  sentence: string | null;
  balance: number;
};

interface JobSearchFilter {
  /**
   * A comma-seperated string of RoleIDs. eg, "1001,1818,1819"
   */
  roles?: string;
  /**
   * A comma-seperated string of level labels.
   * eg, "entry-level,experienced,manager"
   */
  levels?: string;
  includeContract?: boolean;
  includeFullTime?: boolean;
  includeInternship?: boolean;
  keywords?: string;
  limit?: number;
  page: number;
  posted_date?: string;
  salary_min?: number;
  salary_max?: number;
  sort?: string;
  location?: string;
  /**
   * A comma-seperated string of job types.
   * eg, "Permanent,Contract,Internship"
   */
  job_types?: string;
}

type BasicInfoForm = {
  name: string;
  email: string;
  password_group: { password: string; password_confirm: string };
  mobile: string;
  birth_year: string;
  is_local: number;
  state: string;
  gender: string;
};

type EducationWorkForm = {
  education_qualification: string;
  education_title: string;
  education_institute: string;
  education_period: {
    start: string;
    end: string;
  };
  work_role: string;
  work_company_name: string;
  work_responsibilities: string;
  working_period: {
    start: string;
    end: string;
  };
  cv: { file: File; name: string }[];
  open_to_work: number;
  education_start: string;
  education_end: string;
  work_start: string;
  work_end: string;
};
