@import "animate.css/animate";
@import "transition-style";
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --animate-delay: 0.5s;
}

a {
  cursor: pointer;
}

html {
  font-family: "Montserrat", sans-serif;
}
h1 {
  font-size: 2rem;
}
h2 {
  font-size: 1.75rem;
  line-height: 1.25;
}
h3 {
  font-size: 1.25rem;
}

.btn,
.select {
  font-size: 13px;
  font-weight: 400;
}

.avatar-contain {
  object-fit: contain;
}

.outline-never {
  outline: none;
}
.outline-never:focus {
  outline: none;
}
.outline-never:active {
  outline: none;
}
.outline-never:hover {
  outline: none;
}
.outline-never:focus-within {
  outline: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

#om-campaign-8 > div.powered-by > a {
  display: none !important;
}