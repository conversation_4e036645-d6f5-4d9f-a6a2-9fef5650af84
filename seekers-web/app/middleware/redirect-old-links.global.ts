export default defineNuxtRouteMiddleware((to, from) => {
  // redirect 'https://seekers.my/jobfinder/jobs/*' to 'https://seekers.my/jobs/*'
  if (to.path.startsWith("/jobfinder/jobs")) {
    const newPath = to.path.replace("/jobfinder/jobs", "/jobs");
    return navigateTo(newPath);
  }

  // redirect 'https://seekers.my/jobfinder/industry/industry-slug/company-slug/job-slug' to 'https://seekers.my/jobs/job-slug'
  if (to.path.startsWith("/jobfinder/industry")) {
    const lastPart = to.path.split('/').pop();
    const newPath = '/jobs/' + lastPart;
    return navigateTo(newPath);
  }
});
