export default defineNuxtRouteMiddleware((to, from) => {
  const pathsWithLangPrefix = [
    // routes should be just like in browser url eg. /global-hr
    "/global-hr",
    "/global-hr/thankyou",
    // "/jobs"
  ];
  // If Browser Language is Japanese, redirect to /ja/**/*
  if (process.client) {
    const browserLang = navigator.language;
    if (browserLang.includes("ja")) {
      if (to.path.startsWith("ja")) return;
      if (
        from.path !== "/ja/global-hr" &&
        pathsWithLangPrefix.includes(to.path)
      ) {
        const newPath = "/ja" + to.fullPath;
        return navigateTo(newPath, { external: true });
      }
    }
    // if (browserLang.includes("en")) {
    //   //Function : If Browser Language is English, redirect /ja to non-prefixed path
    //   if (to.fullPath.startsWith("/ja")) {
    //     // Create new non-prefixed path
    //     const newPath = to.fullPath.slice(3);

    //     // If new path is in pathsWithLangPrefix, do not redirect
    //     if (pathsWithLangPrefix.includes(newPath)) return;

    //     // Redirect to new path
    //     return navigateTo(newPath, { external: true });
    //   }
    // }
  }
});
