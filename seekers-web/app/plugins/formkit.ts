import { plugin, defaultConfig } from "@formkit/vue";

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.use(
    plugin,
    defaultConfig({
      plugins: [],
      config: {
        locale: "my",
        classes: {
          label: "text-sm",
          input:
            "mt-1 p-4 px-4 w-full rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm",
          help: "text-xs text-gray-500 mt-1",
          messages: "list-none p-0 mt-1 mb-0",
          message: "text-red-500 mb-1 text-xs lg:text-left",
        },
      },
    })
  );
});
