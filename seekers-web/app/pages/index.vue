<template>
  <div id="homepage" class="min-h-screen">
    <HomepageHero />

    <HomepageFeaturedJobs
      :highlightedJobs="highlightedJobs"
      :highlightedJobsPending="highlightedJobsPending"
      :latestJobs="latestJobs"
      :latestJobsPending="latestJobsPending"
      :latestTechJobs="latestTechJobs"
      :latestTechJobsPending="latestTechjobsPending"
    />

    <HomepageLeverageTopTalent />

    <HomepageTrustedPartner />

    <!-- <HomepageFindDreamJob />

    <HomepageSuccessfulCandidates />

    <HomepagePopularJobCategories />

    <HomepageBottomBanner /> -->
  </div>
</template>

<script setup lang="ts">
const { data: highlightedJobs, pending: highlightedJobsPending } = useFetch<{
  data: Job[];
}>("/api/jobs/highlighted?limit=6", { key: "highlighted-jobs" });
const { data: latestJobs, pending: latestJobsPending } = useFetch<{
  data: Job[];
}>("/api/jobs/latest", { key: "latest-jobs" });
const { data: latestTechJobs, pending: latestTechjobsPending } = useFetch<{
  data: Job[];
}>("/api/jobs/latest-tech", { key: "latest-tech" });
</script>
