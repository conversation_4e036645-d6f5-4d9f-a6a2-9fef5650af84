<template>
  <div class="w-full flex flex-col gap-8 items-center justify-center h-[70vh]">
    <h1 style="letter-spacing: 0.5em">WELCOME</h1>
    <p>Logging You In</p>
    <img src="@/static/ring-spinner.png" class="animate-spin h-12 w-12" />
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const candidateToken = useCandidate().token;
const leftOffUrl = useHelper.leftOffUrl();

onMounted(() => {
  if (!route.query.token) navigateTo("/candidate/login");
  candidateToken.value = route.query.token as string;
  if (leftOffUrl.value) navigateTo(leftOffUrl.value);
  else navigateTo("/candidate/dashboard");
});
</script>
