<template>
  <div id="sci-code-page" class="p-6 lg:p-12">
    <div class="max-w-2xl mx-auto border flex flex-col gap-8 px-4">
      <h1 class="p-4 text-lg text-center border-b">SCOUT INVITATION</h1>
      <div>
        <p>
          You have received an invitation from
          <b>{{ data.job.company.name }}</b> to apply for their job opening on
          Seekers. Please confirm your application before
          <b>{{ formatDate(data.deadline) }}</b>
        </p>
        <p class="p-4 text-xs bg-warning mt-6">
          Note: A scout invitation is an invitation directly by the employer to
          apply for a job opening on Seekers. While this is not a final job
          offer, but you have higher chance of getting hired because employers
          like your profile
        </p>
      </div>
      <h2 class="bg-gray-100 p-4 text-sm font-semibold text-center">
        JOB DETAILS
      </h2>
      <div class="grid grid-cols-[1fr_3fr] gap-2 px-4">
        <p>Job Title</p>
        <p>{{ data.job.title }}</p>
        <p>Company</p>
        <p>{{ data.job.company.name }}</p>
        <p>Salary Range</p>
        <p>RM {{ data.job.salary_min }} - RM {{ data.job.salary_max }}</p>
        <p>Job URL</p>
        <a
          :href="'/jobs/' + data.job.slug"
          target="_blank"
          class="text-info-content"
          >Click to open in new Tab</a
        >
      </div>
      <h2 class="bg-gray-100 p-4 text-sm font-semibold text-center">
        INVITATION MESSAGE
      </h2>
      <div v-html="data.actual_message"></div>
      <h2 class="bg-gray-100 p-4 text-sm font-semibold text-center">
        APPLICANT DETAILS
      </h2>
      <div class="grid grid-cols-[1fr_3fr] gap-2 px-4">
        <p>Applicant</p>
        <p>{{ data.candidate.name }}</p>
        <p>Email</p>
        <p>{{ data.candidate.email }}</p>
      </div>
      <button
        v-if="!responded && data.status == 'ongoing'"
        class="btn btn-success mt-12"
        @click="respondToInvitation('yes')"
      >
        Yes. I'm Interested
      </button>
      <button
        v-if="!responded && data.status == 'ongoing'"
        @click="respondToInvitation('no')"
        class="btn btn-error -mt-4 mb-8"
      >
        Sorry, I'm not interested
      </button>
      <button v-else @click="" disabled class="btn mt-12 mb-8">
        You have already responded
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import Swal from "sweetalert2";
const baseUrl = useRuntimeConfig().public.baseUrl;
const route = useRoute();
const responded = ref(false);

const { data } = await useFetch<any>(
  baseUrl + `/srs/cv/scout/details/` + route.params.code
);

const form = ref<any>({
  code: route.params.code,
  status: null,
  reject_reason: "",
});

const formatDate = (date: string) => {
  return dayjs(date).format("D MMMM YYYY");
};

function respondToInvitation(answer: string) {
  if (answer == "yes") {
    Swal.fire({
      title: "Apply to this job position?",
      text: "Are you sure to apply to this position?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Confirm",
    }).then((result) => {
      if (result.value) {
        submitResponse(answer);
      }
    });
  } else {
    Swal.fire({
      title: "Reject this job invitation?",
      text: "Please state your reason",
      icon: "warning",
      input: "select",
      inputOptions: {
        "Not the right time to change my job":
          "Not the right time to change my job",
        "Salary is not attractive enough": "Salary is not attractive enough",
        "Location is too far": "Location is too far",
        "Job is not attractive enough": "Job is not attractive enough",
        "My skills/experience does not meet the requirement":
          "My skills/experience does not meet the requirement",
      },
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Reject This Invitation",
    }).then((result) => {
      form.value.reject_reason = result.value;
      submitResponse(answer);
    });
  }
}

async function submitResponse(answer: string) {
  form.value.status = answer;
  let params = form.value;

  await fetch(baseUrl + `/srs/cv/scout/respond/` + route.params.code, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(params),
  });
  return;
}
</script>
