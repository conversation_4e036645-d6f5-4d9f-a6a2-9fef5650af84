<template>
  <div id="ivn-code-page" class="lg:p-12 p-4">
    <div class="max-w-2xl mx-auto flex flex-col gap-6">
      <p class="rounded-lg bg-info p-6 text-sm">
        Congratulations! You are invited for an interview for your job
        application on Seekers.<br />Please confirm your acceptance or ask to
        reschedule
      </p>
      <h1 class="bg-black text-lg text-white p-2 text-center">
        Interview Information
      </h1>
      <div class="flex flex-col gap-6">
        <p class="bg-accent text-white p-2 text-center text-sm">
          Application Details
        </p>
        <div class="grid md:grid-cols-[1fr_3fr] gap-4">
          <p class="font-semibold">Applicant</p>
          <p>{{ data.applicant.candidate.name }}</p>
          <p class="font-semibold">Job Title</p>
          <p>{{ data.applicant.job.title }}</p>
          <p class="font-semibold">Company</p>
          <p>{{ data.applicant.job.company.name }}</p>
        </div>

        <p class="bg-accent text-white p-2 text-center text-sm">
          Interview Details
        </p>
        <div class="grid md:grid-cols-[1fr_3fr] gap-4">
          <p class="font-semibold">Location</p>
          <p>{{ data.interview_location }}</p>
          <p class="font-semibold">Date & Time</p>
          <p>{{ data.interview_datetime }}</p>
          <p class="font-semibold">Remarks</p>
          <p class="whitespace-pre-wrap">{{ data.interview_remarks }}</p>
        </div>

        <p class="bg-accent text-white p-2 text-center text-sm">
          Person In Charge
        </p>
        <div class="grid md:grid-cols-[1fr_3fr] gap-4">
          <p class="font-semibold">Name</p>
          <p>{{ data.interview_pic }}</p>
          <p class="font-semibold">Position</p>
          <p>{{ data.interview_pic_position }}</p>
          <p class="font-semibold">Phone No.</p>
          <p class="whitespace-pre-line">{{ data.interview_pic_tel }}</p>
        </div>
      </div>
      <div
        class="grid grid-cols-2 gap-2 mt-12"
        v-if="!data.interview_accept && !data.interview_reschedule"
      >
        <button class="btn btn-success" @click="acceptInterview">
          Confirm Interview
        </button>
        <button class="btn btn-info" @click="rescheduleInterview">
          Reschedule Interview
        </button>
      </div>

      <div
        class="grid grid-cols-2 gap-2 mt-12"
        v-if="data.interview_accept || data.interview_reschedule || responded"
      >
        <p>You have already accepted/rescheduled this interview.</p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Swal from "sweetalert2";
const baseUrl = useRuntimeConfig().public.baseUrl;
const route = useRoute();
const router = useRouter();
const responded = ref(false);
const { data, pending } = await useFetch<any>(
  baseUrl + `/interviews/${route.params.code}/get_interview_by_code`
);

async function acceptInterview() {
  let data = {
    acceptance: 1,
    code: route.params.code,
  };
  await fetch(
    baseUrl + `/interviews/${route.params.code}/respond_to_interview`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    }
  )
    .then((res) => res.json())
    .then((res) => {
      if (res.status == "success") {
        responded.value = true;
        router.go(0);
      } else {
        Swal.fire("Error", res.message, "error");
      }
    })
    .catch((err) => {
      Swal.fire("Error", err.message, "error");
      router.go(0);
    });
}

function rescheduleInterview() {
  Swal.fire({
    title: "State your preferred date and time",
    input: "text",
    inputAttributes: {
      autocapitalize: "off",
    },
    showCancelButton: true,
    confirmButtonText: "Reschedule",
    showLoaderOnConfirm: true,
    preConfirm: (reschedule) => {
      if (reschedule == "") {
        throw new Error("Please fill in your preferred date and time");
      }
    },
  })
    .then(async (result) => {
      if (result.isDismissed) {
        return;
      }
      let data = {
        acceptance: 2,
        code: route.params.code,
        reschedule: result.value,
      };
      await fetch(
        baseUrl + `/interviews/${route.params.code}/respond_to_interview`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      )
        .then((res) => res.json())
        .then((res) => {
          if (res.status == "success") {
            responded.value = true;
            router.go(0);
          } else {
            Swal.fire("Info", res.message, "info");
          }
        })
        .catch((err) => {
          Swal.fire("Info", err.message, "info");
          router.go(0);
        });
    })
    .catch(() => {
      Swal.fire("Info", "Please state your preferred date and time", "info");
      router.go(0);
    });
}
</script>
