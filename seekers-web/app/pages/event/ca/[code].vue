<template>
  <div id="ca-code-page" class="p-6 lg:p-12">
    <div class="max-w-2xl mx-auto border flex flex-col gap-8 px-4">
      <h1 class="p-4 text-lg text-center border-b">Confirm Application</h1>
      <p>
        Thank you for your application on Seekers for the following position.
        This job position requires verification from you in order to proceed.
        Please carefully check and verify your application
      </p>
      <h2 class="bg-gray-100 p-4 text-sm font-semibold text-center">
        JOB DETAILS
      </h2>
      <div class="grid grid-cols-[1fr_3fr] gap-2 px-4">
        <p>Job Title</p>
        <p>{{ data.job.title }}</p>
        <p>Company</p>
        <p>{{ data.job.company.name }}</p>
      </div>
      <h2 class="bg-gray-100 p-4 text-sm font-semibold text-center">
        APPLICANT DETAILS
      </h2>
      <div class="grid grid-cols-[1fr_3fr] gap-2 px-4">
        <p>Applicant</p>
        <p>{{ data.candidate.name }}</p>
        <p>Phone No.</p>
        <p>{{ data.candidate.mobile }}</p>
        <p>Email</p>
        <p>{{ data.candidate.email }}</p>
      </div>
      <button
        v-if="data.is_verified === 0"
        class="btn btn-success mt-12"
        @click="confirmApplication"
      >
        Confirm My Application
      </button>
      <p
        v-if="data.is_verified === 0"
        class="text-error-content text-xs -mt-4 text-center mb-6 cursor-pointer"
        @click="reportApplication"
      >
        I did not apply for this position
      </p>
      <div v-if="data.is_verified === 1 || responded" class="text-center">
        <p class="text-success-content">Your application has been confirmed</p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Swal from "sweetalert2";
const router = useRouter();
const baseUrl = useRuntimeConfig().public.baseUrl;
const route = useRoute();
const responded = ref(false);
const { data } = await useFetch<any>(
  baseUrl + `/applicants/${route.params.code}/get_applicant_by_code`
);

async function confirmApplication() {
  await fetch(
    baseUrl + `/applicants/${route.params.code}/confirm_application`,
    {
      method: "POST",
    }
  )
    .then((res) => res.json())
    .then((res) => {
      if (res.status == "success") {
        responded.value = true;
        Swal.fire("Success", res.message, "success");
      } else {
        Swal.fire("Error", res.message, "error");
      }
    })
    .catch((err) => {
      Swal.fire("Error", err.message, "error");
    });
}
async function reportApplication() {
  await fetch(baseUrl + `/applicants/${route.params.code}/report_application`, {
    method: "POST",
  })
    .then((res) => res.json())
    .then((res) => {
      if (res.status == "success") {
        responded.value = true;
        Swal.fire("Success", res.message, "success");
        router.go(0);
      } else {
        Swal.fire("Info", res.message, "error");
        router.go(0);
      }
    })
    .catch((err) => {
      Swal.fire("Info", err.message, "error");
      router.go(0);
    });
}
</script>
