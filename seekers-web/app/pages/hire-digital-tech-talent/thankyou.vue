<template>
  <div
    class="text-center flex flex-col md:gap-12 justify-around items-center p-12 md:py-24 max-w-7xl md:mx-auto"
  >
    <div>
      <h1 class="text-5xl font-semibold">
        <span class="text-[#FF1D03]">Thank you</span> we will contact you within
        48 hours.
      </h1>
      <div class="grid w-full mt-8">
        <div class="py-8 md:pl-8 text-left grid gap-4">
          <div>
            <p>Can't wait? You can reach out to us instead :)</p>
            <p>
              We offer free professional insights to help with your IT or
              Digital hiring needs.
            </p>
          </div>
          <div class="h-full w-full">
            <p>Schedule A Meeting With Us!</p>
            <div
              class="calendly-inline-widget h-[64rem] md:h-[700px]"
              data-url="https://calendly.com/_seekers/headhunting-with-seekers"
            ></div>
          </div>
          <div>
            <p>
              Chat with us via
              <NuxtLink to="https://wa.link/uss5nj" class="underline"
                ><Icon name="logos:whatsapp-icon" />WhatsApp</NuxtLink
              >
            </p>
          </div>
          <div>
            <p>
              If you are jobseekers, you can sign up
              <NuxtLink to="https://seekers.my/register" class="underline"
                >here.</NuxtLink
              >
            </p>
            <p class="text-sm">
              *Please note we can't help jobseekers through the above meeting
              and WhatsApp
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col md:flex-row gap-4 justify-center mt-4">
      <nuxt-link to="/hire-digital-tech-talent" class="btn px-12"
        >Back</nuxt-link
      >
      <nuxt-link to="/" class="btn px-12">Go to Home</nuxt-link>
    </div>
  </div>
</template>

<script lang="ts" setup>
// const options = {
//   url: "https://calendly.com/_seekers/headhunting-with-seekers?month=2024-08",
// };
const calendly = useCalendly();
onMounted(() => {
  calendly.initInlineWidget();
});
</script>
