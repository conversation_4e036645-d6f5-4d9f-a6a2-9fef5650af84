<template>
  <div id="candidate-dashboard-work" transition-style="in:circle:top-left">
    <CandidateDashboardHeader
      title="My Work & Experience"
      subtitle="Work experiences are automatically sorted based on each start date"
    />
    <div class="bg-white p-6 rounded-lg">
      <div v-if="!pending" transition-style="in:circle:top-left">
        <CandidateDashboardWorkExperienceItem
          v-for="(education, index) in profile?.work_experiences"
          :value="education"
          :key="education.id"
          :editAllowed="true"
        />
        <CandidateDashboardWorkExperienceItem :value="{}" lastStep addNew />
      </div>
      <div v-else>
        <span class="loading loading-ring loading-lg"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { data: profile, pending } = await useCandidate().getProfile();

interface WorkExperiences {
  id: number;
  candidate_id: number;
  company_name: string;
  role: string;
  start: string;
  end: string;
  responsibilities: string;
  created_at: string;
  updated_at: string;
}

const arrayToTry: WorkExperiences[] = profile?.value.work_experiences;

arrayToTry.sort(
  (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
);
</script>
