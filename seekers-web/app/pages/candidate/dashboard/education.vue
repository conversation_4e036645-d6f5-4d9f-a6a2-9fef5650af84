<template>
  <div id="candidate-dashboard-education">
    <CandidateDashboardHeader
      title="My Education"
      subtitle="Education are automatically sorted based on each start date"
    />
    <div class="bg-white p-6 rounded-lg">
      <div v-if="!pending"  transition-style="in:circle:top-left">
        <CandidateDashboardEducationItem
          v-for="(education, index) in profile?.candidate_education"
          :value="education"
          :key="education.id"
          editAllowed
        />
        <CandidateDashboardEducationItem :value="{}" lastStep addNew />
      </div>
      <div v-else>
        <span class="loading loading-ring loading-lg"></span>
      </div>
    </div>
  </div>
</template>
<script setup>
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { data: profile, pending } = useCandidate().getProfile();
</script>
