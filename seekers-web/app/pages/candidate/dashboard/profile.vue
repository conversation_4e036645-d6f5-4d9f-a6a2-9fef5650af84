<template>
  <div
    id="candidate-dashboard-profile"
    class="bg-white -m-3 md:-m-6 lg:-m-12 mb-0 md:mb-3 lg:mb-3"
    transition-style="in:circle:top-left"
    v-if="candidateData"
  >
    <CandidateDashboardProfileHeader
      :open-to-work="candidateData.open_to_work"
      :name="candidateData.name"
      :designation="candidateData.current_position"
      :state="candidateData.state"
      :email="candidateData.email"
      :createdAt="candidateData.created_at"
      :tags="candidateData.tags_data"
      :profileImg="
        candidateData?.image_url ||
        'https://seekers.my/blog/content/images/size/w100/2020/07/seekers_favicon.jpg'
      "
    />
    <div class="grid lg:grid-cols-[2fr_5fr] p-6 gap-6 lg:p-12 lg:gap-12">
      <div class="flex flex-col gap-3 md:gap-6">
        <!-- left section -->
        <div
          class="h-min bg-info grid sm:grid-cols-2 lg:grid-cols-1 gap-3 lg:gap-6 p-6 xl:p-12 rounded-lg"
        >
          <CandidateDashboardProfileSpecs v-if="candidateData" />
        </div>
        <CandidateDashboardSocialLinks />

        <div
          class="bg-info p-6 px-6 md:p-6 xl:p-8 rounded-lg text-sm"
          v-if="candidateData?.tags_data?.length != 0"
        >
          <p class="mb-3">Professional Skills</p>
          <div class="flex flex-wrap gap-2">
            <div
              class="rounded bg-white p-1 px-2 text-accent text-xs"
              v-for="skill in candidateData?.tags_data?.slice(0, 12)"
            >
              {{ skill }}
            </div>
          </div>
        </div>
      </div>

      <!-- right section -->
      <div class="flex flex-col gap-12 max-w-3xl">
        <CandidateDashboardAbout />
        <div>
          <div class="flex items-center mb-4 gap-4">
            <h3 class="font-semibold">Education</h3>
            <nuxt-link to="/candidate/dashboard/education">
              <Icon name="heroicons:pencil" class="w-5 h-5" />
            </nuxt-link>
          </div>
          <CandidateDashboardEducationItem
            v-for="(education, index) in candidateData?.candidate_education"
            :value="education"
            :key="education.id"
            :last-step="
              index === candidateData?.candidate_education?.length - 1
            "
          />
          <CandidateDashboardEducationItem
            v-if="candidateData?.candidate_education?.length == 0"
            :value="{}"
            lastStep
            addNew
          />
        </div>
        <div>
          <div class="flex items-center mb-4 gap-4">
            <h3 class="font-semibold">Work & Experience</h3>
            <nuxt-link to="/candidate/dashboard/work-experience">
              <Icon name="heroicons:pencil" class="w-5 h-5" />
            </nuxt-link>
          </div>
          <CandidateDashboardWorkExperienceItem
            v-for="(education, index) in candidateData?.work_experiences"
            :value="education"
            :key="education.id"
            :last-step="index === candidateData?.work_experiences?.length - 1"
          />
          <CandidateDashboardWorkExperienceItem
            v-if="candidateData?.work_experiences?.length == 0"
            :value="{}"
            lastStep
            addNew
          />
        </div>
        <CandidateDashboardPortfolio
          :data="candidateData.candidate_portfolio"
        />
        <div>
          <div class="flex items-center mb-4 gap-4">
            <h3 class="font-semibold">License & Certication</h3>
            <nuxt-link to="/candidate/dashboard/license-certification">
              <Icon name="heroicons:pencil" class="w-5 h-5" />
            </nuxt-link>
          </div>
          <CandidateDashboardLicenseCertificationItem
            v-for="(
              certification, index
            ) in candidateData?.candidate_certification"
            :value="certification"
            :key="certification.id"
            :last-step="
              index === candidateData?.candidate_certification?.length - 1
            "
          />

          <CandidateDashboardLicenseCertificationItem
            v-if="candidateData?.candidate_certification?.length == 0"
            :value="{}"
            lastStep
            addNew
          />
        </div>
      </div>
    </div>

    <div class="bg-white pb-12">
      <div
        class="bg-success/50 p-4 rounded-md grid gap-4 justify-center items-center text-center text-sm md:grid-cols-[2fr_1fr] md:max-w-3xl md:mx-auto"
      >
        <div class="grid gap-2">
          <p class="font-semibold">
            Discover Your Worth: Check Out Our Comprehensive
            <span class="text-primary">Salary Guide</span>
          </p>
          <p>Click Here to Discover Market Rates and Maximize Your Income!</p>
        </div>
        <nuxt-link
          to="/salary-guide"
          class="py-2 px-4 w-fit mx-auto rounded-md bg-primary text-white"
          >Salary Guide</nuxt-link
        >
      </div>
    </div>

    <!-- <DevOnly>
      <pre>{{ candidateData }}</pre>
    </DevOnly> -->
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const { data: candidateData } = await useCandidate().getProfile();
useCandidate().profile.value = candidateData;

const arrayToTry: WorkExperienceItem[] = candidateData?.value.work_experiences;

arrayToTry.sort(
  (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
);
</script>
