<template>
  <div
    id="candidate-dashboard-license-certification"
    transition-style="in:circle:top-left"
  >
    <CandidateDashboardHeader
      title="License & Certification"
      subtitle="Licenses and certifications are automatically sorted based on each start date"
    />

    <div class="bg-white p-6 rounded-lg">
      <div v-if="!pending" transition-style="in:circle:top-left">
        <CandidateDashboardLicenseCertificationItem
          v-for="(education, index) in profile?.candidate_certification"
          :value="education"
          :key="education.id"
          editAllowed
        />
        <CandidateDashboardLicenseCertificationItem
          :value="{}"
          lastStep
          addNew
        />
      </div>
      <div v-else>
        <span class="loading loading-ring loading-lg"></span>
      </div>
    </div>
  </div>
</template>
<script setup>
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { data: profile, pending } = useCandidate().getProfile();
</script>
