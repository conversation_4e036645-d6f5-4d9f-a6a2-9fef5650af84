<template>
  <div id="candidate-dashboard-following" transition-style="in:circle:top-left">
    <CandidateDashboardHeader
      title="Companies Followed"
      subtitle="Any new jobs posted by these companies will be shown in your feed."
    />

    <div class="bg-white p-6 rounded-lg">
      <div v-if="pending" class="border rounded-lg p-8 animate-pulse">
        <div class="bg-gray-100 rounded-lg h-2"></div>
        <div class="bg-gray-100 w-32 lg:w-64 mt-2 rounded-lg h-2"></div>
        <div class="bg-gray-100 w-64 lg:w-96 mt-2 rounded-lg h-2"></div>
      </div>
      <div
        class="grid gap-6 lg:grid-cols-2"
        :class="{ 'animate-pulse': pending }"
      >
        <div
          class="flex gap-4 lg:gap-6 items-center rounded-lg drop-shadow p-3 lg:p-6 bg-white"
          v-if="!pending && profile?.companies_following.length > 0"
          v-for="company in profile?.companies_following"
        >
          <img
            :src="company?.logo_url_seo"
            alt="comapny logo"
            class="w-20 h-20 object-contain cursor-pointer"
            @click="navigateTo(`/company/${company?.slug}`)"
          />
          <div
            @click="navigateTo(`/company/${company?.slug}`)"
            class="cursor-pointer flex-grow"
          >
            <h3 class="text-lg font-semibold">{{ company?.name }}</h3>
            <h5 class="text-xs text-gray-500">
              {{ company?.location != "null" ? company.location : "Malaysia" }}
            </h5>
            <h5 class="text-xs text-gray-500 mt-2">
              {{ company?.open_jobs_count }} Jobs Available
            </h5>
          </div>
          <button class="btn btn-circle" @click="unfollow(company.id)">
            <Icon name="heroicons:bookmark-solid" />
          </button>
        </div>
        <div
          v-if="!pending && profile?.companies_following.length === 0"
          class="border rounded-lg p-8"
        >
          <p>No Companies followed.</p>
          <button class="btn px-12 mt-8" @click="navigateTo('/company')">
            Browse Companies
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const { data: profile, pending, refresh } = useCandidate().getProfile();
const unfollow = async (company_id: number) => {
  await useCandidate().unfollowCompany(company_id);
  refresh();
};
</script>
