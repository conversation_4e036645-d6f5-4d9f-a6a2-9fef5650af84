<template>
  <div
    id="candidate-settings"
    :class="{ 'animate-pulse': loading }"
    transition-style="in:circle:top-right"
  >
    <CandidateDashboardHeader
      title="Profile"
      subtitle="Edit your profile, add a profile photo, and more."
    />

    <div class="bg-white p-6 rounded-lg">
      <div class="grid lg:grid-cols-2 gap-4">
        <div>
          <h3>Profile Photo</h3>
          <div class="my-4 flex gap-4 items-center">
            <img
              id="avatar-image"
              class="w-24 h-24 rounded-full"
              :src="
                profile?.image_url ||
                'https://seekers.my/blog/content/images/size/w100/2020/07/seekers_favicon.jpg'
              "
              alt="profile avatar"
            />
            <label for="image">
              <div class="btn btn-sm">Change</div>
            </label>
            <input
              @change="handleImageChange($event)"
              type="file"
              id="image"
              class="input w-full p-0 rounded-none hidden"
              placeholder="Image"
            />
          </div>
        </div>

        <div>
          <h3>CV | Resume</h3>
          <CustomCvInput
            :existingCvName="form.cv"
            @change="(file) => (form.cv = file)"
          />
        </div>
      </div>
      <!-- <div class="divider"></div> -->
      <div class="my-8">
        <h3>Actively looking for a new job?</h3>
        <p class="text-xs">
          Let our recruiters know by changing your status below.
        </p>
        <button
          @click="form.open_to_work = form.open_to_work === 1 ? 0 : 1"
          class="btn mt-4"
          :class="{
            'outline outline-offset-1 outline-gray-200':
              form.open_to_work === 0,
            'btn-primary': form.open_to_work === 1,
          }"
        >
          <Icon
            :name="
              form.open_to_work === 1 ? 'heroicons:check' : 'heroicons:x-mark'
            "
          />
          <p class="ml-1">
            {{
              form.open_to_work === 1
                ? "Yes, Im open to work!"
                : "No, im not open to work"
            }}
          </p>
        </button>
      </div>

      <div class="grid lg:grid-cols-2 gap-20 mt-4">
        <div class="flex flex-col gap-2">
          <h3>About Me</h3>
          <div
            v-for="(value, key) in aboutMeItems"
            class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm"
          >
            <label :for="key" class="label"
              ><div class="label-text text-accent">{{ value }}</div>
            </label>
            <input
              :id="key"
              name="education_level"
              :type="key === 'email' ? 'email' : 'text'"
              :disabled="key === 'email'"
              v-model="form[key]"
              class="input input-bordered text-sm"
              placeholder=""
            />
          </div>
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label for="nationality" class="label"
              ><div class="label-text text-accent">Nationality</div>
            </label>
            <div class="btn-group btn-group-horizontal">
              <button
                class="btn"
                :class="{ 'btn-accent': form.is_local === 1 }"
                @click="form.is_local = 1"
              >
                Malaysian
              </button>
              <button
                class="btn"
                :class="{ 'btn-accent': form.is_local === 0 }"
                @click="form.is_local = 0"
              >
                Expatriate
              </button>
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-2">
          <h3>Additional Info</h3>

          <!-- gender -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label"><div class="label-text">Gender</div></label>
            <select
              class="select text-sm w-full input-bordered"
              v-model="form.gender"
            >
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </div>

          <!-- education level -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label"
              ><div class="label-text">Education Level</div></label
            >
            <select
              class="select text-sm w-full input-bordered"
              v-model="form.education_level"
            >
              <option v-for="item in qualificationsList" :value="item.value">
                {{ item.label }}
              </option>
            </select>
          </div>

          <!-- current position -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label"
              ><div class="label-text">Current Job Title</div></label
            >
            <input
              id="key"
              name="key"
              type="key === 'email' ? 'email' : 'text'"
              v-model="form.current_position"
              class="input input-bordered text-sm"
              placeholder="Current Job Title"
            />
          </div>

          <!-- current salary -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label"
              ><div class="label-text">Current Salary (RM)</div></label
            >
            <input
              id="key"
              name="key"
              type="number"
              v-model="form.current_salary"
              class="input input-bordered text-sm"
              placeholder="Current Salary (RM)"
              min="500"
            />
          </div>

          <!-- expected salary -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label"
              ><div class="label-text">Expected Salary (RM)</div></label
            >
            <input
              id="key"
              name="key"
              type="key === 'email' ? 'email' : 'text'"
              v-model="form.expected_salary"
              class="input input-bordered text-sm"
              placeholder="Expected Salary (RM)"
            />
          </div>

          <!-- state -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label"><div class="label-text">State</div></label>
            <select
              class="select text-sm w-full input-bordered"
              v-model="form.state"
            >
              <option selected disabled>Choose a State</option>
              <option v-for="item in malaysianStates" :value="item">
                {{ item }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <div class="my-8 grid lg:grid-cols-2 gap-20">
        <div class="flex flex-col gap-2">
          <h3>Languages</h3>
          <CustomLanguagePicker v-model="form.languages" />
        </div>
      </div>
      <button
        class="btn btn-primary px-12 mt-8"
        @click="save"
        :disabled="loading"
      >
        {{ loading ? "Saving ......" : "Save Profile" }}
      </button>
      <div class="divider"></div>

      <!-- <button @click="candidate.logout" class="btn btn-accent">
        <Icon name="heroicons:arrow-left-on-rectangle-solid" class="w-5 h-5" />
        <span
          class="mx-4 text-xs animate__animated animate__fadeIn animate__faster"
          >Logout</span
        >
      </button> -->
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const candidate = useCandidate();
const { data: profile } = await candidate.getProfile();
candidate.logout;
const loading = useState(() => false);
const qualificationsList = useHelper.qualificationsList;
const malaysianStates = useHelper.malaysianStates;

const aboutMeItems = {
  name: "Name",
  email: "Email",
  mobile: "Mobile",
  birth_year: "Year of Birth",
};

const cvName = ref("");

const form = ref({
  birth_year: profile?.value.birth_year || null,
  current_position: profile?.value.current_position || null,
  cv: profile?.value.cv || null,
  education_level: profile?.value.education_level || "bachelor",
  email: profile?.value.email || "hidden",
  gender: profile?.value.gender || "Male",
  image: profile?.value.image || null,
  languages:
    profile?.value.candidate_languages.length > 0
      ? profile?.value.candidate_languages
      : [{ selected: true, language: "English", level: "Native" }],
  open_to_work: profile?.value.open_to_work,
  name: profile?.value.name || null,
  is_local: profile?.value.is_local,
  password: profile?.value.password || null,
  password_confirmation: profile?.value.password_confirmation || null,
  mobile: profile?.value.mobile || null,
  race: profile?.value.race || null,
  location: profile?.value.location || null,
  state: profile?.value.state || null,
  current_salary: profile?.value.current_salary || null,
  expected_salary: profile?.value.expected_salary || null,
});

function handleImageChange(event: any) {
  form.value.image = event.target.files[0];

  // set avatar-image element src to new image
  const reader = new FileReader();
  reader.onload = (e) => {
    const avatarImage = document.getElementById("avatar-image");
    avatarImage?.setAttribute("src", e.target?.result as string);
  };
  reader.readAsDataURL(event.target.files[0]);
}

async function save() {
  form.value.languages = JSON.stringify(form.value.languages);
  if (typeof form.value.cv == "string") {
    form.value.cv = null;
  }
  loading.value = true;
  await candidate.updateProfile(form.value);
  loading.value = false;
}
</script>
