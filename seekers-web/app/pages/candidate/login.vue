<template>
  <div
    id="candidate-login-page"
    class="grid grid-cols-1 lg:grid-cols-3 w-screen"
  >
    <div class="relative w-full">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463401/website/candidate/registration-bg_hpglmp.png"
        alt="login-bg"
        class="h-20 lg:h-screen w-full object-left-top object-cover"
      />
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463402/website/candidate/registration-vector_va9uqh.svg"
        alt=""
        class="hidden lg:block lg:absolute bottom-28 left-0 object-cover"
      />
    </div>
    <div class="col-span-2 p-8 py-28">
      <div
        id="login-form"
        class="max-w-xl mx-auto flex flex-col gap-8 justify-center"
      >
        <h1>Candidate Login</h1>
        <div>
          <p class="mb-2 text-sm">Email</p>
          <label for="email" class="sr-only">Email</label>

          <div class="relative">
            <input
              v-model="email"
              type="email"
              class="w-full rounded-lg border border-gray-200 p-4 pr-12 text-sm shadow-sm focus:outline-primary"
              placeholder="Enter email"
              v-on:keyup.enter="login"
            />

            <div class="absolute inset-y-0 right-4 inline-flex items-center">
              <Icon name="heroicons:at-symbol" class="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        <div>
          <p class="mb-2 text-sm">Password</p>
          <label for="password" class="sr-only" v-on:keyup.enter="login"
            >Password</label
          >
          <div class="relative">
            <input
              v-model="password"
              :type="viewPassword ? 'text' : 'password'"
              class="w-full rounded-lg border border-gray-200 focus:outline-primary p-4 pr-12 text-sm shadow-sm"
              placeholder="Enter password"
              v-on:keyup.enter="login"
            />

            <div class="absolute inset-y-0 right-4 inline-flex items-center">
              <Icon
                name="heroicons:eye"
                class="h-5 w-5 text-gray-400 cursor-pointer"
                @click="viewPassword = !viewPassword"
              />
            </div>
          </div>
        </div>

        <div class="flex w-full">
          <input
            type="checkbox"
            class="form-checkbox h-4 w-4 accent-primary"
            id="rememberme"
            checked
          />
          <label for="rememberme" class="ml-2 text-xs text-gray-400">
            Remember me
          </label>
          <nuxt-link
            to="/forgot-password"
            class="ml-auto text-xs text-gray-400"
          >
            Forgot your password?
          </nuxt-link>
        </div>

        <div class="w-full">
          <button @click="login" class="btn btn-primary text-sm w-full">
            Log In
          </button>

          <p class="text-sm text-center mt-4">
            Don't have an account?
            <nuxt-link to="/register" class="text-primary">Signup</nuxt-link>
          </p>
        </div>

        <div id="login-divider" class="grid grid-cols-11 grid-rows-2">
          <div class="col-span-5 border-b border-gray-200"></div>
          <div class="col-span-1 row-span-2 text-center text-xs">or</div>
          <div class="col-span-5 border-b border-gray-200"></div>
        </div>

        <div class="flex flex-col md:flex-row gap-2 md:gap-4 w-full">
          <!-- <button disabled class="btn bg-info text-info-content flex-grow">
            <Icon name="bxl:facebook" class="h-5 w-5" />
            <span class="ml-2">Login with Facebook</span>
          </button> -->
          <nuxt-link
            href="https://backend.seekers.my/auth/redirect/google"
            class="btn bg-error text-error-content flex-grow"
          >
            <Icon name="bxl:google" class="h-5 w-5" />
            <span class="ml-2">Login with Google</span>
          </nuxt-link>
          <nuxt-link
            href="https://backend.seekers.my/auth/redirect/linkedin"
            class="btn flex-grow"
          >
            <Icon name="bxl:linkedin" class="h-5 w-5" />
            <span class="ml-2">Login with LinkedIn</span>
          </nuxt-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const baseUrl = useRuntimeConfig().public.baseUrl;
const email = ref("");
const password = ref("");
const token = useCandidate().token;
const viewPassword = useState(() => false);
const candidateLogin = useCandidate().login;
const masterLogin = useCandidate().master_login;

if (token.value) {
  navigateTo("/candidate/dashboard");
}

async function login() {
  await candidateLogin(email.value, password.value);
}

async function master_login() {
  await masterLogin(email.value, password.value);
}
</script>
