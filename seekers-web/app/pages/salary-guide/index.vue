<template>
  <div id="salary-guide">
    <div
      class="grid justify-center place-items-center relative w-full h-full md:h-[40rem] -z-0 mb-4"
    >
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678182827/website/salary-guide%20page/salaryguide-hero-min_kwv25b.jpg"
        alt=""
        class="w-full h-full absolute -z-10 object-cover"
      />

      <h1 class="text-white text-xl md:text-5xl p-16 md:px-72 text-center">
        Discover where your Digital Role salary stands compared to industry
        standards
      </h1>
    </div>

    <!-- COLUMN TABS -->
    <div class="grid grid-cols-3">
      <div
        v-for="tab in tabs"
        class="flex items-center justify-center md:justify-start border-x border-t rounded-t-md p-3 gap-3 cursor-pointer"
        :class="{ 'bg-success': activeTab == tab.title }"
        @click="switchTab(tab.title)"
      >
        <div
          class="border p-3 rounded-md hidden md:block"
          :class="activeTab == tab.title ? 'bg-white' : 'bg-gray-100'"
        >
          <Icon :name="tab.icon" class="h-6 w-6 text-primary" />
        </div>
        <h2 class="text-sm font-semibold">{{ tab.title }}</h2>
      </div>
    </div>

    <!-- BUSINESS TABLE -->
    <div
      v-if="activeTab == 'Business'"
      class="grid md:grid-cols-2 gap-4 py-8 px-4 md:px-24 bg-success"
    >
      <div>
        <div
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] bg-[#D9D5D5] text-sm font-semibold p-2 pl-4"
        >
          <p>Job Title</p>
          <p>Salary Range</p>
        </div>
        <div
          v-for="business in businesses.slice(0, 29)"
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] md:grid-rows-1 bg-white p-2 pl-4 text-sm"
        >
          <p class="">{{ business.title }}</p>
          <p>RM{{ business.min }} - RM{{ business.max }}</p>
        </div>
      </div>
      <div>
        <div
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] bg-[#D9D5D5] text-sm font-semibold p-2 pl-4"
        >
          <p>Job Title</p>
          <p>Salary Range</p>
        </div>
        <div
          v-for="business in businesses.slice(30, 59)"
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] md:grid-rows-1 bg-white p-2 pl-4 text-sm"
        >
          <p class="">{{ business.title }}</p>
          <p>RM{{ business.min }} - RM{{ business.max }}</p>
        </div>
      </div>
    </div>

    <!-- CREATIVE TABLE -->
    <div
      v-if="activeTab == 'Creative'"
      class="grid md:grid-cols-2 gap-4 py-8 px-4 md:px-24 bg-success"
    >
      <div>
        <div
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] bg-[#D9D5D5] text-sm font-semibold p-2 pl-4"
        >
          <p>Job Title</p>
          <p>Salary Range</p>
        </div>
        <div
          v-for="creative in creatives"
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] md:grid-rows-1 bg-white p-2 pl-4 text-sm"
        >
          <p class="">{{ creative.title }}</p>
          <p>RM{{ creative.min }} - RM{{ creative.max }}</p>
        </div>
      </div>
    </div>

    <!-- TECHNICAL TABLE -->
    <div
      v-if="activeTab == 'Technical'"
      class="grid md:grid-cols-2 gap-4 py-8 px-4 md:px-24 bg-success"
    >
      <div>
        <div
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] bg-[#D9D5D5] text-sm font-semibold p-2 pl-4"
        >
          <p>Job Title</p>
          <p>Salary Range</p>
        </div>
        <div
          v-for="technical in technicals"
          class="grid grid-cols-2 gap-4 md:grid-cols-[7fr_3fr] md:grid-rows-1 bg-white p-2 pl-4 text-sm"
        >
          <p class="">{{ technical.title }}</p>
          <p>RM{{ technical.min }} - RM{{ technical.max }}</p>
        </div>
      </div>
    </div>

    <!-- IDEAL JOB MATCH -->
    <div class="relative -z-0 mt-24 md:mt-48">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678215208/website/salary-guide%20page/ideal-job-match-min_l9wtw2.jpg"
        alt=""
        class="absolute object-cover h-full w-full -z-10"
      />

      <div class="grid justify-items-center text-center p-6 md:p-32 z-10">
        <h2 class="font-semibold text-white text-3xl">
          Discover your ideal job match on Seekers Malaysia
        </h2>
        <p class="text-white text-xl my-8">
          Want to boost your salary by pursuing a different carrer path?
        </p>
        <nuxt-link to="/register" class="btn text-primary !font-semibold"
          >Find My Dream Job Now</nuxt-link
        >
      </div>
    </div>

    <!-- DIGITAL TALENT JOBCARDS -->
    <div id="digital-talent-jobs" class="bg-success bg-opacity-60 py-12">
      <div class="max-w-7xl md:mx-auto m-4">
        <h2 class="font-bold text-[#1A3454]">Digital Talent Jobs</h2>

        <div class="flex md:items-center mb-8">
          <p class="mb-8 mt-4">
            Try to match up jobs here with your expertise?
          </p>
        </div>

        <JobCardsList
          :jobsList="latestTechJobs?.data"
          :isLoading="pendingTech"
          :showPagination="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
useSeoMeta({
  title: "Malaysia Salary Guide for emerging IT/Tech/Digital positions",
  ogTitle: "Malaysia Salary Guide for emerging IT/Tech/Digital positions",
  description:
    "Discover current job rates and explore the highest paying jobs in the Tech/Digital industry, such as Digital Marketing, Software Engineer, or Data Scientist positions.",
  ogDescription:
    "Discover current job rates and explore the highest paying jobs in the Tech/Digital industry, such as Digital Marketing, Software Engineer, or Data Scientist positions.",
  ogType: "website",
  ogSiteName: "Seekers",
  ogUrl: "https://seekers.my/salary-guide",
  ogImage:
    "https://s3-ap-southeast-1.amazonaws.com/seekers-web/seekers_meta_banner.jpg",
});

const activeTab = ref("Business");
const tabs = [
  { icon: "heroicons:building-office-2", title: "Business" },
  { icon: "heroicons-solid:light-bulb", title: "Creative" },
  { icon: "heroicons:computer-desktop-20-solid", title: "Technical" },
];

function switchTab(tab: string) {
  activeTab.value = tab;
}

const { data: latestTechJobs, pending: pendingTech } = useFetch<any>(
  "/api/jobs/latest-tech",
  { key: "latest-tech" }
);

const businesses = [
  {
    title: "Affiliate Executive",
    min: 3800,
    max: 5500,
    category: "Business",
  },
  {
    title: "Affiliate Specialist",
    min: 5000,
    max: 9500,
    category: "Business",
  },
  {
    title: "Affiliate Manager",
    min: 6000,
    max: 8100,
    category: "Business",
  },
  {
    title: "Assistant Manager Digital Marketing",
    min: 5000,
    max: 8500,
    category: "Business",
  },
  {
    title: "Assistant Project Manager",
    min: 2500,
    max: 5800,
    category: "Business",
  },
  {
    title: "Associate Product Manager",
    min: 3000,
    max: 9000,
    category: "Business",
  },
  {
    title: "Brand Marketing Strategist",
    min: 2000,
    max: 8000,
    category: "Business",
  },
  {
    title: "Communications Specialist",
    min: 7000,
    max: 8500,
    category: "Business",
  },
  {
    title: "Community Manager",
    min: 6000,
    max: 12000,
    category: "Business",
  },
  {
    title: "Data Scientist",
    min: 5500,
    max: 9600,
    category: "Business",
  },
  {
    title: "Data Analyst / Data Scientist",
    min: 5500,
    max: 9000,
    category: "Business",
  },
  {
    title: "Digital Content Specialist",
    min: 3000,
    max: 5500,
    category: "Business",
  },
  {
    title: "Digital Marketing Executive (SEO)",
    min: 3000,
    max: 6500,
    category: "Business",
  },
  {
    title: "Digital Marketing Manager",
    min: 6000,
    max: 9000,
    category: "Business",
  },
  {
    title: "Digital Media Specialist",
    min: 3000,
    max: 3500,
    category: "Business",
  },
  {
    title: "Digital Marketing Specialist",
    min: 3000,
    max: 7500,
    category: "Business",
  },
  {
    title: "E-commerce Assistant",
    min: 1500,
    max: 3000,
    category: "Business",
  },
  {
    title: "E-commerce Assistant Manager",
    min: 5800,
    max: 6300,
    category: "Business",
  },
  {
    title: "E-commerce Executive",
    min: 2500,
    max: 5000,
    category: "Business",
  },
  {
    title: "E-commerce Senior Executive",
    min: 2800,
    max: 4500,
    category: "Business",
  },
  {
    title: "E-commerce Marketing",
    min: 2500,
    max: 5500,
    category: "Business",
  },
  {
    title: "E-commerce Director",
    min: 10000,
    max: 12000,
    category: "Business",
  },
  {
    title: "Editorial Assistant",
    min: 2500,
    max: 4500,
    category: "Business",
  },
  {
    title: "Head of Marketing",
    min: 7000,
    max: 15000,
    category: "Business",
  },
  {
    title: "Internal Communication Specialist",
    min: 2500,
    max: 5500,
    category: "Business",
  },
  {
    title: "Junior Digital Marketing Strategist",
    min: 2200,
    max: 2800,
    category: "Business",
  },
  {
    title: "Junior Data Scientist",
    min: 3800,
    max: 6000,
    category: "Business",
  },
  {
    title: "Marketing Executive",
    min: 2500,
    max: 5500,
    category: "Business",
  },
  {
    title: "Marketing Specialist",
    min: 3200,
    max: 5000,
    category: "Business",
  },
  {
    title: "Marketing Manager",
    min: 5000,
    max: 9000,
    category: "Business",
  },
  {
    title: "Marketing Communication Specialist",
    min: 3800,
    max: 6000,
    category: "Business",
  },
  {
    title: "Project Manager",
    min: 5000,
    max: 10000,
    category: "Business",
  },
  {
    title: "Product Marketing Manager",
    min: 7500,
    max: 10000,
    category: "Business",
  },
  {
    title: "Production Assistant",
    min: 2000,
    max: 2800,
    category: "Business",
  },
  {
    title: "Product Manager",
    min: 6000,
    max: 10000,
    category: "Business",
  },
  {
    title: "SEO Executive",
    min: 2600,
    max: 3800,
    category: "Business",
  },
  {
    title: "SEO Manager",
    min: 7000,
    max: 10000,
    category: "Business",
  },
  {
    title: "Senior Ad Performance Specialist",
    min: 5000,
    max: 7000,
    category: "Business",
  },
  {
    title: "Senior SEO Executive",
    min: 4500,
    max: 5000,
    category: "Business",
  },
  {
    title: "Senior SEO Specialist",
    min: 4000,
    max: 6500,
    category: "Business",
  },
  {
    title: "Senior Marketing Executive",
    min: 3800,
    max: 6000,
    category: "Business",
  },
  {
    title: "Senior Marketing Specialist",
    min: 5500,
    max: 8000,
    category: "Business",
  },
  {
    title: "Senior Marketing Manager",
    min: 8000,
    max: 15000,
    category: "Business",
  },
  {
    title: "Senior Performance Marketing Specialist",
    min: 5500,
    max: 7000,
    category: "Business",
  },
  {
    title: "Senior Product Manager",
    min: 10000,
    max: 18000,
    category: "Business",
  },
  {
    title: "Senior Project Manager",
    min: 8000,
    max: 14500,
    category: "Business",
  },
  {
    title: "Senior Social Media Executive",
    min: 3000,
    max: 4400,
    category: "Business",
  },
  {
    title: "Senior Social Media Specialist",
    min: 4000,
    max: 7000,
    category: "Business",
  },
  {
    title: "Social Media & Branding Planner",
    min: 2200,
    max: 3200,
    category: "Business",
  },
  {
    title: "Social Media Associate",
    min: 2000,
    max: 5500,
    category: "Business",
  },
  {
    title: "Social media editor",
    min: 2800,
    max: 4000,
    category: "Business",
  },
  {
    title: "Social media manager",
    min: 6000,
    max: 12000,
    category: "Business",
  },
  {
    title: "Social media strategist",
    min: 3200,
    max: 5500,
    category: "Business",
  },
  {
    title: "Social media management",
    min: 4000,
    max: 7000,
    category: "Business",
  },
  {
    title: "Social Media Specialist",
    min: 3000,
    max: 6000,
    category: "Business",
  },
  {
    title: "Senior Data Scientist",
    min: 9800,
    max: 14000,
    category: "Business",
  },
  {
    title: "Senior Digital Marketing Executive",
    min: 4000,
    max: 9000,
    category: "Business",
  },
  {
    title: "Senior Digital Marketing Specialist",
    min: 3800,
    max: 7500,
    category: "Business",
  },
  {
    title: "Senior Digital Marketing Manager",
    min: 8000,
    max: 12000,
    category: "Business",
  },
];

const creatives = [
  {
    title: "Content Creator",
    min: 2300,
    max: 4000,
    category: "Creative",
  },
  {
    title: "Content Editor Executive",
    min: 2500,
    max: 3500,
    category: "Creative",
  },
  {
    title: "Content Writer Executive",
    min: 2500,
    max: 3500,
    category: "Creative",
  },
  {
    title: "Content Specialist",
    min: 5000,
    max: 6500,
    category: "Creative",
  },
  {
    title: "Junior Graphic Designer",
    min: 2500,
    max: 3700,
    category: "Creative",
  },
  {
    title: "Junior Visual Designer",
    min: 2000,
    max: 3400,
    category: "Creative",
  },
  {
    title: "Managing Editor",
    min: 7000,
    max: 8000,
    category: "Creative",
  },
  {
    title: "Media Planner",
    min: 2500,
    max: 3000,
    category: "Creative",
  },
  {
    title: "Senior Visual Designer",
    min: 4100,
    max: 9000,
    category: "Creative",
  },
  {
    title: "Senior Content Specialist",
    min: 4300,
    max: 7200,
    category: "Creative",
  },
  {
    title: "Senior Content Writer cum Marketing Executive",
    min: 4000,
    max: 7000,
    category: "Creative",
  },
  {
    title: "Senior Editor",
    min: 2800,
    max: 4200,
    category: "Creative",
  },
  {
    title: "Senior UI/UX Designer",
    min: 8000,
    max: 15000,
    category: "Creative",
  },
  {
    title: "Senior Visual Merchandiser",
    min: 2500,
    max: 5000,
    category: "Creative",
  },
  {
    title: "Senior Graphic Designer",
    min: 4000,
    max: 8000,
    category: "Creative",
  },
  {
    title: "UI Designer",
    min: 4400,
    max: 8000,
    category: "Creative",
  },
  {
    title: "UX Designer",
    min: 7000,
    max: 10000,
    category: "Creative",
  },
  {
    title: "UI/UX Designer",
    min: 6000,
    max: 10000,
    category: "Creative",
  },
  {
    title: "Visual Designer",
    min: 4100,
    max: 10000,
    category: "Creative",
  },
  {
    title: "Visual Merchandiser",
    min: 3000,
    max: 5000,
    category: "Creative",
  },
  {
    title: "Visual Merchandiser Manager",
    min: 4000,
    max: 8000,
    category: "Creative",
  },
  {
    title: "Video Editor",
    min: 2000,
    max: 5000,
    category: "Creative",
  },
  {
    title: "Video Producer",
    min: 3500,
    max: 5000,
    category: "Creative",
  },
];

const technicals = [
  {
    title: "Backend Developer Engineer",
    min: 6000,
    max: 10000,
    category: "Technical",
  },
  {
    title: "DevOps Engineer",
    min: 5000,
    max: 16000,
    category: "Technical",
  },
  {
    title: "Database Engineer",
    min: 5000,
    max: 9000,
    category: "Technical",
  },
  {
    title: "Frontend Developer/Engineer",
    min: 4500,
    max: 8000,
    category: "Technical",
  },
  {
    title: "Fullstack Developer/Engineer",
    min: 7000,
    max: 10000,
    category: "Technical",
  },
  {
    title: "Fullstack Web Developer",
    min: 5000,
    max: 10000,
    category: "Technical",
  },
  {
    title: "IT Assurance Analyst",
    min: 3000,
    max: 8000,
    category: "Technical",
  },
  {
    title: "IT Project Manager",
    min: 8000,
    max: 14000,
    category: "Technical",
  },
  {
    title: "IT Business Analyst",
    min: 5000,
    max: 10000,
    category: "Technical",
  },
  {
    title: "Junior Backend Developer",
    min: 3000,
    max: 7600,
    category: "Technical",
  },
  {
    title: "Junior Frontend Developer",
    min: 2200,
    max: 3500,
    category: "Technical",
  },
  {
    title: "Junior Web Developer",
    min: 2000,
    max: 5000,
    category: "Technical",
  },
  {
    title: "Junior QA Analyst",
    min: 3000,
    max: 5000,
    category: "Technical",
  },
  {
    title: "Junior DevOps Engineer",
    min: 3000,
    max: 8000,
    category: "Technical",
  },
  {
    title: "Junior Database Engineer",
    min: 3000,
    max: 5000,
    category: "Technical",
  },
  {
    title: "Junior IT Business Analyst",
    min: 3000,
    max: 6000,
    category: "Technical",
  },
  {
    title: "Lead Full Stack Developer",
    min: 10000,
    max: 22000,
    category: "Technical",
  },
  {
    title: "Senior Frontend Developer",
    min: 6000,
    max: 8000,
    category: "Technical",
  },
  {
    title: "Senior Backend Developer",
    min: 9000,
    max: 12000,
    category: "Technical",
  },
  {
    title: "Senior Web Designer",
    min: 4000,
    max: 10000,
    category: "Technical",
  },
  {
    title: "Senior QA Analyst",
    min: 3500,
    max: 5500,
    category: "Technical",
  },
  {
    title: "Software QA Analyst",
    min: 3000,
    max: 6000,
    category: "Technical",
  },
  {
    title: "Senior DevOps Engineer",
    min: 8000,
    max: 17000,
    category: "Technical",
  },
  {
    title: "Senior IT Business Analyst",
    min: 8000,
    max: 15000,
    category: "Technical",
  },
  {
    title: "Senior Web Developer",
    min: 5000,
    max: 10000,
    category: "Technical",
  },
  {
    title: "Technical Product Manager",
    min: 7000,
    max: 13000,
    category: "Technical",
  },
  {
    title: "Web Developer",
    min: 2000,
    max: 5900,
    category: "Technical",
  },
  {
    title: "Web Content Specialist",
    min: 2000,
    max: 8200,
    category: "Technical",
  },
  {
    title: "Web Designer",
    min: 2500,
    max: 6000,
    category: "Technical",
  },
  {
    title: "Web Marketer",
    min: 4000,
    max: 5000,
    category: "Technical",
  },
];
</script>
