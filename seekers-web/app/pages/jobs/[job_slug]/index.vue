<template>
  <div id="candidate-job-details-page" class="">
    <!-- HEADER -->
    <div
      class="bg-gradient-to-r from-[#E5EBF5]/60 via-[#F5F7FC]/60 to-success/60 p-2 py-6 md:py-12"
    >
      <div class="grid md:flex gap-4 max-w-7xl mx-auto">
        <div class="flex gap-4 items-center">
          <img
            :src="jobData?.company?.logo_url"
            alt="Company Logo"
            class="w-20 h-20 rounded object-contain"
          />

          <div class="grid gap-2">
            <h1 class="font-semibold text-lg leading-6">
              {{ jobData?.title }}
            </h1>
            <div class="grid gap-2 md:flex text-xs">
              <p class="text-gray-500">
                <Icon name="heroicons:briefcase" class="w-4 h-4 mr-1" />{{
                  jobData?.role?.name
                }}
              </p>
              <p v-if="jobData?.city != null" class="text-gray-500">
                <Icon name="heroicons:map-pin" class="w-4 h-4 mr-1" />{{
                  jobData?.city
                }}
              </p>
              <p class="text-gray-500">
                <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />{{
                  getRelatedDate(jobData?.updated_at as string)
                }}
              </p>
              <p class="text-gray-500">
                <Icon name="heroicons:banknotes" class="w-4 h-4 mr-1" />{{
                  jobData?.salary_range
                }}
              </p>
            </div>
            <div class="flex gap-3">
              <p
                v-if="jobData?.job_condition?.type == 'Permanent'"
                class="badge badge-success text-xs"
              >
                Full Time
              </p>
              <p
                v-if="jobData?.job_condition?.type == 'Permanent'"
                class="badge badge-info text-xs"
              >
                Permanent
              </p>
              <p
                v-if="closingSoon(jobData?.created_at || '')"
                class="badge badge-warning text-xs"
              >
                Urgent
              </p>
              <p v-else class="hidden badge badge-warning text-xs"></p>
            </div>
          </div>
        </div>
        <div
          class="md:ml-auto flex justify-center items-center gap-4"
          v-if="jobData?.status !== 'closed'"
        >
          <nuxt-link
            :to="`/jobs/${route.params.job_slug}/apply`"
            class="btn btn-primary w-52"
            >Apply for job</nuxt-link
          >
          <button
            v-if="!isSaved"
            class="btn btn-success"
            :disabled="pending || !candidateData"
            @click="saveJob"
          >
            <Icon name="heroicons:bookmark" />
          </button>
          <button
            v-else
            class="btn btn-danger"
            :disabled="pending"
            @click="unsaveJob"
          >
            <Icon name="heroicons:bookmark-solid" />
          </button>
        </div>
      </div>
    </div>

    <!-- LEFT SIDE -->
    <div class="max-w-7xl mx-auto grid md:grid-cols-[5fr_3fr] py-12">
      <!-- JOB DESCRIPTION, RESPONSILITIES, ETC -->
      <div class="text-sm mx-4">
        <div class="mb-6" v-if="jobData?.description">
          <h2 class="font-semibold mb-4">Job Description</h2>
          <p>
            {{ jobData?.description }}
          </p>
        </div>
        <div
          class="mb-6"
          v-if="
            jobData?.responsibility_arr && jobData.responsibility_arr.length > 0
          "
        >
          <h2 class="font-semibold mb-4">Key Responsibilities</h2>
          <ul class="list-disc px-4">
            <li
              v-for="responsibility in jobData?.responsibility_arr"
              class="mb-2"
            >
              {{ responsibility }}
            </li>
          </ul>
        </div>
        <div
          class="mb-6"
          v-if="
            jobData?.req_must_have_arr && jobData.req_must_have_arr.length > 0
          "
        >
          <h2 class="font-semibold mb-4">Skills & Experiences</h2>
          <ul class="list-disc px-4">
            <li v-for="skills in jobData?.req_must_have_arr" class="mb-2">
              {{ skills }}
            </li>
          </ul>
        </div>

        <!-- SHARE BUTTONS -->
        <div class="flex items-center gap-2">
          <p class="font-semibold mr-2">Share this job</p>

          <!-- BUTTONS -->
          <nuxt-link
            :to="`https://www.facebook.com/sharer/sharer.php?u=${jobData?.url}&title=${jobData?.title}&description=${jobData?.description}`"
            class="flex items-center btn btn-circle btn-primary"
          >
            <Icon name="bxl:facebook" class="w-5 h-5" />
            <!-- <p class="hidden md:block">Facebook</p> -->
          </nuxt-link>
          <nuxt-link
            :to="`https://twitter.com/intent/tweet?text=${jobData?.title}&url=${jobData?.url}`"
            class="flex items-center btn btn-circle bg-[#1967D2] text-white"
          >
            <Icon name="bxl:twitter" class="w-5 h-5" />
            <!-- <p class="hidden md:block">Twitter</p> -->
          </nuxt-link>
          <nuxt-link
            :to="`https://www.linkedin.com/sharing/share-offsite/?url=${jobData?.url}`"
            class="flex items-center btn btn-circle bg-[#D93025] text-white"
          >
            <Icon name="bxl:linkedin-square" class="w-5 h-5" />
            <!-- <p class="hidden md:block">Google+</p> -->
          </nuxt-link>
          <nuxt-link
            :to="`https://api.whatsapp.com/send?text=${jobData?.title}%0D%0A${jobData?.url}%0D%0A${jobData?.description}`"
            class="flex items-center btn btn-circle bg-[#F9AB00] text-white"
          >
            <Icon name="bxl:whatsapp" class="w-5 h-5" />
            <!-- <p class="hidden md:block">Instagram</p> -->
          </nuxt-link>
        </div>

        <!-- RELATED JOBS DESKTOP -->
        <div class="mt-12 hidden md:block" id="related-jobs">
          <h3 class="font-semibold mb-6">Related Jobs</h3>
          <CandidateHorizontalJobCard
            v-if="relatedJobsData?.length > 0"
            v-for="relatedJob in relatedJobsData"
            :job="relatedJob"
          />
          <div v-else class="border rounded-lg w-max p-8 pr-20">
            No related jobs at the moment
          </div>
        </div>
      </div>

      <!-- RIGHT SIDE -->
      <div class="m-4 md:m-0">
        <div class="bg-info rounded-md p-8">
          <div class="mb-12">
            <p class="font-semibold mb-4">Job Conditions:</p>
            <div class="grid md:grid-cols-2 text-sm gap-4">
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon
                    name="game-icons:sands-of-time"
                    class="h-6 w-6 text-primary"
                  />
                </div>

                <div class="ml-2">
                  <p class="font-semibold">Probation Period:</p>
                  <p>
                    {{ jobData?.job_condition?.probation || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="fluent-mdl2:group" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Age:</p>
                  <p>
                    {{ jobData?.job_requirement?.age_min }} -
                    {{ jobData?.job_requirement?.age_max }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon
                    name="clarity:avatar-line"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Job Type:</p>
                  <p>{{ jobData?.job_condition?.type || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon
                    name="heroicons:banknotes"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Allowances:</p>
                  <p>
                    {{ jobData?.job_condition?.allowances || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:clock" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Hours:</p>
                  <p>{{ jobData?.job_condition?.working_hour || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon
                    name="heroicons:language"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Languages:</p>
                  <p class="capitalize break-all">
                    {{ jobData?.job_requirement?.language || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:sun" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Days:</p>
                  <p>{{ jobData?.job_condition?.working_days || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="bi:people" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Malaysia Only:</p>
                  <p>
                    {{ jobData?.job_requirement?.is_local_only ? "Yes" : "No" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon
                    name="heroicons:calendar"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Annual Leave:</p>
                  <p>
                    {{ jobData?.job_condition?.annual_leave || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="vaadin:coin-piles" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Benefits:</p>
                  <p>
                    {{ jobData?.job_condition?.benefits || "-" }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <p class="font-semibold mb-2">Job Skills</p>
            <p
              v-for="tag in jobData?.tags_data"
              class="btn mb-2 mr-2 w-fit gap-4 capitalize"
            >
              {{ tag }}
            </p>
          </div>
        </div>

        <div class="bg-info rounded-md p-8 my-4 md:w-3/4">
          <div class="flex items-center">
            <img
              :src="jobData?.company?.logo_url"
              alt="Company Logo"
              class="w-16 h-16 rounded object-contain mr-4"
            />
            <h2 class="font-semibold text-sm">{{ jobData?.company?.name }}</h2>
          </div>
          <div class="grid items-center text-sm gap-4 mt-4">
            <div class="flex gap-2 justify-between">
              <p class="font-semibold">Primary Industry:</p>
              <h3 class="text-end text-sm">{{ jobData?.industry?.name }}</h3>
            </div>
            <div
              v-if="!isConfidential && jobData?.company?.size"
              class="flex gap-2 justify-between"
            >
              <p class="font-semibold">Company size:</p>
              <p class="text-end">{{ jobData?.company?.size }}</p>
            </div>
            <div
              v-if="jobData?.company?.location != null"
              class="flex justify-between"
            >
              <p class="font-semibold">Location:</p>
              <p class="text-end">{{ jobData?.company?.location }}</p>
            </div>
            <div
              v-if="!isConfidential && jobData?.company?.reg_no"
              class="flex justify-between"
            >
              <p class="font-semibold">Company Registration:</p>
              <p class="text-end">{{ jobData?.company?.reg_no }}</p>
            </div>
          </div>
          <nuxt-link
            :to="isConfidential ? '#' : `/company/${jobData?.company?.slug}`"
            class="btn btn-success mt-4 w-full"
            :class="{
              'btn-disabled': isConfidential,
            }"
            >{{
              isConfidential ? "Company Confidential" : "View Company Profile"
            }}</nuxt-link
          >
        </div>
      </div>

      <!-- RELATED JOBS MOBILE -->
      <div class="m-4 md:m-0 md:hidden" id="related-jobs">
        <h3 class="font-semibold mb-6">Related Jobs</h3>
        <CandidateHorizontalJobCard
          v-if="relatedJobsData?.length > 0"
          v-for="relatedJob in relatedJobsData"
          :job="relatedJob"
        />
        <div v-else class="border rounded-lg w-max py-8 px-10 mx-auto">
          No related jobs at the moment
        </div>
      </div>
    </div>

    <!-- Refer A Talent Mobile -->
    <div
      class="bg-[url(https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1692129472/website/candidate/refer-a-talent-banner-rs_pvmbmh.jpg)] lg:hidden"
    >
      <div class="p-8 md:p-24">
        <div class="grid gap-4 bg-white p-8 xl:w-1/2 rounded-xl mr-auto">
          <p class="text-4xl font-semibold">Refer-A-Talent</p>
          <p class="mt-4 mb-2">
            Know someone perfect for this role? Refer them to Seekers and
            <span class="font-semibold">earn RM500</span>! Join our referral
            program now and help us find top talent!
          </p>
          <nuxt-link
            :to="`/refer-a-talent?job=${jobData?.slug}#refer-talent-form`"
            class="btn btn-primary w-fit"
            >Refer-A-Talent Now!</nuxt-link
          >
        </div>
      </div>
    </div>

    <!-- Refer A Talent Desktop -->
    <div class="relative hidden lg:block">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1692129472/website/candidate/refer-a-talent-banner-rs_pvmbmh.jpg"
      />
      <div
        class="grid gap-4 bg-white p-8 lg:w-2/5 rounded-xl mr-auto absolute lg:top-[10%] xl:top-[15%] 2xl:top-1/4 lg:left-[5%] 2xl:left-[8%]"
      >
        <p class="lg:text-3xl xl:text-4xl font-semibold">Refer-A-Talent</p>
        <p class="mt-4 mb-2 lg:text-sm xl:text-base">
          Know someone perfect for this role? Refer them to Seekers and
          <span class="font-semibold">earn RM500</span>! Join our referral
          program now and help us find top talent!
        </p>
        <nuxt-link
          :to="`/refer-a-talent?job=${jobData?.slug}#refer-talent-form`"
          class="btn btn-primary w-fit"
          >Refer-A-Talent Now!</nuxt-link
        >
      </div>
    </div>

    <DevOnly>
      <!-- <pre>{{ jobData }}</pre> -->
      <!-- <pre>{{ relatedJobsData }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
const route = useRoute();
const { data: jobData } = await useJobs().getBySlug(
  route.params.job_slug as string
);

if (jobData.value?.error) {
  throw createError({ statusCode: 404, statusMessage: "Job Not Found" });
}
let googleRichSearchJobData = {};
if (jobData.value?.status !== "closed") {
  googleRichSearchJobData = {
    "@context": "https://schema.org/",
    "@type": "JobPosting",
    title: jobData.value?.title,
    description: jobData.value?.description
      ? jobData.value?.description
      : jobData.value?.responsibility_arr,
    datePosted: jobData.value?.created_at || jobData.value?.updated_at,
    hiringOrganization: {
      "@type": "Organization",
      name: jobData.value?.company?.name,
      logo: jobData.value?.company?.logo_url,
    },
    baseSalary: {
      "@type": "MonetaryAmount",
      currency: "MYR",
      value: {
        "@type": "QuantitativeValue",
        minValue: jobData.value?.salary_min,
        maxValue: jobData.value?.salary_max,
      },
    },
    employmentType: jobData.value?.job_condition?.type,
    jobLocation: {
      "@type": "Place",
      address: {
        "@type": "PostalAddress",
        addressLocality: jobData.value?.company?.city,
        addressRegion: jobData.value?.company?.state,
        postalCode: jobData.value?.company?.postal_code,
      },
    },
  };
}

useHead({
  link: [
    {
      rel: "canonical",
      href: () => `https://seekers.my/jobs/${jobData.value?.slug}`,
    },
  ],
  script: [
    {
      type: "application/ld+json",
      children: JSON.stringify(googleRichSearchJobData),
    },
  ],
});
useServerSeoMeta({
  title: () => `${jobData.value?.title} | Seekers`,
  ogTitle: () => `${jobData.value?.title} | Seekers`,
  ogType: "website",
  ogUrl: () => `https://seekers.my/jobs/${jobData.value?.slug}`,
  description: () =>
    `${
      jobData.value?.description ||
      `${jobData.value?.title} job position at ${jobData.value?.company.name}`
    }`,
  ogDescription: () =>
    `${
      jobData.value?.description ||
      `${jobData.value?.title} job position at ${jobData.value?.company.name}`
    }`,
  ogImage: () => `${jobData.value?.company.logo_url}`,
});

const { data: candidateData, pending, refresh } = useCandidate().getProfile();
const save = useCandidate().saveJob;
const unsave = useCandidate().unsaveJob;
const getRelatedDate = useHelper.getRelatedDate;

const { data: relatedJobsData } = useJobs().getRelatedJobs(
  jobData.value?.id as number
);

// if route has 'uid', save it to using useCookie for 24hrs
if (route.query.uid) {
  const referrerUID = useCookie("referrer_uid", { maxAge: 60 * 60 * 24 });
  referrerUID.value = route.query.uid as string;
}

async function saveJob() {
  await save(jobData.value?.id as number);
  await refresh();
}
async function unsaveJob() {
  await unsave(jobData?.value?.id as number);
  await refresh();
}
const isSaved = computed(() => {
  return candidateData?.value?.fav_job_ids?.some(
    (id: number) => id == jobData?.value?.id
  );
});

const isConfidential = computed(() => {
  return jobData?.value?.company?.name.toLowerCase().includes("confidential");
});

function closingSoon(date: string) {
  // return true if date is more than 21 days old from today
  return dayjs().diff(dayjs(date), "day") > 21;
}
</script>
