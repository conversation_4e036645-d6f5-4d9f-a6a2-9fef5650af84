<template>
  <div id="candidate-jobs-list-page" class="min-h-screen">
    <CandidatePageHeader
      class="w-full text-center bg-info px-4 py-10"
      :title="searchFilter.keywords"
    />
    <div
      class="max-w-7xl mx-auto grid sm:grid-cols-[2fr_3fr] lg:grid-cols-[1fr_3fr]"
    >
      <CandidateJobFilter
        class="bg-info p-6 pt-0 lg:p-8 lg:pb-40"
        @search="refetchJobs"
      />
      <div id="search-results-list" class="p-8">
        <div
          class="flex items-center gap-2 flex-wrap justify-center md:justify-start"
        >
          <p class="text-sm">
            Showing
            <span class="font-semibold">{{ searchFilter.page }}</span> of
            <span class="font-semibold">{{ jobsData?.last_page }}</span> pages
          </p>

          <nuxt-link
            v-if="searchFilter.page > 1"
            class="btn btn-info btn-sm"
            :to="`/jobs?page=${prevPageNo}`"
          >
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </div>
          <nuxt-link
            v-if="searchFilter.page < jobsData?.last_page"
            class="btn btn-info btn-sm"
            :to="`/jobs?page=${nextPageNo}`"
          >
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </div>

          <select
            name="sort"
            v-model="searchFilter.sort"
            id="sort-selector"
            class="select select-sm select-accent outline-never md:ml-auto border-base-200"
            @change="refetchJobs"
          >
            <option value="">Latest</option>
            <option value="oldest">Oldest</option>
          </select>
          <select
            v-model="searchFilter.limit"
            @change="refetchJobs"
            name="display"
            id="perpage-selector"
            class="select select-sm select-accent outline-never border-base-200"
          >
            <option
              v-for="option in displayPerPageOptions"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
          <p class="text-xs">per page</p>
        </div>
        <!--  List of jobs -->
        <JobCardsList
          :jobsList="pending ? null : jobsData?.data"
          :isLoading="pending"
          :showPerPage="12"
          :showPagination="false"
          :lastPage="jobsData?.last_page"
        />
      </div>
    </div>
    <DevOnly>
      <!-- <pre>{{ jobsData }}</pre> -->
      <!-- <pre>{{ route }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup>
const route = useRoute();
const pending = ref(false);
const jobsData = ref(null);
const searchFilter = useJobs().searchFilter();

const SEOTagLookup = {
  5418: {
    title: "Fresh Graduate jobs in Malaysia 2024 - Seekers",
    description:
      "Apply fresh graduate jobs in Malaysia 2024 at Seekers platform. Browse jobs in Sales Executive, Admin Executive, Customer Service and more!",
  },
  10209: {
    title: "Mandarin Speaking Jobs in Malaysia 2024 - Seekers",
    description:
      "Looking for Mandarin speaker jobs in Malaysia in 2024? Find roles that require fluency in Mandarin, such as Finance jobs, Accounting jobs, Sales & Marketing jobs, Business Development jobs and more.",
  },
  14099: {
    title: "JLPT jobs in Malaysia 2024 | Japanese Speaking Jobs",
    description:
      "Discover job opportunities in Malaysia that require candidates to have JLPT certification and involve tasks such as translating from English to Japanese or working as a Customer Support job, Japanese translator job and more!",
  },
  14112: {
    title: "Digital & Technology jobs in Malaysia 2024",
    description:
      "Discover a wide range of Digital, IT & Tech jobs opportunities in Malaysia 2024. Find Software Engineering jobs, IT Project Manager jobs, Data Scientist jobs, Cybersecurity jobs, digital marketing jobs and more!",
  },
};

// SEO : If tag id is not found, default to "Find Jobs"
const tagId = route.query.tag_id;
const computedTitle = useState(() => SEOTagLookup[tagId]?.title || "Find Jobs");
const computedDescription = useState(
  () => SEOTagLookup[tagId]?.description || "Seekers - Find Jobs"
);
useSeoMeta({
  title: computedTitle,
  description: computedDescription,
  ogTitle: computedTitle,
  ogDescription: computedDescription,
  ogType: "website",
  ogSiteName: "Seekers",
  ogImage:
    "https://s3-ap-southeast-1.amazonaws.com/seekers-web/seekers_meta_banner.jpg",
  ogUrl: "https://seekers.my/jobs",
});

// get route query and override search filter
const queries = route.query;
let newFilters = {
  ...searchFilter.value,
};
//if query keys have values, override search filter
for (const key in queries) {
  if (queries[key]) {
    newFilters[key] = queries[key];
  }
}

searchFilter.value = newFilters;
if (queries.category) {
  const category = useHelper.jobCategoryLookupByName(queries.category);
  searchFilter.value.roles = category ? category.roles.join(",") : "";
} else {
  searchFilter.value.roles = "";
}
if (!queries.keywords) {
  searchFilter.value.keywords = "";
}

const { data: initialData } = await useJobs().searchWithFilter();
jobsData.value = initialData.value;

const displayPerPageOptions = [
  { value: 12, label: "12" },
  { value: 24, label: "24" },
  { value: 36, label: "36" },
];
async function refetchJobs() {
  pending.value = true;
  const { data } = await useJobs().searchWithFilter();
  jobsData.value = data.value;
  pending.value = false;
}

watch(
  () => route.query.page,
  (page) => {
    if (page) {
      searchFilter.value.page = parseInt(page);
      refetchJobs();
    } else {
      searchFilter.value.page = 1;
      refetchJobs();
    }
  }
);

watch(
  () => route.query.tag_id,
  (tag_id) => {
    if (!tag_id) {
      searchFilter.value.tag_id = "";
      refetchJobs();
    }
  }
);

watch(
  () => searchFilter.value.limit,
  () => {
    searchFilter.value.page = 1;
    refetchJobs();
  }
);

watch(
  () => searchFilter.value.job_types,
  () => {
    searchFilter.value.page = 1;
    refetchJobs();
  }
);

watch(
  () => searchFilter.value.levels,
  () => {
    searchFilter.value.page = 1;
    refetchJobs();
  }
);

const nextPageNo = computed(() => {
  return parseInt(searchFilter.value.page) + 1;
});
const prevPageNo = computed(() => {
  return parseInt(searchFilter.value.page) - 1;
});
</script>
