<template>
  <div id="recruiter-dashboard-profile">
    <RecruiterDashboardHeader
      title="Manage Candidates"
      subtitle="Add or Edit your Candidates"
    />
    <div class="bg-white rounded-lg p-8 grid md:grid-cols-2 gap-4">
      <div class="flex items-center md:col-span-2">
        <h3>All Candidates</h3>
        <button
          :disabled="recruiterPending"
          @click="addMode = !addMode"
          class="btn ml-auto"
          :class="{ 'btn-primary': !addMode, 'btn-secondary': addMode }"
        >
          {{ addMode ? "Cancel" : "Add Candidate" }}
        </button>
      </div>

      <div v-for="candidate in candidateData?.data" v-if="!pending && !addMode">
        <RecruiterDashboardCandidateCard :candidate="candidate" />
      </div>
      <div v-if="pending && !addMode" v-for="i in 2">
        <RecruiterDashboardCandidateCard
          :candidate="{ i }"
          class="animate-pulse"
        />
      </div>

      <RecruiterDashboardCandidateForm
        class="md:col-span-2"
        v-if="addMode"
        @submit="addMode = false"
        :candidate="{}"
        mode="add"
        :recruiterId="recruiterData?.id"
      />
    </div>
    <!-- <pre>{{ candidateData?.data }}</pre> -->
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});
const { data: recruiterData, pending: recruiterPending } =
  useRecruiter().getProfile();
const { data: candidateData, pending } = useRecruiter().getCandidates();
const addMode = ref(false);
</script>
