<template>
  <div id="candidate-details-and-edit-page">
    <RecruiterDashboardHeader
      title="Candidate Details"
      subtitle="Add or Edit your Candidates"
    />
    <button class="btn btn px-12" @click="editMode = true" v-if="!editMode">
      Edit Candidate
    </button>
    <RecruiterDashboardCandidateForm
      class="animate__animated animate__fadeInDown"
      v-if="editMode && !pending"
      :candidate="candidateData"
      @submit="navigateTo('/recruiter/dashboard/candidates')"
      mode="edit"
    />
  </div>
</template>
<script setup>
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});
const route = useRoute();
const { data: candidateData, pending } = useRecruiter().getCandidateById(
  route.params.candidate_id
);

const editMode = ref(true);
</script>
