<template>
  <section id="recruiter-dashboard-all-jobs">
    <RecruiterDashboardHeader
      title="All Jobs"
      subtitle="Search and Filter all available jobs"
    />

    <div class="mx-auto grid sm:grid-cols-[2fr_3fr] lg:grid-cols-[1fr_3fr]">
      <CandidateJobFilter
        class="bg-white p-6 lg:p-8 lg:pb-40"
        @search="refetchJobs"
      />
      <div id="search-results-list" class="p-8">
        <div
          class="flex items-center gap-2 flex-wrap justify-center md:justify-start"
        >
          <p class="text-sm">
            Showing
            <span class="font-semibold">{{ searchFilter.page }}</span> of
            <span class="font-semibold">{{ jobsData?.last_page }}</span> pages
          </p>

          <nuxt-link
            v-if="searchFilter.page > 1"
            class="btn btn-info btn-sm"
            :to="`/recruiter/dashboard/jobs?page=${prevPageNo}`"
          >
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </div>
          <nuxt-link
            v-if="searchFilter.page < jobsData?.last_page"
            class="btn btn-info btn-sm"
            :to="`/recruiter/dashboard/jobs?page=${nextPageNo}`"
          >
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </div>

          <select
            name="sort"
            v-model="searchFilter.sort"
            id="sort-selector"
            class="select select-sm select-accent outline-never md:ml-auto border-base-200"
            @change="refetchJobs"
          >
            <option value="">Latest</option>
            <option value="oldest">Oldest</option>
          </select>
          <select
            v-model="searchFilter.limit"
            @change="refetchJobs"
            name="display"
            id="sort-selector"
            class="select select-sm select-accent outline-never border-base-200"
          >
            <option
              v-for="option in displayPerPageOptions"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
          <p class="text-xs">per page</p>
        </div>
        <!--  List of jobs -->
        <JobCardsList
          :jobsList="pending ? null : jobsData?.data"
          :isLoading="pending"
          :showPerPage="12"
          :showPagination="false"
          :lastPage="jobsData?.last_page"
        />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});
const route = useRoute();
const pending = ref(false);
const jobsData = ref<any>(null);
const searchFilter = useJobs().searchFilter();

// get route query and override search filter
const queries: any = route.query;
let newFilters: any = {
  ...searchFilter.value,
};
//if query keys have values, override search filter
for (const key in queries) {
  if (queries[key]) {
    newFilters[key] = queries[key];
  }
}

searchFilter.value = newFilters;
if (queries.category) {
  const category = useHelper.jobCategoryLookupByName(queries.category);
  searchFilter.value.roles = category ? category.roles.join(",") : "";
} else {
  searchFilter.value.roles = "";
}
if (!queries.keywords) {
  searchFilter.value.keywords = "";
}

const { data: initialData } = await useJobs().recruiterSearchWithFilter();
jobsData.value = initialData.value;

const displayPerPageOptions = [
  { value: 12, label: "12" },
  { value: 24, label: "24" },
  { value: 36, label: "36" },
];
async function refetchJobs() {
  pending.value = true;
  const { data } = await useJobs().searchWithFilter();
  jobsData.value = data.value;
  pending.value = false;
}

watch(
  () => route.query.page,
  (page) => {
    if (page) {
      searchFilter.value.page = parseInt(page as string);
      refetchJobs();
    } else {
      searchFilter.value.page = 1;
      refetchJobs();
    }
  }
);

watch(
  () => searchFilter.value.limit,
  () => {
    searchFilter.value.page = 1;
    refetchJobs();
  },
)

watch(
  () => searchFilter.value.job_types,
  () => {
    searchFilter.value.page = 1;
    refetchJobs();
  },
)

watch(
  () => searchFilter.value.levels,
  () => {
    searchFilter.value.page = 1;
    refetchJobs();
  },
)


const nextPageNo = computed(() => {
  return searchFilter.value.page + 1;
});
const prevPageNo = computed(() => {
  return searchFilter.value.page - 1;
});
</script>
