<template>
  <div id="recruiter-job-details-page" class="">
    <!-- HEADER -->
    <div
      class="bg-gradient-to-r from-[#E5EBF5]/60 via-[#F5F7FC]/60 to-success/60 py-4 md:py-12"
    >
      <div
        class="md:flex md:item-center md:max-w-7xl md:justify-between md:mx-auto mx-4"
      >
        <div class="flex items-center text-sm justify-center">
          <img
            :src="jobData?.company?.logo_url"
            alt="Company Logo"
            class="w-20 h-20 rounded object-contain"
          />
          <div class="ml-4">
            <p class="font-semibold text-lg">{{ jobData?.title }}</p>
            <div class="md:flex md:items-center md:gap-3 grid text-xs mt-1">
              <p class="text-gray-500">
                <Icon name="heroicons:briefcase" class="w-4 h-4 mr-1" />{{
                  jobData?.role?.name
                }}
              </p>
              <p v-if="jobData?.city !== null" class="text-gray-500">
                <Icon name="heroicons:map-pin" class="w-4 h-4 mr-1" />{{
                  jobData?.city
                }}
              </p>
              <p class="text-gray-500">
                <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />{{
                  getRelatedDate(jobData?.updated_at as string)
                }}
              </p>
              <p class="text-gray-500">
                <Icon name="heroicons:banknotes" class="w-4 h-4 mr-1" />{{
                  jobData?.salary_range
                }}
              </p>
            </div>

            <p
              v-if="jobData?.reward_max !== null"
              class="badge badge-success mt-2"
            >
              RM {{ jobData?.reward_max }} max reward
            </p>
          </div>
        </div>
        <div class="flex justify-center items-center gap-4 mt-4 md:mt-0">
          <nuxt-link :to="jobData?.slug + '/apply'" class="btn btn-primary w-52"
            >Apply for candidate</nuxt-link
          >

          <!-- 5 TEMPLATES BUTTON -->
          <div class="tooltip" data-tip="Click here to copy templates">
            <button
              for="my-modal-4"
              class="btn btn-success"
              @click="showTemplates = true"
            >
              <Icon name="heroicons:document-duplicate" />
            </button>

            <CustomModal
              id="my-modal-4"
              :show="showTemplates"
              @close="showTemplates = false"
              :clickContentClose="true"
              :showCloseButton="false"
            >
              <template #header>
                <div class="grid items-center">
                  <h2 class="text-lg font-semibold">
                    Click a template to copy
                  </h2>
                </div>
              </template>
              <div>
                <input
                  type="text"
                  v-model="candidateName"
                  placeholder="Enter candidate name"
                  class="border w-full p-2 rounded-md"
                />

                <div
                  v-for="template in templates"
                  class="border p-4 mt-4 shadow cursor-pointer"
                  id="template-text"
                  @click="copyTemplate"
                  @close="showTemplates = false"
                >
                  <p class="font-semibold text-center mb-4">
                    {{ template.title }}
                  </p>
                  <p v-html="template.html" class="text-center break-words"></p>
                </div>
              </div>
            </CustomModal>
          </div>

          <!-- SAVE JOB BUTTON -->
          <!-- <button v-if="!isSaved" class="btn btn-success" @click="saveJob">
            <Icon name="heroicons:bookmark" />
          </button>
          <button v-else class="btn btn-danger" @click="unsaveJob">
            <Icon name="heroicons:bookmark-solid" />
          </button> -->
          <button
            class="btn btn-success tooltip"
            data-tip="Click here to copy job URL"
            @click="copyUrl"
          >
            <Icon name="heroicons:link" />
          </button>
        </div>
      </div>
    </div>

    <!-- JOB CONDITIONS & COMPANY PROFILE GRID -->
    <div class="max-w-7xl mx-auto py-12">
      <div class="grid md:grid-cols-[5fr_2fr] gap-6 mb-12 mx-4">
        <div class="border rounded-md p-8 bg-white">
          <div class="mb-12">
            <p class="font-semibold mb-4">Job Conditions:</p>
            <div class="grid md:grid-cols-4 text-sm gap-4">
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon
                    name="game-icons:sands-of-time"
                    class="h-6 w-6 text-primary"
                  />
                </div>

                <div class="ml-2">
                  <p class="font-semibold">Probation Period:</p>
                  <p>
                    {{ jobData?.job_condition?.probation || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon name="fluent-mdl2:group" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Age:</p>
                  <p>
                    {{ jobData?.job_requirement?.age_min }} -
                    {{ jobData?.job_requirement?.age_max }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon
                    name="clarity:avatar-line"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Job Type:</p>
                  <p>{{ jobData?.job_condition?.type || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon
                    name="heroicons:banknotes"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Allowances:</p>
                  <p>
                    {{ jobData?.job_condition?.allowances || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon name="heroicons:clock" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Hours:</p>
                  <p>{{ jobData?.job_condition?.working_hour || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon
                    name="heroicons:language"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Languages:</p>
                  <p class="capitalize break-all">
                    {{ jobData?.job_requirement?.language || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon name="heroicons:sun" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Days:</p>
                  <p>{{ jobData?.job_condition?.working_days || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon name="bi:people" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Malaysia Only:</p>
                  <p>
                    {{
                      jobData?.job_requirement?.is_local_only === 1
                        ? "Yes"
                        : "No"
                    }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon
                    name="heroicons:calendar"
                    class="h-6 w-6 text-primary"
                  />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Annual Leave:</p>
                  <p>
                    {{ jobData?.job_condition?.annual_leave || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6 mr-2">
                  <Icon name="vaadin:coin-piles" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Benefits:</p>
                  <p>
                    {{ jobData?.job_condition?.benefits || "-" }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- COMPANY PROFILE -->
        <div class="bg-info rounded-md p-8">
          <div class="flex items-center">
            <img
              :src="jobData?.company?.logo_url"
              alt="Company Logo"
              class="w-16 h-16 rounded object-contain mr-4"
            />
            <p class="font-semibold">{{ jobData?.company?.name }}</p>
          </div>
          <div class="grid items-center text-sm gap-4 mt-4">
            <div class="flex gap-2 justify-between">
              <p class="font-semibold">Primary Industry:</p>
              <p class="text-end">{{ jobData?.industry?.name }}</p>
            </div>
            <div
              v-if="!isConfidential && jobData?.company?.size"
              class="flex gap-2 justify-between"
            >
              <p class="font-semibold">Company size:</p>
              <p class="text-end">{{ jobData?.company?.size }}</p>
            </div>
            <div
              v-if="
                jobData?.company?.location &&
                jobData?.company?.location !== 'null'
              "
              class="flex justify-between"
            >
              <p class="font-semibold">Location:</p>
              <p class="text-end">{{ jobData?.company?.location }}</p>
            </div>
            <div
              v-if="!isConfidential && jobData?.company?.reg_no"
              class="flex justify-between"
            >
              <p class="font-semibold">Company Registration:</p>
              <p class="text-end">{{ jobData?.company?.reg_no }}</p>
            </div>
          </div>

          <nuxt-link
            :to="isConfidential ? '#' : `/company/${jobData?.company.slug}`"
            class="btn btn-success mt-4 w-full"
            :class="{
              'btn-disabled': isConfidential,
            }"
            >{{
              isConfidential ? "Company Confidential" : "View Company Profile"
            }}</nuxt-link
          >
        </div>
      </div>

      <!-- JOB DESCRIPTION, RESPONSILITIES, ETC -->
      <div class="text-sm mx-4">
        <div class="mb-6">
          <p class="font-semibold mb-4" v-if="jobData?.description">
            Job Description
          </p>
          <p>
            {{ jobData?.description }}
          </p>
        </div>
        <div class="mb-6">
          <p class="font-semibold mb-4">Key Responsibilites</p>
          <ul class="list-disc px-4">
            <li
              v-for="responsibility in jobData?.responsibility_arr"
              class="mb-2"
            >
              {{ responsibility }}
            </li>
          </ul>
        </div>
        <div class="mb-6">
          <p class="font-semibold mb-4">Skills & Experiences</p>
          <ul class="list-disc px-4">
            <li v-for="skills in jobData?.req_must_have_arr" class="mb-2">
              {{ skills }}
            </li>
          </ul>
        </div>

        <!--SHARE BUTTONS-->
        <div
          class="flex flex-wrap gap-2 justify-center md:justify-start items-center max-w-7xl mx-auto text-sm mb-4"
        >
          <p class="font-semibold w-full md:w-fit">Share this job</p>
          <!--BUTTONS-->
          <nuxt-link
            :to="`https://www.facebook.com/sharer/sharer.php?u=${jobData?.url}&title=${jobData?.title}&description=${jobData?.description}`"
            class="flex items-center btn btn-primary md:w-32"
          >
            <Icon name="bxl:facebook" class="w-5 h-5 md:mr-1" />
            <p class="hidden md:block">Facebook</p>
          </nuxt-link>
          <nuxt-link
            :to="`https://twitter.com/intent/tweet?text=${jobData?.title}&url=${jobData?.url}`"
            class="flex items-center btn bg-[#1967D2] text-white md:w-32"
          >
            <Icon name="bxl:twitter" class="w-5 h-5 md:mr-1" />
            <p class="hidden md:block">Twitter</p>
          </nuxt-link>
          <nuxt-link
            :to="`https://www.linkedin.com/sharing/share-offsite/?url=${jobData?.url}`"
            class="flex items-center btn bg-[#D93025] text-white md:w-32"
          >
            <Icon name="bxl:linkedin-square" class="w-5 h-5 md:mr-1" />
            <p class="hidden md:block">LinkedIn</p>
          </nuxt-link>
          <nuxt-link
            :to="`https://api.whatsapp.com/send?text=${jobData?.title}%0D%0A${jobData?.url}%0D%0A${jobData?.description}`"
            class="flex items-center btn bg-[#F9AB00] text-white md:w-32"
          >
            <Icon name="bxl:whatsapp" class="w-5 h-5 md:mr-1" />
            <p class="hidden md:block">WhatsApp</p>
          </nuxt-link>
        </div>
      </div>

      <!-- RELATED JOBS -->
      <div class="mx-4 mt-12" id="related-jobs">
        <h3 class="font-semibold text-center mb-4">Related Jobs</h3>

        <div class="grid md:grid-cols-4 gap-4">
          <JobCard
            v-if="relatedJobs?.length > 0"
            v-for="job in relatedJobs"
            :job="job"
          />
          <div
            v-else
            class="border rounded-lg w-max p-7 mx-auto md:col-start-2 md:col-span-2"
          >
            No related jobs at the moment
          </div>
        </div>
      </div>
    </div>

    <DevOnly>
      <!-- <pre>{{ jobData }}</pre> -->
      <!-- <pre>{{ relatedJobs }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";

definePageMeta({
  middleware: "recruiter-auth",
});

const route = useRoute();
const { data: jobData } = await useJobs().getBySlug(
  route.params.recruiter_job_slug as string
);

const { data: recruiterData } = useRecruiter().getProfile();

const { data: relatedJobs } = useJobs().getRelatedJobs(
  jobData?.value?.id as number
);
const getRelatedDate = useHelper.getRelatedDate;

const save = useRecruiter().saveJob;
const unsave = useRecruiter().unsaveJob;

const showTemplates = ref(false);
onMounted(() => {
  //append uid query to the current route
  navigateTo({ query: { uid: recruiterData?.value?.id } });
});

async function saveJob() {
  await save(jobData.value?.id as number);
  await useRecruiter().getProfile();
}
async function unsaveJob() {
  await unsave(jobData.value?.id as number);
  await useRecruiter().getProfile();
}
const isSaved = computed(() => {
  return recruiterData?.value?.fav_job_ids?.some(
    (id: number) => id == jobData?.value?.id
  );
});

// if company name has confidential word
const isConfidential = computed(() => {
  return jobData?.value?.company.name.toLowerCase().includes("confidential");
});

// 5 templates function
const candidateName = ref(null);
const referralLink = `${jobData?.value?.url}?uid=${recruiterData?.value?.id}`;

const templates = computed(() => {
  const name = candidateName.value || "{Candidate Name}";

  return [
    {
      num: 1,
      title: "Connection Request",
      text: `Dear ${name},\n\nGood Day! Hope you are well.\n\n${recruiterData?.value?.name}, recruitment specialist here from Seekers.\n\nI have a client, they are hiring ${jobData?.value?.title}.\n\nWonder whether you are open to new job opportunities?\n\nThanks!\n${recruiterData?.value?.name}`,
      html: `<p class="mb-2">Dear ${name},</p><p class="mb-2">Good Day! Hope you are well.</p><p class="mb-2">${recruiterData?.value?.name}, recruitment specialist here from Seekers.</p><p class="mb-2">I have a client, they are hiring ${jobData?.value?.title}.</p><p class="mb-2">Wonder whether you are open to new job opportunities?</p><p>Thanks!</p><p>${recruiterData?.value?.name}</p>`,
    },
    {
      num: 2,
      title: "Thank you & Greeting",
      text: `Dear ${name},\n\nThank you for connecting.\n\nMay i ask whether you are open to new job opportunities?\n\nThanks!\n${recruiterData?.value?.name}`,
      html: `<p class="mb-2">Dear ${name},<p><p class="mb-2">Thank you for connecting.</p><p class="mb-2">May i ask whether you are open to new job opportunities?</p><p class="mb-2">Thanks!</p><p>${recruiterData?.value?.name}</p>`,
    },
    {
      num: 3,
      title: "Position Introduction",
      text: `Dear ${name},\n\nThanks for showing interest.\n\nHere's the full job description.\n${referralLink}\n\nDo ask me any question about this job.\n\nKindly read through it slowly.\n\nThanks!\n${recruiterData?.value?.name}`,
      html: `<p class="mb-2">Dear ${name},</p><p class="mb-2">Thanks for showing interest.</p><p class="mb-2">Here's the full job description.</p><p class="mb-2"><a href="${referralLink}">${referralLink}</a></p><p class="mb-2">Do ask me any question about this job.</p><p class="mb-2">Kindly read through it slowly.</p><p class="mb-2">Thanks!</p><p class="mb-2">${recruiterData?.value?.name}</p>`,
    },
    {
      num: 4,
      title: "Build Higher Interest (engagement)",
      text: `Dear ${name},\n\nCan you share with me your strength and achievements.\n\nWhat can you contribute to the company?\n\nThanks!\n${recruiterData?.value?.name}`,
      html: `<p class="mb-2">Dear ${name},</p><p class="mb-2">Can you share with me your strength and achievements.</p><p class="mb-2">What can you contribute to the company?</p><p class="mb-2">Thanks!</p><p class="mb-2">${recruiterData?.value?.name}</p>`,
    },
    {
      num: 5,
      title: "Ask Salary & Notice Period",
      text: `Dear ${name},\n\nThe job monthly salary is RM${jobData?.value?.salary_min} - RM${jobData?.value?.salary_max}.\n\nMay i know your current and expected salary and notice period?\n\nKindly email me your latest resume to ${recruiterData?.value?.email}.\n\nThanks!\n${recruiterData?.value?.name}`,
      html: `<p class="mb-2">Dear ${name},</p><p class="mb-2">The job monthly salary is RM${jobData?.value?.salary_min} - RM${jobData?.value?.salary_max}.</p><p class="mb-2">May i know your current and expected salary and notice period?</p><p class="mb-2">Kindly email me your latest resume to ${recruiterData?.value?.email}.</p><p class="mb-2">Thanks!</p><p class="mb-2">${recruiterData?.value?.name}</p>`,
    },
  ];
});

function copyTemplate() {
  let text = document.querySelector("#template-text");
  let copy = text?.textContent;
  navigator.clipboard.writeText(`${copy}`).then(
    (e) => {
      Swal.fire({ title: "Template copied!", icon: "success" });
    },
    (e) => {
      Swal.fire({ title: "Fail to copy template!", icon: "error" });
    }
  );
}

function copyUrl() {
  navigator.clipboard.writeText(referralLink).then(
    (e) => {
      Swal.fire({ title: "Link copied!", icon: "success" });
    },
    (e) => {
      Swal.fire({ title: "Fail to copy link!", icon: "error" });
    }
  );
}
</script>
