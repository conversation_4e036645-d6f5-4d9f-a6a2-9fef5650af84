<template>
  <div class="p-8 lg:p-12" :class="{ 'animate-pulse': pendingJob }">
    <div class="text-center">
      <h1 class="font-semibold">Job Application</h1>
      <div class="divider max-w-2xl mx-auto"></div>
      <img
        :src="jobData?.company.logo_url"
        class="mx-auto border rounded-full w-20 h-20 md:w-32 md:h-32 object-contain mt-4"
      />
      <p class="-mb-1 mt-4">{{ jobData?.company?.name || "--------" }}</p>
      <h3 class="font-semibold">{{ jobData?.title || "--------" }}</h3>
      <p class="text-sm text-accent mt-6">
        Please ensure your candidate info is correct and fully filled.
      </p>
      <div class="divider max-w-2xl mx-auto"></div>

      <!-- if candidate not picked, list candidate or add new -->
      <div
        class="flex flex-col gap-2 max-w-3xl mx-auto min-h-[50vh]"
        v-if="!pickedCandidate"
      >
        <h3>Pick a Candidate</h3>
        <div class="grid grid-cols-2 gap-2">
          <button
            class="btn"
            :class="{ 'btn-primary': !addMode }"
            @click="addMode = false"
            :disabled="pendingCandidates || candidateData?.data.length === 0"
          >
            Existing
          </button>
          <button
            class="btn"
            :class="{ 'btn-primary': addMode }"
            @click="addMode = true"
          >
            Add New
          </button>
        </div>
        <div v-if="addMode">
          <RecruiterDashboardCandidateForm
            v-if="addMode"
            @submit="handleAddedNewCandiate"
            :candidate="{}"
            mode="add"
            :recruiterId="recruiterData?.id"
            class="-m-8 mt-0"
          />
        </div>

        <RecruiterDashboardCandidateCard
          v-if="!addMode"
          v-for="candidate in candidateData?.data"
          :key="candidate.id"
          :candidate="candidate"
          @click="handlePickCandidate(candidate)"
          :navigation="false"
          :class="{
            'border-primary border-2': pickedCandidate?.id === candidate.id,
          }"
        />
      </div>

      <!-- Once Candidate is picked, Show confirmation -->

      <div
        v-if="pickedCandidate"
        class="max-w-2xl mx-auto flex flex-col gap-4 py-3"
      >
        <div class="">
          <h3>Picked Candidate</h3>
        </div>
        <RecruiterDashboardCandidateCard
          :candidate="pickedCandidate"
          class="border-primary border-2"
          :navigation="false"
        />

        <div class="text-left">
          <FormKit
            type="form"
            ref="candidateApplicationForm"
            :actions="false"
            :form-class="'grid gap-4 lg:gap-8'"
            @submit="submitExisting"
          >
            <!-- <FormKit
              type="number"
              name="salary_expected"
              label="Expected Salary (RM)"
              validation="required"
              :value="
                pickedCandidate?.expected_salary === 0
                  ? null
                  : pickedCandidate?.expected_salary
              "
              placeholder="5000"
            />
            <FormKit
              type="number"
              name="salary_current"
              label="Current Salary (RM)"
              validation="required"
              :value="
                pickedCandidate?.current_salary === 0
                  ? null
                  : pickedCandidate?.current_salary
              "
              placeholder="3000"
            />

            <FormKit
              type="select"
              name="notice_period"
              label="Notice Period"
              validation="required"
              :value="'2 weeks'"
              :validation-messages="{
                length: 'Must pick a notice period',
              }"
            >
              <option v-for="period in noticePeriodOptions" :value="period">
                {{ period }}
              </option>
            </FormKit> -->
          </FormKit>
        </div>

        <div class="grid grid-cols-[1fr_4fr] gap-4 mt-8 mb-12">
          <button class="btn" @click="pickedCandidate = null">
            <Icon name="heroicons:chevron-left" /> Back
          </button>
          <button class="btn btn-primary" @click="handleSubmitButton">
            Submit
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  middleware: "recruiter-auth",
});
const baseUrl = useRuntimeConfig().public.baseUrl;
const route = useRoute();
const addMode = ref(false);
const pickedCandidate = ref<any>(null);
const candidateApplicationForm = ref<any>(null);
const recruitertoken = useRecruiter().token;

const { data: noticePeriodOptions } = useHelper.noticePeriods();

const { data: jobData, pending: pendingJob } = useJobs().getBySlug(
  route.params.recruiter_job_slug as string
);
const { data: recruiterData, pending: pendingRecruiter } =
  useRecruiter().getProfile();
const {
  data: candidateData,
  pending: pendingCandidates,
  refresh: refreshCandidate,
} = useRecruiter().getCandidates();

const handlePickCandidate = (candidate: any) => {
  candidate.profile_completed
    ? (pickedCandidate.value = candidate)
    : navigateTo("/recruiter/dashboard/candidates/" + candidate.id);
  useHelper.scrollToTop();
};

const handleSubmitButton = () => {
  const node = candidateApplicationForm.value.node;
  node.submit();
};

const handleAddedNewCandiate = async (newCandidateForm: any) => {
  if (jobData.value) {
    const finalForm = {
      candidate_id: newCandidateForm.candidate_id,
      job_id: jobData.value.id,
      user_id: recruiterData.value.id,
      salary_current: newCandidateForm.current_salary,
      salary_expected: newCandidateForm.expected_salary,
      notice_period: newCandidateForm.notice_period,
    };

    const res = await (
      await fetch(baseUrl + "/applicants", {
        method: "POST",
        body: useHelper.JSONToFormData(finalForm),
        headers: {
          authorization: "Bearer " + recruitertoken.value,
        },
      })
    ).json();

    if (!res.id) {
      useHelper.errorToast(res.error);
      pickedCandidate.value = null;
      useHelper.scrollToTop();
    } else {
      useHelper.successToast("Candidate Application Submitted");
      navigateTo(`/recruiter/dashboard/jobs/${jobData.value.slug}/thankyou`);
    }
  }
};

const submitExisting = async (form: any) => {
  if (jobData.value && pickedCandidate.value && recruiterData.value) {
    const finalForm = {
      candidate_id: pickedCandidate.value.id,
      job_id: jobData.value.id,
      user_id: recruiterData.value.id,
      salary_current: pickedCandidate.value.current_salary,
      salary_expected: pickedCandidate.value.expected_salary,
      notice_period: pickedCandidate.value.notice_period,
    };

    const res = await (
      await fetch(baseUrl + "/applicants", {
        method: "POST",
        body: useHelper.JSONToFormData(finalForm),
        headers: {
          authorization: "Bearer " + recruitertoken.value,
        },
      })
    ).json();

    if (!res.id) {
      useHelper.errorToast(res.error);
      pickedCandidate.value = null;
      useHelper.scrollToTop();
    } else {
      res.status = res.success;
      useHelper.successToast("Candidate Application Submitted");
      navigateTo(`/recruiter/dashboard/jobs/${jobData.value.slug}/thankyou`);
    }
  }
};
</script>
