<template>
  <div
    class="text-center flex flex-col md:gap-12 justify-around items-center p-12 md:py-24 max-w-7xl md:mx-auto"
  >
    <div class="font-semibold">
      <h1 class="text-5xl">
        <span class="text-primary">Thank you</span> for your application for the
        <span class="text-primary">{{ jobData?.title }}</span> role.
      </h1>
      <div class="text-xl mt-8">
        <p>
          Your application has been received and is currently being reviewed.
        </p>
        <p>
          If your candidate is selected for the next stage, you can check the
          status of your submission in your recruitment dashboard.
        </p>
        <p>
          Thank you again for your submission and we wish you all the best in
          your recruitment efforts!
        </p>
      </div>
    </div>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      width="291.5"
      height="323.5"
      viewBox="0 0 583.04004 647.03484"
    >
      <path
        d="M466.21802,256.62591v30.31073c-3.77618,7.30487-7.48096,16.79834-11.10742,28.6875,0,0,5.55591,18.55005,11.10742,40.96423v64.10986h-138.66492l-9.63177,13.66199h-26.40131v60.05011h43.04559v138.21759h8.94214v-138.21759h25.27795v138.21759h8.9422v-138.21759h87.63342v138.21759h8.94214v-138.21759h33.98022v138.21759h8.94214l-.35744-138.21759h9.3998v-237.78442h-60.05017Z"
        fill="#ccc"
      />
      <g>
        <path
          d="M150.784,542.20215c-.8443-1.42365-4.49683,.10071-5.16833-1.19135-.66913-1.28735,2.72449-3.24884,4.66333-7.7298,.34967-.80804,2.55298-5.90033,.92188-7.47162-3.09174-2.97833-17.23962,9.31256-22.19073,5.29041-1.08661-.88269-1.8457-2.64484-5.13428-5.50586-1.30829-1.13812-2.09937-1.65204-2.86499-1.46088-1.08636,.27124-1.17242,1.72522-2.33734,4.58398-1.74701,4.28729-2.78027,4.02838-3.79816,7.44897-.75562,2.53925-.58301,4.0155-1.47217,4.3031-1.25824,.40698-2.41638-2.28632-4.01099-2.04517-1.6272,.24609-2.61096,3.38208-2.92169,5.72998-.58313,4.40607,1.03497,7.31348,1.93182,10.37067,.97461,3.32239,1.55304,8.37494-.69769,15.52759l-22.27719,62.76611c4.91812-12.87946,19.15842-48.18536,25.14218-61.3053,1.72742-3.78748,3.6347-7.62909,7.55096-9.14526,3.77045-1.45978,8.94867-.58826,15.55029-2.17865,.771-.18573,2.91052-.73004,3.17981-1.98846,.22266-1.04034-1.03583-1.62109-.84247-2.59552,.25934-1.30701,2.73413-1.32306,5.77533-2.83093,2.14447-1.06329,3.48047-2.32782,4.62933-3.41528,.34637-.32782,5.49512-5.26166,4.37115-7.1568h-.00006l.00002,.00006Z"
          fill="#f2f2f2"
        />
        <path
          d="M63.13889,575.24487c-.94235,.0036-1.24805,2.23608-2.07593,2.19287-.82492-.04303-.80847-2.2746-2.4483-4.5191-.29572-.40472-2.1593-2.95544-3.40115-2.60828-2.35385,.65808-.4035,11.14825-3.80618,12.41785-.74673,.27863-1.83066,.14276-4.18369,.93134-.9361,.31372-1.41656,.55371-1.54364,.98468-.1803,.61145,.50845,1.07324,1.57544,2.46979,1.60016,2.09442,1.17496,2.52692,2.56009,4.01343,1.02826,1.10352,1.80264,1.44476,1.68723,1.96411-.16327,.73499-1.81934,.52631-2.16107,1.37854-.34869,.86969,.90659,2.25726,1.9693,3.08728,1.99429,1.5575,3.88812,1.60217,5.64737,2.04413,1.91183,.48029,4.55859,1.65424,7.41974,4.82263l24.37836,29.04382c-4.90228-6.12994-18.12189-23.30609-22.83472-30.0285-1.3605-1.94061-2.69568-3.98517-2.30984-6.34479,.37143-2.27179,2.29327-4.56177,3.41748-8.26074,.13132-.43201,.48145-1.63916-.05847-2.13446-.44638-.40942-1.09451,.04071-1.51697-.33539-.56665-.50446,.13953-1.72369,.27689-3.65143,.09686-1.35931-.13831-2.37988-.34055-3.25757-.06097-.26459-.99695-4.21521-2.25137-4.21039v.00006s-.00002,.00012-.00002,.00012Z"
          fill="#f2f2f2"
        />
        <path
          d="M81.93851,579.46496l-.56775-5.06818,.2594-.38422c1.20035-1.77667,1.81216-3.51178,1.81949-5.15869,.00122-.26184-.01102-.52368-.02325-.79047-.04895-1.05719-.11011-2.37134,.5751-3.90332,.38422-.85406,1.46344-2.83142,3.08594-2.58667,.43683,.06122,.76721,.26184,1.01804,.49677,.03671-.05872,.07465-.11743,.11502-.18109,.50534-.77087,.90546-1.10126,1.29089-1.41937,.29611-.24469,.60202-.49677,1.08167-1.09387,.21045-.26184,.37442-.49188,.5127-.68518,.41971-.5849,.96786-1.28235,1.93451-1.27008,1.03271,.04895,1.57355,.90302,1.93207,1.46832,.63995,1.00824,.92993,1.73511,1.12204,2.21716,.06976,.17621,.14929,.37439,.18967,.43561,.33282,.48944,3.03824,.0318,4.05746-.13458,2.28937-.38177,4.27039-.71216,5.08655,.73169,.5849,1.03271,.15051,2.39825-1.33008,4.1676-.4613,.55066-.95197,.96912-1.38757,1.29706,.36096,.21533,.68396,.54572,.81494,1.06451h0c.30835,1.22852-.74762,2.45941-3.13733,3.66345-.59344,.30096-1.39246,.70233-2.51205,.89569-.52737,.09058-1.00214,.11261-1.40836,.12482-.00858,.23492-.06363,.48944-.20679,.75128-.41971,.77087-1.29333,1.13794-2.60995,1.04987-1.44876-.07587-2.64175-.35242-3.69406-.59467-.91891-.21045-1.7106-.3891-2.33218-.34747-1.15262,.09302-2.04099,1.01556-3.08347,2.21228l-2.6026,3.04187h-.00003l-.00002-.00012Z"
          fill="#f2f2f2"
        />
        <path
          d="M84.5722,549.289l-4.75842,1.83472-.43744-.15344c-2.02304-.71045-3.85654-.86212-5.45151-.45148-.2536,.06525-.50378,.14349-.7587,.22296-1.01019,.31549-2.26587,.70801-3.92151,.43378-.92361-.15503-3.11002-.69739-3.28485-2.32892-.05161-.43805,.05869-.80853,.22232-1.11078-.06613-.02063-.13257-.04242-.20435-.06531-.87384-.29327-1.29489-.59656-1.70041-.88867-.31183-.22437-.63324-.45636-1.33249-.76886-.30667-.13715-.57077-.23743-.79285-.32208-.67221-.25763-1.4859-.61096-1.71924-1.54907-.2146-1.01135,.47437-1.75116,.93024-2.24133,.81296-.87476,1.44244-1.3396,1.86005-1.64777,.15274-.11212,.32431-.23938,.37326-.29395,.38901-.44611-.73987-2.94696-1.15936-3.89062-.94998-2.11768-1.77203-3.95007-.5824-5.10577,.85059-.8277,2.28165-.75391,4.36868,.22943,.64963,.30658,1.17886,.67505,1.60657,1.01318,.11676-.40381,.35437-.80005,.82303-1.05829h0c1.11011-.60986,2.56863,.0993,4.33945,2.10547,.44168,.49768,1.03259,1.16876,1.50357,2.2027,.22134,.48712,.36307,.9408,.47797,1.33069,.22943-.05127,.48956-.06256,.77917,.00946,.85211,.21045,1.4288,.9624,1.67752,2.2583,.2941,1.42059,.32922,2.64471,.36179,3.72406,.02951,.94226,.0575,1.75336,.25543,2.34406,.38232,1.09137,1.50006,1.71661,2.92206,2.42151l3.60254,1.74591h-.00012s0,.00012,0,.00012Z"
          fill="#f2f2f2"
        />
        <path
          d="M81.93851,534.35803l-.56775-5.06818,.2594-.38422c1.20035-1.77667,1.81216-3.51178,1.81949-5.15869,.00122-.26184-.01102-.52368-.02325-.79047-.04895-1.05719-.11011-2.37134,.5751-3.90332,.38422-.85406,1.46344-2.83142,3.08594-2.58667,.43683,.06122,.76721,.26184,1.01804,.49677,.03671-.05872,.07465-.11743,.11502-.18109,.50534-.77087,.90546-1.10126,1.29089-1.41937,.29611-.24469,.60202-.49677,1.08167-1.09387,.21045-.26184,.37442-.49188,.5127-.68518,.41971-.5849,.96786-1.28235,1.93451-1.27008,1.03271,.04895,1.57355,.90302,1.93207,1.46832,.63995,1.00824,.92993,1.73511,1.12204,2.21716,.06976,.17621,.14929,.37439,.18967,.43561,.33282,.48944,3.03824,.0318,4.05746-.13458,2.28937-.38177,4.27039-.71216,5.08655,.73169,.5849,1.03271,.15051,2.39825-1.33008,4.1676-.4613,.55066-.95197,.96912-1.38757,1.29706,.36096,.21533,.68396,.54572,.81494,1.06451h0c.30835,1.22852-.74762,2.45941-3.13733,3.66345-.59344,.30096-1.39246,.70233-2.51205,.89569-.52737,.09058-1.00214,.11261-1.40836,.12482-.00858,.23492-.06363,.48944-.20679,.75128-.41971,.77087-1.29333,1.13794-2.60995,1.04987-1.44876-.07587-2.64175-.35242-3.69406-.59467-.91891-.21045-1.7106-.3891-2.33218-.34747-1.15262,.09302-2.04099,1.01556-3.08347,2.21228l-2.6026,3.04187h-.00003l-.00002-.00012Z"
          fill="#f2f2f2"
        />
        <path
          d="M87.12415,632.73578l-1.0621-.66565-.25696-1.22601,.25696,1.22601-1.23828,.14197c-.01959-.11499-.08688-.37933-.18842-.79291-.55429-2.26611-2.24408-9.16235-3.65369-20.02307-.9838-7.58142-1.54053-15.37579-1.65555-23.17017-.11502-7.80658,.25452-13.71661,.55063-18.46661,.22391-3.5827,.49557-6.99408,.7623-10.31744,.70969-8.87848,1.379-17.26508,.88098-26.54974-.11011-2.07275-.3414-6.38721-2.862-10.97821-1.46222-2.6626-3.47626-5.02655-5.98709-7.0235l1.56131-1.96265c2.77145,2.2074,4.99963,4.82593,6.62338,7.78217,2.79471,5.09021,3.04678,9.79126,3.16791,12.05005,.50656,9.44867-.17007,17.91602-.88712,26.88013-.26431,3.3111-.53595,6.70776-.75864,10.27582-.29367,4.70599-.65952,10.56458-.54572,18.27081,.11258,7.70135,.66318,15.40027,1.63351,22.88629,1.39246,10.72363,3.0578,17.51715,3.60474,19.74896,.29123,1.19177,.35239,1.44141,.05383,1.91376h.00003Z"
          fill="#f2f2f2"
        />
        <path
          d="M68.07507,518.87213c-.10278,0-.20679-.00244-.31201-.00983-2.13397-.11499-4.11008-1.39246-5.87451-3.79559-.82715-1.13062-1.25052-2.42029-2.09482-4.99231-.13092-.39645-.76721-2.40805-1.15387-5.17584-.2533-1.8085-.22147-2.56467,.13705-3.23276,.39767-.74396,1.04129-1.26276,1.76566-1.6127-.0318-.23984-.00613-.487,.0979-.73907,.42703-1.04495,1.56253-.9079,2.17679-.84183,.31079,.03915,.69867,.09055,1.11716,.06851,.65829-.0318,1.01193-.2276,1.54785-.52127,.5127-.28143,1.15018-.63138,2.09604-.82471,1.86478-.3891,3.42731,.1395,3.94244,.31323,2.71027,.90302,4.04645,3.01007,5.59308,5.44992,.30835,.48944,1.36676,2.271,2.06543,4.71332,.50412,1.76199,.43317,2.5451,.28143,3.12753-.30835,1.19669-1.03638,1.88193-2.88525,3.39432-1.93085,1.58331-2.89993,2.37622-3.73199,2.85834-1.93695,1.11841-3.15323,1.82074-4.76837,1.82074h0Z"
          fill="#f2f2f2"
        />
      </g>
      <path
        d="M583.04004,632.59631h0c0,.46393-.3761,.84003-.84003,.84003H0v-1.68005H582.20001c.46393,0,.84003,.3761,.84003,.84003Z"
        fill="#e6e6e6"
      />
      <g>
        <g>
          <path
            id="uuid-ab5b4137-23bf-44b0-b8fd-0caecc79ac33-259"
            d="M262.56619,182.27698c-5.91843-6.91496-14.28372-9.46635-18.68384-5.69913-4.40012,3.76726-3.16846,12.42532,2.75301,19.34196,2.32748,2.79536,5.29054,4.99269,8.64133,6.40813l25.44661,28.9823,13.34601-12.23125-26.50278-27.27284c-.88184-3.53017-2.59647-6.79774-5.00034-9.52917Z"
            fill="#ffb6b6"
          />
          <path
            d="M453.56561,217.61941s5.56567,25.47594-16.52527,33.59933c-22.09097,8.12341-79.33722,39.31415-83.57477,37.88477-4.23755-1.42941-18.10077,5.54865-34.46301-9.33755-16.36227-14.8862-55.89194-59.76595-55.89194-59.76595l16.25995-22.08293,61.21866,42.29051s1.30933-.38853,7.11252-.9901,1.26456,3.05916,10.3418-4.2623,67.01291-33.55998,67.01291-33.55998c0,0,17.00082-8.42445,28.50922,16.22421l-.00006-.00003v.00002Z"
            fill="#2f2e41"
          />
        </g>
        <path
          d="M309.11057,573l9,29-25,17-18-16s7-25,4-31l30,1Z"
          fill="#ffb6b6"
        />
        <polygon
          points="436.11057 162.00001 450.44916 193.62991 408.4964 219.70866 406.11057 182.00001 436.11057 162.00001"
          fill="#ffb6b6"
        />
        <polygon
          points="436.11057 162.00001 450.44916 193.62991 408.4964 219.70866 406.11057 182.00001 436.11057 162.00001"
          isolation="isolate"
          opacity=".1"
        />
        <circle cx="419.62256" cy="159.73379" r="30.46439" fill="#ffb6b6" />
        <path
          d="M408.11057,213.00001l43-29-7,244.99998s-35.5-61.5-62.5-64.5c0,0,3-7,5-11s5-24,5-24l14-108,2.5-8.5v.00002Z"
          fill="#34a853"
        />
        <path
          d="M442.61057,466.5l-38-134s6-108,46.5-148.5l9.5,14.5s9.39371,1.39371,7.69684,7.69685c-1.69684,6.30315,37.30316,238.30315,37.30316,238.30315h-40.42126s-3.57874,22-22.57874,22Z"
          fill="#2f2e41"
        />
        <path
          d="M408.11057,213.00001v-4s-18.5,1.5-19.5,12.5-9,53.99998-1,79.99998l-10,16-7,38,22,6,12-92.27954s8-48.72047,3.5-56.22047v.00003Z"
          fill="#2f2e41"
        />
        <path
          d="M443.61057,403.5s17,49-19,50-92.08267,1-92.08267,1l-27.91733,15,8,109-56,1s-18-134-2-150,62-33,62-33l53-27,14.90552-18.09448,59.09448,52.09448Z"
          fill="#2f2e41"
        />
        <path
          d="M445.65085,177.50001c2.12445-.75476,22.00967-24.06042,5.45972-39.5,0,0-6.44891-18.86123-32.16742-16.88288s-32.64273,16.04487-32.64273,23.35462,6.81012,15.52827,6.81012,15.52827c0,0-4.83179-20.08859,6.04913-19.09941,10.88092,.98917,7.91339,4.94588,15.82678,2.96751,7.91339-1.97835,15.82678,.98917,15.82678,.98917l7.91339,2.96751v12.0687s3.45041-2.76891,3.70355,0,3.22067,17.60651,3.22067,17.60651Z"
          fill="#2f2e41"
        />
        <path
          d="M273.61057,424.5l-1,31-39-7s16.74556-27.55905,14.37277-35.27954l25.62723,11.27954Z"
          fill="#ffb6b6"
        />
        <path
          d="M269.61057,448.5s7,0,7,4,11,35-10,36-20-3-20-3c0,0-59,27-80-13,0,0-5-10,7-12s60-18.74408,60-18.74408c0,0,28,10.74408,36,6.74408Z"
          fill="#2f2e41"
        />
        <path
          d="M310.11057,602s7-8.48029,8-7.74017c1,.74017,23,25.74017,8,32.74017s-23,10-23,10l-4-6s-30,29-76,9c0,0-4-9,5-11s47-32.47241,47-32.47241c0,0,16,11.47241,24,9.47241s11-4,11-4Z"
          fill="#2f2e41"
        />
        <path
          d="M381.61057,361.5s-93.5-47.5-101.5-47.5-23.29135,3.29135-26.64568,18.64566c-3.35432,15.35434-23.35432,89.35434-23.35432,89.35434l49,12,19-53,31,28,52.5-31.3071v-16.1929Z"
          fill="#2f2e41"
        />
        <g>
          <path
            id="uuid-9122ce6e-50cf-4901-97fd-d5a56bf7c6b1-260"
            d="M337.58374,343.15024c-9.0054,1.32175-15.62305,7.0397-14.78094,12.77066s8.82422,9.3038,17.83267,7.98038c3.60577-.47931,7.04431-1.81512,10.02795-3.89575l38.08557-6.08215-3.23514-17.81161-37.20218,7.88696c-3.45691-1.13553-7.13553-1.42648-10.72794-.84848Z"
            fill="#ffb6b6"
          />
          <path
            d="M469.2941,200.38386s24.52185,8.87013,19.62006,31.89127-9.02484,88.08266-12.49295,90.90614-4.9534,18.27261-26.27011,24.18204c-21.31668,5.90942-82.48718,32.06607-82.48718,32.06607l-7.88998-42.16077,68.41299-29.25534s.36917-1.31491,2.9534-6.54565,3.26239,.56064,1.9061-11.02213c-1.35632-11.58276,7.32431-74.58789,7.32431-74.58789,0,0,1.9339-18.87485,28.9234-15.47379l-.00003,.00005Z"
            fill="#2f2e41"
          />
        </g>
        <path
          d="M247.86203,13.93603h0c-.1235,.12004-.2374,.24633-.35956,.36737-19.07095-18.92456-49.78458-19.09105-69.05963-.37452h0c-19.34363,18.79034-19.99731,49.63684-1.46703,69.22981l-.01205,.01172,.4614,.47513h0l68.23756,70.25912,70.73437-68.69908-.19675-.20245c.1188-.11323,.24252-.21819,.36044-.33298h0c19.53278-18.97068,19.98837-50.18376,1.0177-69.71655h0c-18.97067-19.53268-50.18387-19.98826-69.71643-1.01759,0,0-.00003,.00002-.00003,.00002Z"
          fill="#34a853"
        />
      </g>
    </svg>
    <div class="flex flex-col md:flex-row gap-4 justify-center mt-4">
      <nuxt-link
        :to="`/recruiter/dashboard/jobs/${jobData?.slug}`"
        class="btn px-12"
        >Back</nuxt-link
      >
      <nuxt-link to="/recruiter/dashboard" class="btn px-12"
        >Go to Dashboard</nuxt-link
      >
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: "recruiter-auth",
});

const route = useRoute();
const { data: jobData, pending: pendingJob } = useJobs().getBySlug(
  route.params.recruiter_job_slug as string
);
</script>
