<template>
  <div id="recruiter-dashboard-page" class="grid gap-8 px-3 md:px-0">
    <RecruiterDashboardStatsOverview
      :profileData="profileData"
      :totalApplications="profileData?.dashboard_overview?.total_applicants"
      :totalJobs="latestJobs?.total * latestJobs?.per_page"
      :name="profileData?.name"
      :totalWallet="totalWalletIn ? totalWalletIn : 0"
    />

    <div
      class="grid lg:grid-cols-[5fr_2fr] gap-4 items-stretch lg:max-h-[60em] xl:max-h-[38em]"
    >
      <RecruiterDashboardJobsList
        title="Recommended Jobs"
        :jobs="recommendedJobs?.data"
        :limit="4"
        class="h-full order-2 lg:order-1"
      />

      <RecruiterDashboardNotifications
        :data="notificationsData?.data"
        class="h-full order-1 lg:order-2 overflow-y-auto"
      />
    </div>

    <RecruiterDashboardJobsList
      title="Digital / Technical Jobs"
      :jobs="latestJobs?.data"
      :limit="4"
    />

    <RecruiterDashboardMyApplicant />
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});

const baseUrl = useRuntimeConfig().public.baseUrl;
const token = useRecruiter().token;

const { data: profileData } = await useRecruiter().getProfile();
const { data: latestJobs } = useJobs().recruiterLatestJobs();
const { data: recommendedJobs } = useRecruiter().recommendedJobs();

const { data: notificationsData } = useFetch<any>(baseUrl + "/notifications", {
  headers: { Authorization: "Bearer " + token.value },
});

const { data: walletIn, pending } = useFetch<any>(baseUrl + "/wallet_in", {
  headers: { Authorization: "Bearer " + token.value },
});

const totalWalletIn = computed(() => {
  return walletIn.value?.reduce(
    (acc: any, wallet: any) => acc + wallet.amount,
    0
  );
});
</script>
