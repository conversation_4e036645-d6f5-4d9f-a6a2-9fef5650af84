<template>
  <div
    id="candidate-login-page"
    class="grid grid-cols-1 lg:grid-cols-3 w-screen"
  >
    <div class="relative w-full">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463401/website/candidate/registration-bg_hpglmp.png"
        alt="login-bg"
        class="h-20 lg:h-screen w-full object-left-top object-cover"
        style="filter: hue-rotate(131deg) brightness(0.8) saturate(1.5)"
      />
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463402/website/candidate/registration-vector_va9uqh.svg"
        alt=""
        class="hidden lg:block lg:absolute bottom-28 left-0 object-cover"
      />
    </div>
    <div class="col-span-2 p-8 py-28">
      <div
        id="forgot-password"
        class="max-w-xl mx-auto flex flex-col gap-8 justify-center"
      >
        <h1>Forgot Password</h1>
        <div v-if="!resetSuccess">
          <p class="mb-2 text-sm">Email</p>
          <label for="email" class="sr-only">Email</label>

          <div class="relative">
            <input
              v-model="email"
              type="email"
              class="w-full rounded-lg border border-gray-200 p-4 pr-12 text-sm shadow-sm focus:outline-primary"
              placeholder="Enter email"
              v-on:keyup.enter="resetPassword"
            />

            <div class="absolute inset-y-0 right-4 inline-flex items-center">
              <Icon name="heroicons:at-symbol" class="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        <p v-else>Please check your email for a link reset password</p>

        <div class="w-full">
          <button
            v-if="!resetSuccess"
            @click="resetPassword"
            class="btn btn-primary text-sm w-full"
            :disabled="email.length < 10"
          >
            Reset Password
          </button>

          <button
            v-else
            @click="navigateTo('/recruiter/login')"
            class="btn btn-primary text-sm w-full"
            :disabled="email.length < 10"
          >
            Back to Login
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const resetSuccess = useState(() => false);
const email = ref("");
async function resetPassword() {
  const res = await useRecruiter().resetPassword(email.value);
  if (res.success) {
    resetSuccess.value = true;
  }
}
</script>
