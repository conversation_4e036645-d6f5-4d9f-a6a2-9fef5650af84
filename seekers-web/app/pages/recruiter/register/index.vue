<template>
  <div
    id="recruiter-register-page"
    class="grid grid-cols-1 lg:grid-cols-3 w-full"
  >
    <div class="relative w-full">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463401/website/candidate/registration-bg_hpglmp.png"
        alt="register-bg"
        class="h-20 lg:h-screen w-full object-left-top object-cover"
        style="filter: hue-rotate(131deg) brightness(0.8) saturate(1.5)"
      />
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463402/website/candidate/registration-vector_va9uqh.svg"
        alt=""
        class="hidden lg:block lg:absolute bottom-28 left-0 object-cover"
      />
    </div>
    <div class="col-span-2 p-8 py-28">
      <div
        id="register-form"
        class="max-w-4xl mx-auto flex flex-col gap-8 justify-center"
      >
        <h1 class="text-center">Create a Recruiter Account</h1>

        <!-- STEP 1 FORM -->
        <div v-if="step == 1">
          <FormKit
            type="form"
            id="registration-example"
            v-model="basicInfoForm"
            :form-class="'hide'"
            submit-label="Next"
            @submit="nextHandler"
            :incomplete-message="false"
            :submit-attrs="{
              inputClass: 'btn btn-primary ',
              outerClass: 'max-w-xl mx-auto text-center',
            }"
          >
            <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
              <FormKit
                type="text"
                name="name"
                label="Full Name"
                validation="required|length:5"
                placeholder="Full Name"
              />
              <FormKit
                type="email"
                name="email"
                label="Email Address"
                validation="required|email"
                placeholder="Your Email Address"
              />
              <FormKit type="group" name="password_group">
                <FormKit
                  type="password"
                  name="password"
                  value=""
                  label="Create Password"
                  placeholder="Enter Your Password"
                  help="Enter password"
                  validation="required|length:8"
                />
                <FormKit
                  type="password"
                  name="password_confirm"
                  label="Confirm Password"
                  placeholder="Confirm Your Password"
                  help="Confirm your new password"
                  validation="required|confirm"
                  validation-label="Password confirmation"
                />
              </FormKit>
              <FormKit
                type="tel"
                name="mobile"
                label="Phone Number"
                placeholder="01144448888"
                validation="required|length:10,13"
                :validation-messages="{
                  matches: 'Phone number must be in the format xxxxxxxxxx',
                }"
              />

              <FormKit
                type="text"
                name="ic"
                label="IC Number"
                placeholder="Enter Your IC Number"
                validation="required|matches:/[0-9]/|length:12,12"
                :validation-messages="{
                  matches: 'Please insert number only without dashes (-)',
                  length: 'Please insert number only without dashes (-)',
                }"
                validation-visibility="blur"
              />

              <!-- <FormKit
                type="file"
                label="Upload Identification Card Copy"
                accept=".pdf"
                :classes="{
                  noFiles: 'hidden',
                  fileName: 'hidden',
                  fileRemove: 'hidden',
                }"
                validation="required"
              /> -->
            </div>
            <p class="text-xs text-center mt-8">
              By pressing next, you agree on Seekers's Privacy Statement, Terms
              & Conditions
            </p>
          </FormKit>
        </div>

        <!-- STEP 2 FORM -->
        <div v-if="step == 2">
          <p class="text-center mb-8">
            Please complete the screening questions below
          </p>
          <FormKit
            type="form"
            id="registration-example"
            v-model="screeningQuestionForm"
            :form-class="'hide'"
            submit-label="Register"
            @submit="register"
            :incomplete-message="false"
            :submit-attrs="{
              inputClass: 'btn btn-primary',
              outerClass: 'max-w-xl mx-auto text-center mt-4',
            }"
          >
            <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
              <!-- Technical Recruiter select -->
              <FormKit
                type="select"
                label="1. Are you a Technical or a Non Technical Recruiter?"
                name="is_technical_recruiter"
                validation="required"
                placeholder="Please Select"
                :value="0"
                :options="[
                  { label: 'Technical Recruiter', value: 1 },
                  { label: 'Non Technical Recruiter', value: 0 },
                ]"
              />

              <!-- Experience select -->
              <FormKit
                type="select"
                label="2. How many years of experience do you have as a recruiter?"
                name="experience_years"
                validation="required"
                placeholder="Please Select"
                :options="recruiterExeperianceOptions"
              />
              <!-- Specialization select -->
              <FormKit
                type="select"
                label="3. What is your specialization?"
                name="specialization"
                validation="required"
                placeholder="Please Select"
                :options="specializationOptions"
              />
              <!-- Fulltime select -->
              <FormKit
                type="select"
                label="4. Are you working full-time now?"
                name="is_working_fulltime"
                validation="required"
                placeholder="Please Select"
                :options="[
                  { label: 'Yes', value: 1 },
                  { label: 'No', value: 0 },
                ]"
              />
              <!-- Commit hours select -->
              <FormKit
                type="select"
                label="5. How many hours can you commit per day doing freelance recruitment?"
                name="committed_hours_daily"
                validation="required"
                placeholder="Please Select"
                :options="[
                  { label: '1-3 hours', value: 3 },
                  { label: 'More than 5 hours', value: 5 },
                ]"
              />
              <!-- Source method select -->
              <FormKit
                type="select"
                label="6. What is your source method?"
                name="sourcing_method"
                validation="required"
                placeholder="Please Select"
                :options="[
                  'Social Media',
                  'Jobstreet',
                  'LinkedIn Premium',
                  'LinkedIn Recruiter',
                  'Competitor Headhunting',
                ]"
                :classes="{
                  inner: 'md:mt-6',
                }"
              />
            </div>
            <p class="text-xs text-center mt-8">
              By pressing register, you agree on Seekers's Privacy Statement,
              Terms & Conditions
            </p>
          </FormKit>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";

definePageMeta({
  title: "Recruiter Register",
  description: "Recruiter Register",
  keywords: "Recruiter Register",
  layout: "recruiter",
});

// hellobar script
useHead({
  script: [
    {
      src: "https://my.hellobar.com/bb324d1a61ba9504386213880443e889e88536f1.js",
      type: "text/javascript",
      async: true,
    },
  ],
});

const token = useRecruiter().token;
if (token.value) {
  navigateTo("/recruiter/dashboard");
}
const specializationOptions = useHelper.industries.map(
  (industry) => industry.name
);

const recruiterExeperianceOptions = useHelper.recruiterExeperianceOptions;

// Form things
const step = ref(1);
const basicInfoForm: any = useState("basicInfoForm", () => null);
const registrationForm: any = ref(null);
const screeningQuestionForm: any = ref(null);

function nextHandler() {
  registrationForm.value = {
    ...basicInfoForm?.value,
    password: basicInfoForm.value.password_group.password,
  };
  delete registrationForm.value.password_group;
  step.value = 2;
  window.scrollTo(0, 0);
}

async function register() {
  // combine both forms
  let formData = {
    ...registrationForm.value,
    ...screeningQuestionForm.value,
  };
  const result = await useRecruiter().register(formData);

  if (result && result.success) {
    navigateTo("/recruiter/register/completed");
  } else if (result.error.email) {
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: result.error.email[0],
    });
    step.value = 1;
  } else {
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: "Something went wrong!",
    });
  }
}
</script>
