<template>
  <div id="candidate-login-page" class="grid grid-cols-1 lg:grid-cols-3 w-full">
    <div class="relative w-full">
      <DevOnly>
        <div class="absolute m-2 flex flex-col gap-2">
          <button class="btn" @click="generatePassAccount">
            Simulate Password Account Creation
          </button>

          <p>DEBUG registrationForm:</p>
          <p>
            {{ registrationForm }}
          </p>
        </div>
      </DevOnly>
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463401/website/candidate/registration-bg_hpglmp.png"
        alt="login-bg"
        class="h-20 lg:h-screen w-full object-left-top object-cover"
      />
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463402/website/candidate/registration-vector_va9uqh.svg"
        alt=""
        class="hidden lg:block lg:absolute bottom-28 left-0 object-cover"
      />
    </div>
    <div class="col-span-2 p-8 py-12">
      <div
        id="login-form"
        class="max-w-4xl mx-auto flex flex-col gap-8 justify-center"
      >
        <h1 class="text-center">Candidate Registration</h1>
        <ul class="steps scale-75">
          <li class="step step-primary cursor-pointer" @click="step = 1">
            Basic Info
          </li>

          <li
            class="step cursor-pointer"
            :class="{ 'step-primary': step === 2 }"
          >
            Education / Work
          </li>
        </ul>
        <div v-if="step == 1">
          <RegistrationBasicInfoForm @submit="nextForm" />
        </div>

        <div v-if="step == 2">
          <RegistrationEducationWorkForm @submit="register" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";
const step = ref(1);
const registrationForm: any = ref(null);

// triggered when the basic info form is submitted.
// Basic form data is passed as an argument form the form child component.
// So we can use it to populate the registration form.
function nextForm(basicInfoFormData: BasicInfoForm) {
  registrationForm.value = {
    ...basicInfoFormData,
    password: basicInfoFormData.password_group.password,
  };
  delete registrationForm.value.password_group;
  step.value = 2;
  window.scrollTo(0, 0);
}

// triggered when the education/work form is submitted.
// Education/work form data is passed as an argument form the form child component.
// So we can use it to populate the registration form.
async function register(educationWorkFormData: EducationWorkForm) {
  try {
    console.log(educationWorkFormData);
    const formRegister = {
      ...registrationForm.value,
      ...educationWorkFormData,
      open_to_work: 1,
      education_start: educationWorkFormData.education_period.start,
      education_end: educationWorkFormData.education_period.end,
      work_start: educationWorkFormData.working_period.start,
      work_end: educationWorkFormData.working_period.end,
      cv: educationWorkFormData.cv[0]?.file,
    };
    if (!formRegister.cv) {
      delete formRegister.cv;
    }
    await useCandidate().register(formRegister);
    Swal.fire({
      title: "Registration Success",
      text: "Please check your email to verify your account",
      icon: "success",
      confirmButtonText: "OK",
    }).then(() => {
      navigateTo("/candidate/login");
    });
  } catch (e: any) {
    try {
      Swal.fire({
        title: "Error",
        text: Object.keys(e.data.error)
          .map((key) => e.data.error[key])
          .join("\n"),
        icon: "info",
        confirmButtonText: "OK",
      });
    } catch (e) {
      console.log(e);
    }
  }
}

const generatePassAccount = async () => {
  const yes = confirm("Are you sure you want to generate a test account?");
  if (yes) {
    const randomNumber = Math.floor(Math.random() * 1000);
    registrationForm.value = {
      name: `Generated Test Account ${randomNumber}`,
      email: `${randomNumber}@seekers.my`,
      mobile: "***********",
      birth_year: "2000",
      is_local: 1,
      state: "Kuala Lumpur",
      gender: "female",
      password: "***********",
      education_qualification: "stm-stpm",
      education_title: "***********",
      education_institute: "***********",
      education_period: {
        start: "2019-11-30",
        end: "2021-12-31",
      },
      work_role: "***********",
      work_company_name: "***********",
      work_responsibilities: "***********",
      working_period: {
        start: "2022-11-30",
        end: "2023-12-31",
      },
      cv: [],
    };
    await register(registrationForm.value);
  }
};
</script>
