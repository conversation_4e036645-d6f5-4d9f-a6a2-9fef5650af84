<template>
  <div id="companies-page">
    <CandidatePageHeader
      class="w-full text-center bg-info px-4 py-10"
      title="Companies"
      subtitle="Find Companies"
    />
    <div
      class="max-w-7xl mx-auto grid sm:grid-cols-[2fr_3fr] lg:grid-cols-[1fr_3fr]"
    >
      <!-- <CandidateJobFilter class="bg-info p-8 pb-40" /> -->
      <div id="search-results-list" class="p-8 col-span-2">
        <div class="flex items-center gap-2">
          <p class="text-sm">
            Showing
            <span class="font-semibold">{{ searchCompanyFilter.page }}</span> of
            <span class="font-semibold">{{ companiesData?.last_page }}</span>
            pages
          </p>

          <nuxt-link
            v-if="searchCompanyFilter.page > 1"
            class="btn btn-info btn-sm"
            :to="`/company?page=${prevPageNo}`"
          >
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </div>
          <nuxt-link
            v-if="searchCompanyFilter.page < companiesData.last_page"
            class="btn btn-info btn-sm"
            :to="`/company?page=${nextPageNo}`"
          >
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </div>

          <select
            name="sort"
            v-model="searchCompanyFilter.sort"
            id="sort-selector"
            class="select select-sm select-accent outline-never ml-auto border-base-200"
          >
            <option value="">Latest</option>
            <option value="oldest">Oldest</option>
          </select>
          <select
            v-model="searchCompanyFilter.limit"
            @change="refetchCompanies"
            name="display"
            id="perpage-selector"
            class="select select-sm select-accent outline-never border-base-200"
          >
            <option
              v-for="option in displayPerPageOptions"
              :value="option.value"
            >
              {{ option.label }}
            </option>
          </select>
          <p class="text-xs">per page</p>
        </div>
        <CompanyCardsList
          :companiesList="pending ? null : companiesData?.data"
          :isLoading="pending"
          :showPerPage="12"
          :showPagination="false"
          :lastPage="companiesData?.last_page"
        />
      </div>
    </div>
    <DevOnly>
      <!-- <pre>{{ companiesData }}</pre> -->
      <!-- <pre>{{ route }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup>
const route = useRoute();
const pending = ref(false);
const companiesData = ref(null);
const searchCompanyFilter = useCompanies().searchCompanyFilter();

const { data: initialData } = await useCompanies().searchWithFilter();
companiesData.value = initialData.value;

const displayPerPageOptions = [
  { value: 12, label: "12" },
  { value: 24, label: "24" },
  { value: 36, label: "36" },
];

async function refetchCompanies() {
  pending.value = true;
  const { data } = await useCompanies().searchWithFilter();
  companiesData.value = data.value;
  pending.value = false;
}

watch(
  () => route.query.page,
  (page) => {
    if (page) {
      searchCompanyFilter.value.page = parseInt(page);
      refetchCompanies();
    } else {
      searchCompanyFilter.value.page = 1;
      refetchCompanies();
    }
  }
);

watch(
  () => searchCompanyFilter.value.limit,
  () => {
    searchCompanyFilter.value.page = 1;
    refetchCompanies();
  }
);

const nextPageNo = computed(() => {
  return parseInt(searchCompanyFilter.value.page) + 1;
});
const prevPageNo = computed(() => {
  return parseInt(searchCompanyFilter.value.page) - 1;
});
</script>
