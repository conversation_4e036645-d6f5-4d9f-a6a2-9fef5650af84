<template>
  <div id="company-details-page">
    <!-- HEADER -->
    <div class="bg-gradient-to-r from-[#E5EBF5]/60 via-[#F5F7FC]/60 to-success/60 py-4 md:py-12">
      <div class="md:max-w-7xl md:mx-auto mx-4 py-4">
        <div class="flex items-center">
          <img :src="companyData?.logo_url" :alt="companyData?.name + '-image'"
            class="w-20 h-20 rounded object-contain" />
          <div class="text-xs w-full ml-4">
            <h1 class="font-semibold text-lg leading-6">
              {{ companyData?.name }}
            </h1>
            <div class="flex items-end justify-between">
              <div>
                <div class="md:flex md:items-center md:gap-2 md:my-1 mt-2">
                  <div class="flex text-gray-500">
                    <Icon name="heroicons:map-pin" class="w-4 h-4 mr-2" />
                    <p>
                      {{ companyData?.city }}
                    </p>
                  </div>
                  <div class="flex text-gray-500 mt-1 md:mt-0">
                    <Icon name="heroicons:briefcase" class="w-4 h-4 mr-2" />
                    <p>
                      {{ companyData?.industry.name }}
                    </p>
                  </div>
                </div>
                <p class="badge badge-success text-xs mt-2 md:mt-1">
                  Open Jobs - {{ companyData?.open_jobs_count }}
                </p>
              </div>

              <div v-if="candidateToken">
                <button v-if="!isFollowed" class="btn btn-success" :disabled="pending" @click="follow">
                  <Icon name="heroicons:bell" />
                </button>

                <button v-else class="btn" :disabled="pending" @click="unfollow">
                  <Icon name="heroicons:bell-solid" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-12">
      <div class="grid md:grid-cols-[5fr_2fr] gap-6 mb-12 mx-4 md:mx-0">
        <!-- LEFT SIDE -->
        <div>
          <p class="font-semibold mb-4">Who we are?</p>
          <div v-html="cleanHtml(companyData?.overview)" class="text-gray-500 mb-4 break-words"></div>
          <div v-if="companyData?.company_images?.length > 0" class="flex gap-4 overflow-x-auto my-4 scrollbar-hide">
            <img v-for="img in companyData?.company_images" :src="img.image_url" :key="img.id" :alt="img.caption"
              class="h-64 rounded-md object-cover" />
          </div>

          <!-- OPEN JOBS DESKTOP -->
          <div class="mt-12 hidden md:block" id="related-jobs">
            <h3 class="font-semibold mb-4">Available Jobs</h3>
            <CandidateHorizontalJobCard v-if="Object.keys(companyData?.jobs).length > 0" v-for="job in companyData.jobs"
              :job="job" />
            <div v-else class="border rounded-lg w-max p-8 pr-20">
              No open positions available currently
            </div>
          </div>
        </div>

        <!-- RIGHT SIDE -->
        <div>
          <div class="bg-info rounded-md p-8 mb-8">
            <div class="flex items-center">
              <p class="font-semibold">{{ companyData?.name }}</p>
            </div>
            <div class="grid items-center text-sm gap-4 mt-4">
              <div class="flex justify-between">
                <p class="font-semibold">Primary Industry:</p>
                <p class="text-end">{{ companyData?.industry.name }}</p>
              </div>
              <div v-if="companyData?.size != null" class="flex justify-between">
                <p class="font-semibold">Company size:</p>
                <p class="text-end">{{ companyData?.size }}</p>
              </div>
              <div v-if="companyData?.location && companyData?.location !== 'null'" class="flex justify-between">
                <p class="font-semibold">Location:</p>
                <p class="text-end">{{ companyData?.location }}</p>
              </div>
              <div v-if="companyData?.reg_no" class="flex justify-between">
                <p class="font-semibold">Company Registration:</p>
                <p class="text-end">{{ companyData?.reg_no }}</p>
              </div>
            </div>
          </div>

          <div v-if="companyData?.long !== null" class="bg-info p-8 rounded-md">
            <p class="font-semibold mb-4">Job Location</p>
            <div class="h-48">
              <ClientOnly>
                <iframe class="w-full h-full rounded-md" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"
                  :src="`https://maps.google.com/maps?width=100%25&amp;height=600&amp;hl=en&amp;q=${companyData?.lat},${companyData?.long}&amp;t=&amp;z=14&amp;ie=UTF8&amp;iwloc=B&amp;output=embed`"></iframe>
              </ClientOnly>
            </div>
          </div>
        </div>

        <!-- OPEN JOBS MOBILE -->
        <div class="md:hidden" id="related-jobs">
          <h3 class="font-semibold mb-4">Available Jobs</h3>
          <CandidateHorizontalJobCard v-if="Object.keys(companyData?.jobs).length > 0" v-for="job in companyData.jobs"
            :job="job" />
          <div v-else class="border rounded-lg w-max p-8">
            No open positions available currently
          </div>
        </div>
      </div>
    </div>
    <DevOnly>
      <!-- <pre>{{ companyData }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const candidateToken = useCandidate().token;
const { data: companyData, error } = await useCompanies().bySlug(
  route.params.company_slug as string
);

if (error.value) {
  throw createError({
    statusCode: 404,
    statusMessage: "Not Found",
  });
}

useHead({
  link: [
    {
      rel: "canonical",
      href: () => `https://seekers.my/company/${companyData.value?.slug}`,
    },
  ],
});
useSeoMeta({
  title: () => `${companyData.value?.name} | Seekers`,
  ogTitle: () => `${companyData.value?.name} | Seekers`,
  ogType: "website",
  ogUrl: () => `https://seekers.my/company/${companyData.value?.slug}`,
  description: () => `${companyData.value?.overview}`,
  ogDescription: () => `${companyData.value?.overview}`,
  ogImage: () => `${companyData.value?.logo_url}`,
});

const { data: profileData, pending, refresh } = useCandidate().getProfile();

const isFollowed = computed(() => {
  return profileData?.value?.companies_following?.some(
    (company: any) => company.id === companyData.value.id
  );
});
const follow = async () => {
  await useCandidate().followCompany(companyData?.value.id);
  refresh();
};
const unfollow = async () => {
  await useCandidate().unfollowCompany(companyData?.value.id);
  refresh();
};

const cleanHtml = (html: string) => {
  if (!html) return html;
  //replace all &nbsp; with a space
  html = html?.replace(/&nbsp;/g, " ");

  // replace <pre tag with <p tag
  html = html?.replace(/<pre/g, "<ul");

  // replace </pre> with </p>
  html = html?.replace(/<\/pre>/g, "</ul>");

  //remove all url links
  html = html?.replace(/<a.*?\/a>/g, "");

  return html;
};
</script>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

pre,
.ql-syntax {
  width: 100% !important;
}
</style>
