# Seekers Web (v3)

[![Deploy to Cloudflare](https://github.com/seekers-my/seekers-nuxt/actions/workflows/deploy.yml/badge.svg)](https://github.com/seekers-my/seekers-nuxt/actions/workflows/deploy.yml)

This Project is the Frontend Code for Seekers.my, developed on Nuxt 3 Framework.

It is hosted on Cloudflare Workers. (see `trangler.toml` file)

The CICD step is using CircleCI (see `.circleci`folder)

## Requirement

Node >= v20.x

OR

Bun >= 1.0.5

## Setup

If you are using Mac or Linux, we recommend using `Bun` for faster development experience.
Windows users can fall back to `Yarn` or `npm` is fine.

Make sure to install the dependencies:

```bash
bun install

```

## Branch Strategy

`main` branch

- Holds the latest code. Used as base for feature branches.
- Visible at https://beta.seekers.my

`production` branch

- Holds the live code. Only normal merge from `main` to this branch. Do not merge in feature branches.
- Visible at https://seekers.my

`feature/example` branches

- After dev work is completed, make a PR to squash merge this branch into `main` branch.

## Development Server

Start the development server on http://localhost:3000
We recommend the use of Nuxt DevTools on Google Chrome or Firefox or Opera

```bash
bun run dev
```

Locally preview production build:

```bash
bun run preview
```

## Q&A

Directus is the target backend framewrk.

When querying data please use the appropriate Filter Queries found here:

https://docs.directus.io/reference/filter-rules.html#filter-operators

## Tech Stack

**Client:** Nuxt 3, TailwindCSS

**Server:** Nuxt 3

**Deployment:** Cloudflare

## Roadmap

- Unit tests using Vitest

- End-to-end tests

## Documentation

Look at the [nuxt 3 documentation](https://v3.nuxtjs.org) to learn more.

## Authors

- [@rafazafar](https://www.github.com/rafazafar)
- [@afiqtyzd](https://www.github.com/afiqtyzd)
