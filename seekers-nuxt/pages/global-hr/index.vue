<template>
  <div id="global-hr">
    <GlobalHRHero />

    <GlobalHRThreePoints />

    <GlobalHRAcquireTalent />

    <GlobalHRPerks />

    <GlobalHRTopTalent />

    <GlobalHRFAQ />

    <GlobalHRReachOut />

    <GlobalHRGainInsight :class="$i18n.locale == 'en' ? 'block' : 'hidden'" />
  </div>
</template>

<script setup lang="ts">
useSeoMeta({
  title:
    "Hire Global Talent | Hire remote employee in other countries without legal entity - Seekers",
  ogTitle:
    "Hire Global Talent | Hire remote employee in other countries without legal entity - Seekers",
  description:
    "Seekers offers global recruitment solutions that connect employer with top talent worldwide, allowing you to build a successful remote employee and expand your business internationally without a legal entity. With our comprehensive global recruitment solutions, you can streamline your hiring process and navigate the legal requirements of international business. Contact us today to hire global talent and benefit from our expertise in global HR.",
  ogDescription:
    "Seekers offers global recruitment solutions that connect employer with top talent worldwide, allowing you to build a successful remote employee and expand your business internationally without a legal entity. With our comprehensive global recruitment solutions, you can streamline your hiring process and navigate the legal requirements of international business. Contact us today to hire global talent and benefit from our expertise in global HR.",
  ogType: "website",
  ogSiteName: "Seekers",
  ogUrl: "https://seekers.my/global-hr",
  ogImage:
    "https://s3-ap-southeast-1.amazonaws.com/seekers-web/seekers_meta_banner.jpg",
});
</script>
