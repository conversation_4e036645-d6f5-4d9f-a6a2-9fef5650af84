<template>
  <div
    id="candidate-dashboard-change-password"
    transition-style="in:circle:top-left"
  >
    <CandidateDashboardHeader
      title="Change Password"
      subtitle="Edit or change your password here"
    />
    <div class="bg-white p-6 rounded-lg">
      <div id="passwordForm">
        <div class="grid gap-4 text-sm">
          <div class="relative">
            <label for="password" class="label"
              ><div class="label-text">Enter Your New Password</div></label
            >
            <input
              id="password"
              v-model="password"
              name="password"
              :type="showPassword ? 'text' : 'password'"
              class="input input-bordered text-sm w-full md:w-96"
              placeholder="Password"
            >
            <Icon
              :name="
                showPassword == false ? 'heroicons:eye' : 'heroicons:eye-slash'
              "
              class="absolute right-0 md:left-0 text-lg mr-4 md:ml-[22rem] mt-4 cursor-pointer"
              @click="toggleShowPassword"
            />
          </div>

          <div class="relative">
            <label for="confirmPassword" class="label"
              ><div class="label-text">Confirm Your New Password</div></label
            >
            <input
              id="confirmPassword"
              v-model="confirmPassword"
              name="password"
              :type="showConfirmPassword ? 'text' : 'password'"
              class="input input-bordered text-sm w-full md:w-96"
              placeholder="Confirm Password"
            >
            <Icon
              :name="
                showConfirmPassword == false
                  ? 'heroicons:eye'
                  : 'heroicons:eye-slash'
              "
              class="absolute right-0 md:left-0 text-lg mr-4 md:ml-[22rem] mt-4 cursor-pointer"
              @click="toggleShowConfirmPassword"
            />
          </div>
        </div>

        <button class="btn btn-primary px-6 py-2 mt-4" @click="changePassword">
          Change Password
        </button>
      </div>
    </div>
    <DevOnly>
      <!-- <pre>{{ profile }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup>
import Swal from "sweetalert2";

definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const password = ref("");
const confirmPassword = ref("");

const showPassword = ref(false);
const showConfirmPassword = ref(false);

function toggleShowPassword() {
  showPassword.value = !showPassword.value;
}

function toggleShowConfirmPassword() {
  showConfirmPassword.value = !showConfirmPassword.value;
}

function validateData() {
  if (!password.value || !confirmPassword.value) {
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: "Please fill in the new password",
      confirmButtonColor: "#33A852",
    });
    return false;
  } else if (password.value !== confirmPassword.value) {
    Swal.fire({
      icon: "error",
      title: "Hmmm...",
      text: "Password does not match",
      confirmButtonColor: "#33A852",
    });
    return false;
  } else {
    return true;
  }
}

async function changePassword() {
  if (validateData()) {
    await useCandidate().updatePassword(password.value, confirmPassword.value);
    Swal.fire({
      icon: "success",
      text: "Password is changed",
      confirmButtonColor: "#33A852",
    });
  }
}
</script>
