<template>
  <div v-if="candidateProfile" transition-style="in:circle:top-left">
    <CandidateDashboardProfileHeader :openToWork="candidateProfile?.open_to_work" :name="candidateProfile?.name"
      :designation="candidateProfile?.latest_role" :state="candidateProfile?.state" :email="candidateProfile?.email"
      :createdAt="candidateProfile?.date_created" :tags="candidateProfile?.tags" :profile-img="candidateProfileImage" />
    <div class=" grid lg:grid-cols-[2fr_5fr] p-6 gap-6 lg:p-12 lg:gap-12">
      <div class="flex flex-col gap-3 md:gap-6">
        <!-- left section -->
        <div class="h-min bg-info grid sm:grid-cols-2 lg:grid-cols-1 gap-3 lg:gap-6 p-6 xl:p-12 rounded-lg">
          <CandidateDashboardProfileSpecs v-if="candidateProfile" :specs="candidateProfile" />
        </div>
        <CandidateDashboardSocialLinks :socials />

        <div v-if="candidateProfile?.tags?.length != 0" class="bg-info p-6 px-6 md:p-6 xl:p-8 rounded-lg text-sm">
          <p class="mb-3">Professional Skills</p>
          <div class="flex flex-wrap gap-2">
            <div v-for="skill in candidateProfile?.tags" class="rounded bg-white p-1 px-2 text-accent text-xs">
              {{ skill }}
            </div>
          </div>
        </div>
      </div>

      <!-- right section -->
      <div class="flex flex-col gap-12 max-w-3xl">
        <CandidateDashboardAbout />
        <div>
          <div class="flex items-center mb-4 gap-4">
            <h3 class="font-semibold">Education</h3>
            <nuxt-link to="/candidate/dashboard/education">
              <Icon name="heroicons:pencil" class="w-5 h-5" />
            </nuxt-link>
          </div>
          <!-- <CandidateDashboardEducationItem
            v-for="(education, index) in educationData"
            :key="education.id"
            :value="education"
            :last-step="index === educationData.length - 1"
          />
          <CandidateDashboardEducationItem
            v-if="educationData.length == 0"
            :value="{}"
            lastStep
            addNew
          /> -->
        </div>
        <div>
          <div class="flex items-center mb-4 gap-4">
            <h3 class="font-semibold">Work & Experience</h3>
            <nuxt-link to="/candidate/dashboard/work-experience">
              <Icon name="heroicons:pencil" class="w-5 h-5" />
            </nuxt-link>
          </div>
          <CandidateDashboardWorkExperienceItem v-for="(education, index) in candidateProfile?.work_history"
            :key="education.id" :value="education" :last-step="index === candidateProfile?.work_history?.length - 1" />
          <CandidateDashboardWorkExperienceItem v-if="candidateProfile?.work_history?.length == 0" :value="{}" last-step
            add-new />
        </div>
        <!-- <CandidateDashboardPortfolio :data="candidateProfile" /> -->
        <div>
          <div class="flex items-center mb-4 gap-4">
            <h3 class="font-semibold">License & Certication</h3>
            <nuxt-link to="/candidate/dashboard/license-certification">
              <Icon name="heroicons:pencil" class="w-5 h-5" />
            </nuxt-link>
          </div>
          <CandidateDashboardLicenseCertificationItem v-for="(
certification, index
            ) in candidateProfile?.license_or_certification" :key="certification.id" :value="certification" :last-step="index === candidateProfile?.license_or_certification?.length - 1
              " />

          <CandidateDashboardLicenseCertificationItem v-if="candidateProfile?.license_or_certification?.length == 0"
            :value="{}" last-step add-new />
        </div>
      </div>
    </div>

    <div class="bg-white pb-12">
      <div
        class="bg-success/50 p-4 rounded-md grid gap-4 justify-center items-center text-center text-sm md:grid-cols-[2fr_1fr] md:max-w-3xl md:mx-auto">
        <div class="grid gap-2">
          <p class="font-semibold">
            Discover Your Worth: Check Out Our Comprehensive
            <span class="text-primary">Salary Guide</span>
          </p>
          <p>Click Here to Discover Market Rates and Maximize Your Income!</p>
        </div>
        <nuxt-link to="/salary-guide" class="py-2 px-4 w-fit mx-auto rounded-md bg-primary text-white">Salary
          Guide</nuxt-link>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { directusAssetsUrl } = useHelper();
const { profile: candidateProfile } = useCandidate();

const candidateProfileImage = computed(() => {
  if (!candidateProfile.value?.profile_photo) {
    return 'https://seekers.my/blog/content/images/size/w100/2020/07/seekers_favicon.jpg"';
  }
  return directusAssetsUrl(candidateProfile.value?.profile_photo.id)
});

const socials = computed(() => ({
  facebook: candidateProfile?.value?.facebook || '',
  twitter: candidateProfile?.value?.twitter || '',
  linkedin: candidateProfile?.value?.linkedin || '',
  github: candidateProfile?.value?.github || '',
  website: candidateProfile?.value?.website || '',
  instagram: candidateProfile?.value?.instagram || '',
}));
</script>
