<template>
  <div id="candidate-settings" :class="{ 'animate-pulse': loading }" transition-style="in:circle:bottom-left">
    <CandidateDashboardHeader title="Profile" subtitle="Edit your profile, add a profile photo, and more." />

    <div class="bg-white p-6 rounded-lg">
      <div class="grid lg:grid-cols-2 gap-4">
        <div>
          <h3>Profile Photo</h3>
          <div class="my-4 flex gap-4 items-center" :class="{ 'animate-pulse': loadingUpload }">
            <img id="avatar-image" class="w-24 h-24 rounded-full object-cover" :src="directusAssetsUrl(profileData.profile_photo.id) ||
              'https://seekers.my/blog/content/images/size/w100/2020/07/seekers_favicon.jpg'
              " alt="profile avatar" />
            <label for="image">
              <div class="btn btn-sm">Change</div>
            </label>
            <input id="image" type="file" class="input w-full p-0 rounded-none hidden" placeholder="Image"
              @change="handleImageChange($event)" />
          </div>
        </div>

        <div>
          <h3>CV | Resume</h3>
          <CustomCvInput :existing-cv-name="profileData.cv || {}" @change="handleCVChange" />
        </div>
      </div>
      <div class="divider"></div>
      <div class="my-8">
        <h3>Actively looking for a new job?</h3>
        <p class="text-xs">
          Let our recruiters know by changing your status below.
        </p>
        <button class="btn mt-4" :class="{
          'outline outline-offset-1 outline-gray-200':
            form.open_to_work === true,
          'btn-primary': form.open_to_work === true,
        }" @click="form.open_to_work = form.open_to_work === true ? false : true">
          <Icon :name="form.open_to_work === true
            ? 'heroicons:check'
            : 'heroicons:x-mark'
            " />
          <p class="ml-1">
            {{
              form.open_to_work === true
                ? "Yes, Im open to work!"
                : "No, im not open to work"
            }}
          </p>
        </button>
      </div>

      <div class="grid lg:grid-cols-2 gap-20 mt-4">
        <div class="flex flex-col gap-2">
          <h3>About Me</h3>
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-2 text-sm">
            <!-- name -->
            <label for="name" class="label">
              <div class="label-text text-accent">Name</div>
            </label>
            <input id="name" name="name" v-model="form.name" class="input input-bordered text-sm" placeholder="Name" />

            <!-- email -->
            <label for="email" class="label">
              <div class="label-text text-accent">Email</div>
            </label>
            <input id="email" name="email" v-model="form.email" disabled class="input input-bordered text-sm"
              placeholder="<EMAIL>" />

            <!-- mobile -->
            <label for="mobile" class="label">
              <div class="label-text text-accent">Mobile</div>
            </label>
            <input id="mobile" name="mobile" v-model="form.mobile" class="input input-bordered text-sm"
              placeholder="Mobile" />

            <!-- birthday -->
            <label for="birthday" class="label">
              <div class="label-text text-accent">Birthday</div>
            </label>
            <FormKit type="date" :value="dayjs(form.birthday).format('YYYY-MM-DD')"
              @change="form.birthday = $event.target.value" />
          </div>

          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label for="nationality" class="label">
              <div class="label-text text-accent">Nationality</div>
            </label>
            <div class="btn-group btn-group-horizontal">
              <button class="btn" :class="{ 'btn-accent': form.is_local === true }" @click="form.is_local = true">
                Malaysian
              </button>
              <button class="btn ml-2" :class="{ 'btn-accent': form.is_local === false }"
                @click="form.is_local = false">
                Expatriate
              </button>
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-2">
          <h3>Additional Info</h3>

          <!-- gender -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label">
              <div class="label-text">Gender</div>
            </label>
            <select v-model="form.gender" class="select text-sm w-full input-bordered">
              <option value="male">Male</option>
              <option value="female">Female</option>
            </select>
          </div>

          <!-- education level -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label">
              <div class="label-text">Education Level</div>
            </label>
            <select v-model="form.education_level" class="select text-sm w-full input-bordered">
              <option v-for="item in QUALIFICATIONS_OPTIONS" :value="item.value">
                {{ item.label }}
              </option>
            </select>
          </div>

          <!-- current position -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label">
              <div class="label-text">Current Job Title</div>
            </label>
            <input id="key" v-model="form.current_position" name="current_position" type="text"
              class="input input-bordered text-sm" placeholder="Current Job Title" />
          </div>

          <!-- current salary -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label">
              <div class="label-text">Current Salary (RM)</div>
            </label>
            <input id="key" v-model="form.salary_current" name="key" type="number" class="input input-bordered text-sm"
              placeholder="Current Salary (RM)" min="500" />
          </div>

          <!-- expected salary -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label">
              <div class="label-text">Expected Salary (RM)</div>
            </label>
            <input id="key" v-model="form.salary_expected" name="key" type="key === 'email' ? 'email' : 'text'"
              class="input input-bordered text-sm" placeholder="Expected Salary (RM)" />
          </div>

          <!-- state -->
          <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
            <label class="label">
              <div class="label-text">State</div>
            </label>
            <select v-model="form.state" class="select text-sm w-full input-bordered">
              <option selected disabled>Choose a State</option>
              <option v-for="item in MALAYSIAN_STATES" :value="item.value">
                {{ item.label }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <div class="my-8 grid lg:grid-cols-2 gap-20">
        <div class="flex flex-col gap-2">
          <h3>Languages</h3>
          <CustomLanguagePicker v-model="form.languages" />
        </div>
      </div>
      <button class="btn btn-primary px-12 mt-8" :disabled="loading" @click="save">
        {{ loading ? "Saving ......" : "Save Profile" }}
      </button>
      <div class="divider" />

      <button class="btn btn-accent" @click="logout">
        <Icon name="heroicons:arrow-left-on-rectangle-solid" class="w-5 h-5" />
        <span class="mx-4 text-xs animate__animated animate__fadeIn animate__faster">Logout</span>
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const user = useUser();
const { directusAssetsUrl } = useHelper();
const { $directus, $updateItem, $uploadFiles } = useNuxtApp();

const logout = async () => {
  await user.logout();
  navigateTo("/", { replace: true });
};

const { getProfile } = useCandidate();

const profileData = await getProfile();

const loading = useState(() => false);
const loadingUpload = useState(() => false);

const form = ref({
  birthday: profileData.birthday || null,
  current_position: profileData.current_position || null,
  cv: profileData.cv?.filename_disk || null,
  education_level: profileData.education_level || "bachelor",
  email: profileData.email || "hidden",
  gender: profileData.gender || "Male",
  profile_photo: profileData.profile_photo || null,
  languages: profileData.languages ?? ['english-native'],
  open_to_work: profileData.open_to_work,
  name: profileData.name || null,
  is_local: profileData.is_local,
  mobile: profileData.mobile || null,
  state: profileData.state || null,
  salary_current: profileData.salary_current || null,
  salary_expected: profileData.salary_expected || null,
});

async function handleImageChange(event: any) {
  // form.value.profile_photo = event.target.files[0];

  // set avatar-image element src to new image
  const reader = new FileReader();
  reader.onload = (e) => {
    const avatarImage = document.getElementById("avatar-image");
    avatarImage?.setAttribute("src", e.target?.result as string);
  };
  reader.readAsDataURL(event.target.files[0]);

  try {
    const formData = new FormData();
    formData.append("folder", "bbad2344-3161-4656-8b88-58b9cadf5be2");
    formData.append("title", `candidate-${profileData.id}`);
    formData.append("file", event.target.files[0]);

    loadingUpload.value = true;
    const result = await $directus.request($uploadFiles(formData));
    console.log("image change result", result);
    profileData.profile_photo = result.id;
    form.value.profile_photo = result.id;
    loadingUpload.value = false;
    // console.log("image change result", result);
  } catch (error) {
    // console.log("image change error", error);
  }
}

async function handleCVChange(file: any) {
  form.value.cv = file;
  console.log("first handle change file", file);

  try {
    const formData = new FormData();
    formData.append("folder", "9128ef54-9070-449f-8904-3d03ff8c812b");
    formData.append("file", form.value.cv);

    loadingUpload.value = true;
    const result = await $directus.request($uploadFiles(formData));
    profileData.cv = result.id;
    form.value.cv = result.id;
    loadingUpload.value = false;
    console.log("cv change result", result);
  } catch (error) {
    console.log("cv change error", error);
  }
}

async function save() {
  loading.value = true;
  const result = await $directus.request(
    $updateItem("candidates", profileData.id, form.value)
  );
  loading.value = false;
}
</script>
