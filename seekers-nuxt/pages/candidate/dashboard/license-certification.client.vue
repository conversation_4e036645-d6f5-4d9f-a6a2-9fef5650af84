<template>
  <div id="candidate-dashboard-license-certification" transition-style="in:circle:top-left">
    <CandidateDashboardHeader title="License & Certification"
      subtitle="Licenses and certifications are automatically sorted based on each start date" />

    <div class="bg-white p-6 rounded-lg">
      <div v-if="!pending" transition-style="in:circle:top-left">
        <CandidateDashboardLicenseCertificationItem v-for="certification in certifications" :key="certification.id"
          :value="certification" edit-allowed />
        <CandidateDashboardLicenseCertificationItem :value="{}" last-step add-new />
      </div>
      <div v-else>
        <span class="loading loading-ring loading-lg" />
      </div>
    </div>
  </div>
</template>
<script setup>
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { certifications } = useCandidate();


</script>
