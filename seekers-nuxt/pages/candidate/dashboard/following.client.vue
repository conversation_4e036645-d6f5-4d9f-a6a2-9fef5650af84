<template>
  <div id="candidate-dashboard-following" transition-style="in:circle:top-left">
    <CandidateDashboardHeader title="Companies Followed"
      subtitle="Any new jobs posted by these companies will be shown in your feed." />

    <div class="bg-white p-6 rounded-lg">
      <div class="grid gap-6 lg:grid-cols-2">
        <div v-for="company in companies" v-if="companies.length > 0"
          class="flex gap-4 lg:gap-6 items-center rounded-lg drop-shadow p-3 lg:p-6 bg-white">
          <img :src="directusAssetsUrl(company?.logo)" alt="comapny logo"
            class="w-20 h-20 object-contain cursor-pointer" @click="navigateTo(`/company/${company?.slug}`)">
          <div class="cursor-pointer flex-grow" @click="navigateTo(`/company/${company?.slug}`)">
            <h3 class="text-lg font-semibold">{{ company?.name }}</h3>
            <h5 class="text-xs text-gray-500">
              {{ company?.location != "null" ? company.location : "Malaysia" }}
            </h5>
            <h5 class="text-xs text-gray-500 mt-2">
              {{ company?.open_jobs_count }} Jobs Available
            </h5>
          </div>
          <button class="btn btn-circle" @click="unfollow(company.id)">
            <Icon name="heroicons:bookmark-solid" />
          </button>
        </div>
        <div v-if="companies.length === 0" class="border rounded-lg p-8">
          <p>No Companies followed.</p>
          <button class="btn px-12 mt-8" @click="navigateTo('/company')">
            Browse Companies
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { directusAssetsUrl } = useHelper()
const { profile, getProfile, unfollowCompany } = useCandidate();
const companies = computed(() => profile.value?.companies_followed || []);
const unfollow = async (company_id: number) => {
  await unfollowCompany(company_id);
  await getProfile();
};
</script>
