<template>
  <div id="candidate-dashboard-portfolio" transition-style="in:circle:top-left">
    <CandidateDashboardHeader
      :title="addMode ? 'Add' : 'My' + ' Portfolio / Documents'"
      subtitle="Upload your portfolio or documents here to help employers get to know
        you better."
    />
    <div class="bg-white p-6 rounded-lg">
      <div v-if="!pending" transition-style="in:circle:top-left">
        <div
          v-if="!addMode"
          id="portoflio-gallery"
          class="grid grid-cols-2 md:flex flex-wrap gap-4 w-full"
          :class="{ 'animate-pulse': isLoading }"
        >
          <div
            v-for="item in candidateData?.candidate_portfolio"
            class="rounded-lg bg-gray-200 w-full h-40 md:w-60 md:h-60 relative"
          >
            <button
              class="btn btn-circle btn-sm left-4 top-4 absolute"
              @click="item.file ? openNewTab(item.url) : openNewTab(item.link)"
            >
              <Icon
                :name="
                  item.file
                    ? 'heroicons:cloud-arrow-down'
                    : 'heroicons:arrow-top-right-on-square-solid'
                "
                class="h-5 w-5 md:w-4 md:h-4 animate__animated animate__fadeIn"
              />
            </button>
            <button
              class="btn btn-circle btn-sm right-4 top-4 absolute"
              @click="deletePortfolio(item.id)"
            >
              <Icon
                name="heroicons:x-mark"
                class="h-5 w-5 md:w-4 md:h-4 animate__animated animate__fadeIn text-error-content"
              />
            </button>
            <div id="portfolio-meta" class="absolute bottom-0 p-4">
              <p class="text-sm">{{ item.title }}</p>
              <p class="text-xs text-gray-500 break-all">
                {{ item.description }}
              </p>
            </div>
          </div>

          <button
            class="btn btn-success border rounded-lg w-full h-40 md:w-60 md:h-60"
            @click="addMode = true"
          >
            Add +
          </button>
        </div>

        <div
          v-else
          id="portfolio-add"
          class="grid gap-4 max-w-xl"
          :class="{ 'animate-pulse': isLoading }"
        >
          <p class="text-xs uppercase text-gray-500">Type</p>
          <div class="grid grid-cols-2 gap-4">
            <button
              class="btn px-12"
              :class="{ 'btn-accent': type == 'file' }"
              @click="type = 'file'"
            >
              File
            </button>
            <button
              class="btn px-12"
              :class="{ 'btn-accent': type == 'link' }"
              @click="type = 'link'"
            >
              Link
            </button>
          </div>

          <div class="grid gap-4">
            <div>
              <label for="title" class="text-xs uppercase text-gray-500"
                >Title</label
              >
              <input
                id="title"
                v-model="form.title"
                type="text"
                class="input input-bordered w-full"
                placeholder="Title"
              />
            </div>
            <div>
              <label for="description" class="text-xs uppercase text-gray-500"
                >Description</label
              >
              <input
                id="description"
                v-model="form.description"
                class="input input-bordered w-full"
                placeholder="Description"
              />
            </div>

            <p v-if="type == 'file'" class="text-xs uppercase text-gray-500">
              File
            </p>
            <label
              v-if="type == 'file'"
              for="file"
              class="text-xs uppercase text-gray-500 p-4 border rounded-lg w-full -mt-4"
              ><Icon
                name="heroicons:folder-plus"
                class="w-5 h-5 -mt-1 mr-4"
              />{{ form.file ? form.file?.name : "Choose File" }}</label
            >
            <input
              id="file"
              type="file"
              class="input w-full p-0 rounded-none hidden"
              placeholder="File"
              @change="form.file = $event.target.files[0]"
            />

            <div v-if="type == 'link'">
              <label for="link" class="text-xs uppercase text-gray-500"
                >Link</label
              >
              <input
                id="link"
                v-model="form.link"
                type="text"
                class="input input-bordered w-full"
                placeholder="Link"
              />
            </div>

            <div class="grid grid-cols-2 gap-4 mt-8">
              <!-- submit / cancel buttons -->
              <button class="btn btn-success px-12" @click="save">
                Submit {{ type }}
              </button>
              <button class="btn px-12" @click="addMode = false">Cancel</button>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <span class="loading loading-ring loading-lg" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const addMode = useState(() => false);
const isLoading = useState(() => false);
const type = ref("file");
const form = ref({
  title: "",
  description: "",
  file: "",
  link: "",
});

// method to convert form to formData
function prepareFormData(): FormData {
  const formData = new FormData();
  formData.append("title", form.value.title);
  formData.append("description", form.value.description);
  if (type.value == "file") {
    formData.append("file", form.value.file);
  } else {
    formData.append("link", form.value.link);
  }
  return formData;
}


const save = async () => {
  isLoading.value = true;
  await useCandidate().createPortfolio(prepareFormData());
  // await useCandidate().getProfile();
  addMode.value = false;
  isLoading.value = false;
};

const deletePortfolio = async (id: number) => {
  isLoading.value = true;
  await useCandidate().deletePortfolio(id);
  // await useCandidate().getProfile();
  isLoading.value = false;
};

function openNewTab(url: string) {
  window.open(url, "_blank")?.focus();
}
</script>
