<template>
  <div id="candidate-dashboard-application" transition-style="in:circle:top-left">
    <CandidateDashboardHeader title="My Applications" subtitle="Keep track of your applications" />
    <div id="dashboard-applications-wrapper" class="p-6 rounded-lg bg-white">
      <!-- Desktop view -->
      <div class="hidden md:block">
        <div class="flex justify-between items-center">
          <p class="my-3">My Applied Jobs</p>
        </div>

        <!-- table header -->

        <div class="mt-4 p-4 px-6 grid grid-cols-[7fr_2fr_2fr] text-primary text-sm bg-info rounded-lg">
          <p>Job title</p>
          <p>Date Applied</p>
          <p>Status</p>
        </div>

        <!-- Job -->
        <div v-for="i in 3" v-if="isLoading"
          class="mt-4 p-4 px-6 grid grid-cols-[7fr_2fr_2fr] items-center animate__animated h-16 bg-gray-100 animate-pulse rounded-lg" />
        <div v-if="applicationsData.length == 0" class="mt-4 p-4 px-6 grid grid-cols-[7fr_2fr_2fr] items-center">
          <div>
            No applications found.
            <nuxt-link class="text-primary" to="/jobs">Find a job now!</nuxt-link>
          </div>
        </div>
        <div v-for="application in applicationsData" class="mt-4 p-4 px-6 grid grid-cols-[7fr_2fr_2fr] items-center">
          <div class="flex gap-6 items-center">
            <img :src="directusAssetsUrl(application.job.company.logo)" alt="job title"
              class="w-12 h-12 rounded-lg object-contain" />
            <div class="">
              <p>{{ application.job.title }}</p>
              <div class="mt-1 flex items-center gap-1 text-xs text-accent">
                <Icon name="heroicons:briefcase" class="w-4 h-4" />
                <span>{{ application.job.company.name }}</span>
                <Icon name="heroicons:map-pin" class="ml-2 w-4 h-4" />
                <span>{{ application.job.company.city }}</span>
              </div>
            </div>
          </div>

          <p class="text-sm">
            {{ dayjs(application.date_created).format("DD MMM YYYY") }}
          </p>

          <p class="text-sm capitalize">{{ application.status }}</p>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const { directusAssetsUrl } = useHelper();

const { $directus, $readMe } = useNuxtApp();

const data = await $directus.request(
  $readMe({
    fields: [
      "candidate_profile.applications.*",
      "candidate_profile.applications.job.*",
      "candidate_profile.applications.job.company.*",
    ],
  })
);

const applicationsData = computed(() => data?.candidate_profile?.applications || []);

</script>
