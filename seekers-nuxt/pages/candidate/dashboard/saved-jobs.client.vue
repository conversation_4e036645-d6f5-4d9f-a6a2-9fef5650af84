<template>
  <div
    id="candidate-dashboard-saved-jobs"
    transition-style="in:circle:top-left"
  >
    <CandidateDashboardHeader
      title="Saved Jobs"
      subtitle="Save jobs to keep tabs on them. You can also apply to them later."
    />
    <div
      class="rounded-lg border p-2 bg-white"
      :class="{ 'animate-pulse': isLoading }"
    >
      <p class="font-semibold p-4 pb-2">My Saved Jobs</p>

      <div class="grid gap-4 p-4">
        <div
          v-for="i in 3"
          v-if="isLoading && !savedJobs"
          class="flex gap-4 w-full items-center border rounded-lg p-4"
          :class="{ 'animate-pulse': isLoading }"
        >
          <div class="w-16 h-16 md:w-24 md:h-24 rounded-full bg-gray-300" />
          <div>
            <div class="w-32 h-4 bg-gray-300 rounded" />
            <div class="w-24 h-4 bg-gray-300 rounded mt-2" />
            <div class="w-16 h-4 bg-gray-300 rounded mt-2" />
          </div>
        </div>

        <div
          v-for="job in savedJobs"
          class="flex gap-4 w-full items-center border rounded-lg p-4 relative pr-10"
          :class="{ 'animate-pulse': isLoading }"
        >
          <img
            :src="directusAssetsUrl(job.company.logo)"
            alt="company logo"
            class="hidden md:block object-contain w-16 h-16 md:w-24 md:h-24 rounded-full bg-white cursor-pointer"
            @click="navigateTo(`/jobs/${job.slug}`)"
          />
          <div class="flex flex-col gap-1">
            <nuxt-link :to="`/jobs/${job.slug}`"
              ><p class="font-semibold">{{ job.title }}</p></nuxt-link
            >
            <div class="flex flex-col md:flex-row gap-2 md:gap-4 text-xs">
              <p class="text-primary">{{ job.industry?.name }}</p>
              <div>
                <Icon name="heroicons:map-pin" class="w-4 h-4" />
                {{ job.state ? job.state : job.company.state }}
              </div>
              <div>
                <Icon name="heroicons:map-pin" class="w-4 h-4" />
                RM{{ job.salary_min }} - RM{{ job.salary_max }}
              </div>
            </div>
          </div>
          <button
            class="btn btn-error btn-sm btn-circle md:ml-auto md:mr-4 absolute right-2 top-2 md:static"
            :disabled="pending"
            @click="deleteItem(job.id)"
          >
            <Icon name="heroicons:trash" class="w-4 h-4" />
          </button>
        </div>
        <div
          v-if="savedJobs?.length === 0"
          class="flex gap-4 w-full items-center border rounded-lg p-4"
        >
          <div class="h-16 md:h-24" />
          <div>
            You have no jobs saved.<br />
            <nuxt-link to="/jobs" class="text-primary"
              >Browse Latest Jobs</nuxt-link
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const { directusAssetsUrl } = useHelper()
const { getProfile, savedJobs, unsaveJob } = useCandidate();

async function deleteItem(jobId) {
  await unsaveJob(jobId);
  await getProfile();
}
</script>
