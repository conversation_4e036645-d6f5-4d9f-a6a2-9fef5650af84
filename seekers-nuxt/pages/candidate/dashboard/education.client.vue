<template>
  <div id="candidate-dashboard-education">
    <CandidateDashboardHeader title="My Education"
      subtitle="Education are automatically sorted based on each start date" />
    <div class="bg-white p-6 rounded-lg">
      <div transition-style="in:circle:top-left">
        <template v-if="!isLoading">
          <CandidateDashboardEducationItem v-for="education in educations" :key="education.id" :value="education"
            editAllowed />
          <CandidateDashboardEducationItem :value="{}" lastStep addNew />
        </template>
        <div v-else>
          <span class="loading loading-ring loading-lg" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});

const { educations } = useCandidate();

</script>
