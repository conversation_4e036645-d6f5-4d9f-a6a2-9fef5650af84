<template>
  <div id="candidate-dashboard-work" transition-style="in:circle:top-left">
    <CandidateDashboardHeader
      title="My Work & Experience"
      subtitle="Work experiences are automatically sorted based on each start date"
    />
    <div class="bg-white p-6 rounded-lg">
      <div transition-style="in:circle:top-left">
        <CandidateDashboardWorkExperienceItem
          v-for="workExperience in workExperiences"
          :key="workExperience.id"
          :value="workExperience"
          :editAllowed="true"
        />
        <CandidateDashboardWorkExperienceItem
          :value="{}"
          lastStep
          addNew
        />
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: "candidate",
  middleware: "candidate-auth",
});
const { workExperiences } = useCandidate()
</script>
