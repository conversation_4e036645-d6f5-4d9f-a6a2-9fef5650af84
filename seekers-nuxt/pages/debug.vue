<template>
  <div>
    Authenticated: {{ authenticated }}<br/>
    <button @click="login('<EMAIL>','demodemo')">Login</button><br/>
    <button @click="logout">Logout</button><br />
    <button @click="tryGetProfile">Get Profile</button><br />
    <!-- <button @click="setToken('wrong lolz')">Set Token</button><br /> -->
    <div class="m-20">
      <p class="text-lg font-semibold">Candidate Profile</p>
      {{ candidateProfile ?? "No Data" }}
    </div>
    <div class="m-20">
      <p class="text-lg font-semibold">Recruiter Profile</p>
      {{ recruiterProfile ?? "No Data" }}
    </div>
    <div class="m-20">
      <p class="text-lg font-semibold">User Profile</p>
      {{ profile }}
    </div>
  </div>
</template>

<script setup lang="ts">
const directusRefreshToken = useCookie('directus_refresh_token')
const { login, logout, getProfile, profile, authenticated} = useUser();
const { profile: candidateProfile } = useCandidate();
const { profile: recruiterProfile } = useRecruiter();

const tryGetProfile = async () => {
  try {
    await getProfile()
  } catch (error) {
    console.log(error);
  }
}
</script>
