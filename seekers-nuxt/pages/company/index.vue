<template>
  <div id="companies-page">
    <CandidatePageHeader class="w-full text-center bg-info px-4 py-10" title="Companies" subtitle="Find Companies" />
    <div class="max-w-7xl mx-auto grid sm:grid-cols-[2fr_3fr] lg:grid-cols-[1fr_3fr]">
      <CompanySearchFilter class="bg-info p-6 pt-0 lg:p-8 lg:pb-40" />
      <div id="search-results-list" class="p-8">
        <div class="flex items-center gap-2 flex-wrap justify-center md:justify-start">
          <p class="text-sm w-40" v-if="(maxPages ?? 0) > 0">
            Showing
            <span class="font-semibold">{{ searchFilter?.page }}</span> of
            <span class="font-semibold">{{ maxPages }}</span> pages
          </p>

          <nuxt-link v-if="searchFilter?.page > 1" class="btn btn-info btn-sm"
            :to="`/jobs?page=${searchFilter?.page - 1}`" @click="searchFilter.page = searchFilter.page - 1">
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-left" class="h-4 w-4" />
          </div>
          <nuxt-link v-if="searchFilter?.page < (maxPages ?? 1)" class="btn btn-info btn-sm"
            :to="`/jobs?page=${searchFilter?.page + 1}`" @click="searchFilter.page = searchFilter.page + 1">
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </nuxt-link>
          <div v-else class="btn btn-disabled btn-sm">
            <Icon name="heroicons:chevron-right" class="h-4 w-4" />
          </div>

          <select id="sort-selector" v-model="searchFilter.sort" name="sort"
            class="select select-sm select-accent outline-never md:ml-auto border-base-200">
            <option value="date_updated">Latest</option>
            <option value="-date_updated">Oldest</option>
          </select>
          <select id="perpage-selector" v-model="searchFilter.limit" name="display"
            class="select select-sm select-accent outline-never border-base-200" @change="searchFilter.page = 1">
            <option v-for="option in DISPLAY_PER_PAGE_OPTIONS" :value="option.value">
              {{ option.label }}
            </option>
          </select>
          <p class="text-xs">per page</p>
        </div>
        <CompanyCardsList :company-list="data" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const { searchFilter, searchWithFilter, searchResultPages, syncRouteWithFilter } = useCompanies();
syncRouteWithFilter(route);

// LOAD DATA
const { data } = await searchWithFilter
const { data: maxPages } = await searchResultPages

</script>
