<template>
  <div id="company-details-page">
    <!-- HEADER -->
    <div class="bg-gradient-to-r from-[#E5EBF5]/60 via-[#F5F7FC]/60 to-success/60 py-4 md:py-12">
      <div class="md:max-w-7xl md:mx-auto mx-4 py-4">
        <div class="flex items-center">
          <img :src="directusAssetsUrl(companyData?.logo)" :alt="companyData?.name + ' Image'"
            class="w-20 h-20 rounded object-contain" />
          <div class="text-xs w-full ml-4">
            <h1 class="font-semibold text-lg leading-6">
              {{ companyData?.name }}
            </h1>
            <div class="flex items-end justify-between">
              <div>
                <div class="md:flex md:items-center md:gap-2 md:my-1 mt-2">
                  <div class="flex text-gray-500">
                    <Icon name="heroicons:map-pin" class="w-4 h-4 mr-2" />
                    <p>
                      {{ companyData?.city }}
                    </p>
                  </div>
                  <div class="flex text-gray-500 mt-1 md:mt-0">
                    <Icon name="heroicons:briefcase" class="w-4 h-4 mr-2" />
                    <p>
                      {{ companyData?.industry.name }}
                    </p>
                  </div>
                </div>
                <p v-if="companyData?.jobs.length > 0" class="badge badge-success text-xs mt-2 md:mt-1">
                  {{ companyData?.jobs.length }} Jobs Available
                </p>
              </div>

              <div v-if="profile">
                <button v-if="!isFollowed" class="btn btn-success" @click="follow">
                  <Icon name="heroicons:bell" />
                </button>

                <button v-else class="btn" @click="unfollow">
                  <Icon name="heroicons:bell-solid" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto py-12">
      <div class="grid md:grid-cols-[5fr_2fr] gap-6 mb-12 mx-4 md:mx-0">
        <!-- LEFT SIDE -->
        <div>
          <v-template v-if="companyData?.overview">
            <p class="font-semibold mb-4">Who we are?</p>
            <div class="text-gray-500 mb-4 break-words" v-html="cleanHtml(companyData?.overview)" />
          </v-template>

          <v-template v-if="companyData?.highlight">
            <p class="font-semibold mb-4">Highlights</p>
            <div class="text-gray-500 mb-4 break-words" v-html="companyData?.highlight" />
          </v-template>
          <!-- <div v-if="companyData?.company_images?.length > 0" class="flex gap-4 overflow-x-auto my-4 scrollbar-hide">
            <img v-for="img in companyData?.company_images" :key="img.id" :src="img.image_url" :alt="img.caption"
              class="h-64 rounded-md object-cover">
          </div> -->

          <!-- OPEN JOBS DESKTOP -->
          <div id="related-jobs" class="mt-12 hidden md:block">
            <h3 class="font-semibold mb-4">Available Jobs</h3>
            <CandidateHorizontalJobCard v-for="job in companyData.jobs" v-if="Object.keys(companyData?.jobs).length > 0"
              :job="job" />
            <div v-else class="border rounded-lg w-max p-8 pr-20">
              No open positions available currently
            </div>
          </div>
        </div>

        <!-- RIGHT SIDE -->
        <div>
          <div class="bg-info rounded-md p-8 mb-8">
            <div class="flex items-center">
              <p class="font-semibold">{{ companyData?.name }}</p>
            </div>
            <div class="grid items-center text-sm gap-4 mt-4">
              <div class="flex justify-between">
                <p class="font-semibold">Primary Industry:</p>
                <p class="text-end">{{ companyData?.industry.name }}</p>
              </div>
              <div v-if="companyData?.size != null" class="flex justify-between">
                <p class="font-semibold">Company size:</p>
                <p class="text-end">{{ companyData?.size }}</p>
              </div>
              <div v-if="companyData?.state && companyData?.state !== 'null'" class="flex justify-between">
                <p class="font-semibold">Location:</p>
                <p class="text-end">{{ companyData?.state }}</p>
              </div>
              <div v-if="companyData?.reg_no" class="flex justify-between">
                <p class="font-semibold">Company Registration:</p>
                <p class="text-end">{{ companyData?.reg_no }}</p>
              </div>
            </div>
          </div>

          <div v-if="companyData?.location_map !== null" class="bg-info p-8 rounded-md">
            <p class="font-semibold mb-4">Job Location</p>
            <div class="h-48">
              <iframe class="w-full h-full rounded-md" width="100%" height="250" frameborder="0" scrolling="no"
                marginheight="0" marginwidth="0"
                :src="`https://maps.google.com/maps?q=${companyData?.location_map.coordinates[1]},${companyData?.location_map.coordinates[0]}&z=15&output=embed`"></iframe>
            </div>
          </div>
        </div>

        <!-- OPEN JOBS MOBILE -->
        <div id="related-jobs" class="md:hidden">
          <h3 class="font-semibold mb-4">Available Jobs</h3>
          <CandidateHorizontalJobCard v-for="job in companyData.jobs" v-if="Object.keys(companyData?.jobs).length > 0"
            :job="job" />
          <div v-else class="border rounded-lg w-max p-8">
            No open positions available currently
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const { profile } = useCandidate();
const { directusAssetsUrl } = useHelper();
const { getBySlug } = useCompanies();
const company_slug = (route.params as { company_slug: string }).company_slug
const companyData = await getBySlug(company_slug, {
  fields: ["*", "industry.name", "jobs.*", "jobs.company.logo"]
});

const isFollowed = computed(() => {
  if (!profile.value?.companies_followed) return false;
  return profile.value?.companies_followed.some((company_id) => company_id === companyData.id);
})

const follow = async () => {
  await useCandidate().followCompany(companyData.id);
}

const unfollow = async () => {
  await useCandidate().unfollowCompany(companyData.id);
}
</script>

<style scoped>
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

pre,
.ql-syntax {
  width: 100% !important;
}
</style>
