<template>
  <div class="text-center p-8 lg:12">
    <h1 class="font-semibold">Job Application</h1>
    <div class="divider max-w-2xl mx-auto" />
    <img :src="directusAssetsUrl(jobData?.company?.logo)"
      class="mx-auto border rounded-full w-32 h-32 object-contain mt-4" />
    <p class="-mb-1 mt-4">{{ jobData?.company?.name }}</p>
    <h2 class="font-semibold">{{ jobData?.title }}</h2>
    <p class="text-sm text-accent mt-6">
      Please ensure all your info are correct and filled up to ensure successful
      application. If not, please
      <nuxt-link to="/candidate/dashboard/settings" class="font-semibold">update your profile.</nuxt-link>
    </p>

    <div class="divider max-w-2xl mx-auto my-8" />
    <div v-if="formStep === 1">
      <!-- Basic Info Form -->
      <div class="max-w-2xl mx-auto grid gap-4">
        <h2>Basic Info</h2>
        <!-- name -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text text-accent">Full Name</div>
          </label>
          <FormKit v-model="form.name" name="Name" type="text" class="input input-bordered text-sm"
            placeholder="Malaysia" validation="required|length:5" />
        </div>

        <!-- gender -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Gender</div>
          </label>
          <FormKit v-model="form.gender" name="Gender" type="select" class="select select-bordered text-sm w-full">
            <option value="male">Male</option>
            <option value="female">Female</option>
          </FormKit>
        </div>

        <!-- mobile -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Mobile</div>
          </label>
          <FormKit v-model="form.mobile" name="Mobile" type="text" class="select select-bordered text-sm w-full" />
        </div>

        <!-- nationality -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label for="nationality" class="label">
            <div class="label-text text-accent">Nationality</div>
          </label>
          <FormKit v-model="form.is_local" name="Nationality" type="select" class="input input-bordered text-sm"
            placeholder="Malaysian" validation="required">
            <option :value="true">Malaysian</option>
            <option :value="false">Expatriate</option>
          </FormKit>
        </div>

        <!-- email -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label for="email" class="label">
            <div class="label-text text-accent">Email</div>
          </label>
          <FormKit v-model="form.email" name="Email" type="email" disabled class="input input-bordered text-sm"
            placeholder="" />
        </div>

        <!-- current salary -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Current Salary (RM)</div>
          </label>
          <FormKit name="Current Salary" type="number" v-model="form.salary_current"
            class="input input-bordered text-sm" placeholder="3000" validation="required" :validation-messages="{
              required:
                'Please enter your current salary in RM. If you are not working, please enter 0',
            }" />
        </div>

        <!-- expected salary -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Exp. Salary (RM)</div>
          </label>
          <FormKit name="Expected Salary" type="number" v-model="form.salary_expected"
            class="input input-bordered text-sm" placeholder="5000" validation="required|length:4" :validation-messages="{
              required: 'Please enter your expected salary in RM.',
            }" />
        </div>

        <!-- notice period -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Notice Period</div>
          </label>
          <FormKit v-model="form.notice_period" name="Notice Period" type="select"
            class="select select-bordered text-sm w-full" validation="required">
            <option v-for="item in NOTICE_PERIODS" :value="item.value">
              {{ item.label }}
            </option>
          </FormKit>
        </div>

        <!-- birthday -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Birth Date</div>
          </label>
          <FormKit name="Birthdate" type="date" v-model="form.birthday" class="input input-bordered text-sm"
            placeholder="1999-12-31" :min="dayjs().subtract(18, 'year').format('YYYY-MM-DD')" max="2050-12-31"
            validation="required" :validation-messages="{
              required: 'Please enter your year of birth',
            }" />
        </div>

        <!-- location -->
        <div class="grid lg:grid-cols-[2fr_6fr] lg:gap-4 text-sm">
          <label class="label">
            <div class="label-text">Location</div>
          </label>
          <FormKit v-model="form.state" name="Location" type="select" class="input input-bordered text-sm"
            placeholder="Pick a State" validation="required" :validation-messages="{
              required: 'Pick where you currently live',
            }">
            <option v-for="location in MALAYSIAN_STATES" :value="location.value">
              {{ location.label }}
            </option>
          </FormKit>
        </div>
      </div>

      <div class="divider max-w-2xl mx-auto my-8" />

      <div class="max-w-2xl mx-auto grid gap-8">
        <h2>Resume | CV</h2>
        <!-- Selector for new of existing resume -->
        <div class="grid grid-cols-2 gap-4">
          <button class="btn" :class="{ 'btn-info': !useExistingCV }" type="button" @click="useExistingCV = false">
            Upload New
          </button>
          <button class="btn" :class="{ 'btn-primary': useExistingCV }" type="button" :disabled="!profile.cv"
            @click="useExistingCV = true">
            Use Existing CV
          </button>
        </div>

        <div v-if="!useExistingCV">
          <label for="cv-upload"
            class="flex flex-col items-center justify-center border w-full h-32 rounded-lg border-dashed cursor-pointer hover:border-accent transition-colors">
            <Icon name="heroicons:document-plus-solid" class="h-8 w-8 text-accent mb-2" />
            <p>
              {{
                cvToUpload?.length > 0
                  ? cvToUpload[0]?.name
                  : "Click to add file"
              }}
            </p>
          </label>
          <div class="hidden">
            <FormKit id="cv-upload" v-model="cvToUpload" type="file" accept=".pdf" />
          </div>
        </div>
        <div v-else
          class="flex flex-col items-center justify-center border w-full h-32 rounded-lg cursor-pointer hover:border-black"
          @click="previewCV">
          <Icon name="heroicons:document-solid" class="h-8 w-8 text-accent mb-2" />
          <p>Click to Preview CV</p>
        </div>

        <div class="divider my-8" />
      </div>

      <button class="btn btn-primary px-12 w-full max-w-2xl" type="button" @click="step1Next">
        Next : {{ hasQuestions ? "Screening Questions" : "Review" }}
      </button>
    </div>
    <!-- Job Screening Questions -->
    <div v-if="formStep === 2" id="screening-questions" class="max-w-2xl mx-auto">
      <h2 class="mb-8">Screening Questions</h2>
      <!-- <div v-for="q in jobData?.job_screening_questions">
        Text Question

        <div v-if="q.type == 'text'">
          <label class="label">{{ q.question }} </label>
          <FormKit
            v-model="q.answer"
            :name="'question_' + q.id"
            validation="required"
            :validation-messages="{ required: 'Please answer this question.' }"
          />
        </div>

        Education Level
        <div v-if="q.type == 'education_level'">
          <label class="label"
            ><div class="label-text text-accent">Education Level</div>
          </label>
          <FormKit
            v-model="q.answer"
            type="select"
            validation="required"
            placeholder="Select Education Level"
          >
            <option v-for="level in educationLevelOptions" :value="level.value">
              {{ level.label }}
            </option>
          </FormKit>
        </div>
      </div> -->
      <div class="divider mt-12 mb-8" />
      <div class="grid gap-2 grid-cols-[2fr_5fr]">
        <button class="btn px-4" @click="formStep--">Back</button>
        <!-- <button class="btn btn-primary px-12 w-full" @click="step2Next">
          Next: Review
        </button> -->
      </div>
    </div>

    <!-- Preview -->
    <div v-if="formStep === 3" class="max-w-xl mx-auto">
      <h2 class="mb-8">Review Details</h2>
      <div v-for="(value, key) in form" class="text-left max-w-md mx-auto mt-4">
        <div v-if="
          ![
            'cv',
            'job_screening_answers',
            'user_id',
            'job_id',
            'is_local',
            'open_to_work',
          ].includes(key)
        " class="grid md:grid-cols-[1fr_1fr] sm:gap-2">
          <p class="font-semibold capitalize">
            {{ key.replaceAll("_", " ") }} :
          </p>
          <p>{{ value }}</p>
        </div>
      </div>
      <div class="grid md:grid-cols-[1fr_1fr] sm:gap-2 text-left max-w-md mx-auto mt-4">
        <p class="font-semibold capitalize">CV :</p>
        <p>{{ !useExistingCV ? cvToUpload[0].name : "Use Existing CV" }}</p>
      </div>
      <!-- <pre>form: {{ form }}</pre> -->
      <div class="grid grid-cols-[2fr_5fr] gap-4 my-12">
        <button class="btn w-full" @click="formStep = hasQuestions ? 2 : 1">
          Back
        </button>
        <button class="btn btn-primary w-full" @click="submitApplication">
          Submit Application
        </button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import Swal from "sweetalert2";
import dayjs from "dayjs";
useSeoMeta({
  robots: "noindex",
});
definePageMeta({
  middleware: "candidate-auth",
});
const route = useRoute();
const referrerUID = useCookie("referrer_uid");
const { directusAssetsUrl } = useHelper();
const { profile } = useCandidate();

const formStep = ref<number>(1);
const loading = ref<boolean>(false);
const useExistingCV = useState(() => profile.cv);
const cvToUpload = ref<any>(null);

const hasQuestions = computed(() => {
  // if (jobData?.value?.job_screening_questions)
  //   return jobData?.value?.job_screening_questions?.length > 0;
  return false;
});

const form = ref({
  name: profile.value?.name ?? null,
  email: profile.value?.email ?? null,
  mobile: profile.value?.mobile ?? null,
  gender: profile.value?.gender ?? "Male",
  birthday: profile.value?.birthday ?? "1999-12-31",
  state: profile.value?.state ?? null,
  salary_current: profile.value?.salary_current ?? null,
  salary_expected: profile.value?.salary_expected ?? null,
  cv: profile.value?.cv ?? null,
  is_local: profile.value?.is_local ?? true,
  // job_id: jobData.value?.id || null,
  job_screening_answers: "",
  notice_period: profile.value?.notice_period ?? "2-weeks",
  user_id: referrerUID.value ? parseInt(referrerUID?.value) : 1,
  // open_to_work: 1,
});

function previewCV() {
  window.open(baseUrl + "/assets/" + profile.cv.id, "_blank");
}

function step1Next() {
  if (
    !form.value.name ||
    !form.value.gender ||
    !form.value.is_local ||
    !form.value.birthday ||
    !form.value.email ||
    !form.value.salary_current ||
    !form.value.salary_expected ||
    !form.value.notice_period ||
    !form.value.state ||
    !form.value.mobile ||
    (!useExistingCV.value && cvToUpload.value?.length == 0)
  ) {
    Swal.fire({
      icon: "error",
      title: "Oops...",
      text: "Please fill in all the required fields.",
      confirmButtonColor: "hsl(var(--a))",
    });
    return;
  }

  if (!useExistingCV.value) {
    form.value.cv = cvToUpload.value[0].file;
  }
  formStep.value = hasQuestions.value ? 2 : 3;
  scrollToTop();
}

function step2Next() {
  loading.value = true;
  const job_screening_answers: any = {};
  // jobData?.value?.job_screening_questions?.forEach((q) => {
  //   job_screening_answers[q.id] = q.answer;
  // });
  form.value.job_screening_answers = JSON.stringify(job_screening_answers);
  formStep.value = 3;
}

async function submitApplication() {
  // if (useExistingCV.value) {
  //   form.value.cv = null;
  // }
  // const { error } = await useCandidate().submitJobApplication(form.value);
  // if (error.value?.status == 401) {
  //   Swal.fire({
  //     icon: "error",
  //     title: "You have already applied for this job.",
  //     text:
  //       error?.value?.status == 401
  //         ? "Application already exists. A recruiter may have submitted your application."
  //         : "Please try again later.",
  //     confirmButtonColor: "hsl(var(--a))",
  //   });
  //   return;
  // }
  // navigateTo("/jobs/" + jobData?.value?.slug + "/completed");
}
</script>
