<template>
  <div id="candidate-job-details-page" class="">
    <!-- HEADER -->
    <div class="bg-gradient-to-r from-[#E5EBF5]/60 via-[#F5F7FC]/60 to-success/60 p-2 py-6 md:py-12">
      <div class="grid md:flex gap-4 max-w-7xl mx-auto">
        <div class="flex gap-4 items-center">
          <img :src="directusAssetsUrl(jobData?.company?.logo)" :alt="jobData?.company?.name + ' Logo'"
            class="w-20 h-20 rounded object-contain" />

          <div class="grid gap-2">
            <h1 class="font-semibold text-lg leading-6">
              {{ jobData?.title }}
            </h1>
            <div class="grid gap-2 md:flex text-xs">
              <p class="text-gray-500">
                <Icon name="heroicons:briefcase" class="w-4 h-4 mr-1" />{{
                  jobData?.role.name
                }}
              </p>
              <p v-if="jobData?.state != null" class="text-gray-500">
                <Icon name="heroicons:map-pin" class="w-4 h-4 mr-1" />{{
                  jobData.state
                }}
              </p>
              <p class="text-gray-500">
                <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />{{
                  getRelatedDate(jobData?.date_updated as string)
                }}
              </p>
              <p class="text-gray-500">
                <Icon name="heroicons:banknotes" class="w-4 h-4 mr-1" />{{
                  `RM ${jobData?.salary_min ?? "N/A"} - RM ${jobData?.salary_max ?? "N/A"}`
                }}
              </p>
            </div>
            <div class="flex gap-3">
              <p v-if="jobData?.position_type == 'permanent'" class="badge badge-success text-xs">
                Full Time
              </p>
              <p v-if="jobData?.position_type == 'permanent'" class="badge badge-info text-xs">
                Permanent
              </p>
              <p v-if="closingSoon(jobData?.date_created || '')" class="badge badge-warning text-xs">
                Urgent
              </p>
              <p v-else class="hidden badge badge-warning text-xs" />
            </div>
          </div>
        </div>
        <div v-if="jobData?.status !== 'closed'" class="md:ml-auto flex justify-center items-center gap-4">
          <nuxt-link :to="`/jobs/${jobSlug}/apply`" class="btn btn-primary w-52">Apply for job</nuxt-link>
          <!-- <button
            v-if="!isSaved"
            class="btn btn-success"
            :disabled="pending || !candidateData"
            @click="saveJob"
          >
            <Icon name="heroicons:bookmark" />
          </button>
          <button
            v-else
            class="btn btn-danger"
            :disabled="pending"
            @click="unsaveJob"
          >
            <Icon name="heroicons:bookmark-solid" />
          </button> -->
        </div>
      </div>
    </div>

    <!-- LEFT SIDE -->
    <div class="max-w-7xl mx-auto grid md:grid-cols-[5fr_3fr] py-12">
      <!-- JOB DESCRIPTION, RESPONSILITIES, ETC -->
      <div class="text-sm mx-4">
        <div v-if="jobData?.description" class="mb-6">
          <h2 class="font-semibold mb-4">Job Description</h2>
          <p>
            {{ jobData?.description }}
          </p>
        </div>
        <div v-if="jobData.responsibilities" class="mb-6">
          <h2 class="font-semibold mb-4">Key Responsibilities</h2>
          <NuxtMarkdown class="pl-4" :source="jobData?.responsibilities" />
        </div>
        <div v-if="jobData?.must_have" class="mb-6">
          <h2 class="font-semibold mb-4">Skills & Experiences</h2>
          <NuxtMarkdown class="pl-4" :source="jobData?.must_have" />
        </div>

        <!-- SHARE BUTTONS -->
        <div class="flex items-center gap-2">
          <p class="font-semibold mr-2">Share this job</p>

          <!-- BUTTONS -->
          <!-- <pre>{{ route }}</pre> -->
          <nuxt-link
            :to="`https://www.facebook.com/sharer/sharer.php?u=${jobData?.fullpath}&title=${jobData?.title}&description=${jobData?.description}`"
            class="flex items-center btn btn-circle btn-primary">
            <Icon name="bxl:facebook" class="w-5 h-5" />
          </nuxt-link>
          <nuxt-link :to="`https://twitter.com/intent/tweet?text=${jobData?.title}&url=${jobData?.fullpath}`"
            class="flex items-center btn btn-circle bg-[#1967D2] text-white">
            <Icon name="bxl:twitter" class="w-5 h-5" />
          </nuxt-link>
          <nuxt-link :to="`https://www.linkedin.com/sharing/share-offsite/?url=${jobData?.fullpath}`"
            class="flex items-center btn btn-circle bg-[#D93025] text-white">
            <Icon name="bxl:linkedin-square" class="w-5 h-5" />
          </nuxt-link>
          <nuxt-link
            :to="`https://api.whatsapp.com/send?text=${jobData?.title}%0D%0A${jobData?.fullpath}%0D%0A${jobData?.description}`"
            class="flex items-center btn btn-circle bg-[#F9AB00] text-white">
            <Icon name="bxl:whatsapp" class="w-5 h-5" />
          </nuxt-link>
        </div>

        <!-- RELATED JOBS DESKTOP -->
        <div id="related-jobs" class="mt-12 hidden md:block">
          <h3 class="font-semibold mb-6">Related Jobs</h3>
          <CandidateHorizontalJobCard v-for="relatedJob in relatedJobsData" v-if="relatedJobsData"
            :job="relatedJob" />
          <div v-else class="border rounded-lg w-max p-8 pr-20">
            No related jobs at the moment
          </div>
        </div>
      </div>

      <!-- RIGHT SIDE -->
      <div class="m-4 md:m-0">
        <div class="bg-info rounded-md p-8">
          <div class="mb-12">
            <p class="font-semibold mb-4">Job Conditions:</p>
            <div class="grid md:grid-cols-2 text-sm gap-4">
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="game-icons:sands-of-time" class="h-6 w-6 text-primary" />
                </div>

                <div class="ml-2">
                  <p class="font-semibold">Probation Period:</p>
                  <p>
                    {{
                      jobData?.probation_months == 0
                        ? "-"
                        : jobData?.probation_months + " months"
                    }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="fluent-mdl2:group" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Age:</p>
                  <p>
                    {{ jobData?.age_min }} -
                    {{ jobData?.age_max }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="clarity:avatar-line" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Job Type:</p>
                  <p class="capitalize">{{ jobData?.position_type || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:banknotes" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Allowances:</p>
                  <p>
                    {{ jobData?.allowances ? jobData?.allowances : "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:clock" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Hours:</p>
                  <p>{{ jobData?.working_hours || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:language" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Languages:</p>
                  <p v-for="language in jobData?.languages" class="capitalize break-all">
                    {{ language || "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:sun" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Days:</p>
                  <p>{{ jobData?.working_days || "-" }}</p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="bi:people" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Malaysia Only:</p>
                  <p>
                    {{ jobData?.allow_foreigner ? "No" : "Yes" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="heroicons:calendar" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Annual Leave:</p>
                  <p>
                    {{ jobData.annual_leave ? jobData.annual_leave + " days" : "-" }}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <div class="h-6 w-6">
                  <Icon name="vaadin:coin-piles" class="h-6 w-6 text-primary" />
                </div>
                <div class="ml-2">
                  <p class="font-semibold">Benefits:</p>
                  <p>
                    {{ jobData?.benefits || "-" }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <p class="font-semibold mb-2">Job Skills</p>
            <p v-for="tag in jobData.tags" class="btn mb-2 mr-2 w-fit gap-4 capitalize">
              {{ tag }}
            </p>
          </div>
        </div>

        <div class="bg-info rounded-md p-8 my-4 md:w-3/4">
          <div class="flex items-center">
            <img :src="directusAssetsUrl(jobData?.company?.logo)" alt="Company Logo"
              class="w-16 h-16 rounded object-contain mr-4" />
            <h2 class="font-semibold text-sm">{{ jobData?.company?.name }}</h2>
          </div>
          <div class="grid items-center text-sm gap-4 mt-4">
            <div class="flex gap-2 justify-between">
              <p class="font-semibold">Primary Industry:</p>
              <h3 class="text-end text-sm">
                {{ jobData?.industry?.name }}
              </h3>
            </div>
            <div v-if="!jobData?.is_confidential && jobData?.company?.size" class="flex gap-2 justify-between">
              <p class="font-semibold">Company size:</p>
              <p class="text-end">{{ jobData?.company?.size }}</p>
            </div>
            <div v-if="jobData?.company?.city != null" class="flex justify-between">
              <p class="font-semibold">Location:</p>
              <p class="text-end">{{ jobData?.company?.city }}</p>
            </div>
            <div v-if="!jobData?.is_confidential && jobData?.company?.reg_no" class="flex justify-between">
              <p class="font-semibold">Company Registration:</p>
              <p class="text-end">{{ jobData?.company?.reg_no }}</p>
            </div>
          </div>
          <nuxt-link :to="jobData?.is_confidential
            ? '#'
            : `/company/${jobData?.company?.slug}`
            " class="btn btn-success mt-4 w-full" :class="{
              'btn-disabled': jobData?.is_confidential,
            }">{{
              jobData.is_confidential
                ? "Company Confidential"
                : "View Company Profile"
            }}</nuxt-link>
        </div>
      </div>

      <!-- RELATED JOBS MOBILE -->
      <div id="related-jobs" class="m-4 md:m-0 md:hidden">
        <h3 class="font-semibold mb-6">Related Jobs</h3>
        <CandidateHorizontalJobCard v-for="relatedJob in relatedJobsData" v-if="relatedJobsData"
          :job="relatedJob" />
        <div v-else class="border rounded-lg w-max py-8 px-10 mx-auto">
          No related jobs at the moment
        </div>
      </div>
      Id: {{ jobData.id }}
      Role Id: {{ jobData.role.parent_id.id }}
      {{ relatedJobsStatus }}
    </div>
    <JobsReferATalent :jobSlug />
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
const route = useRoute();

const { directusAssetsUrl } = useHelper();
const { getBySlug, getRelatedJobs } = useJobs();

const jobSlug = (route.params as any).job_slug;
const jobData: Job = await getBySlug(jobSlug, {
  fields: ["*", "company.*", "company.industry.*", "role.*", "role.parent_id.name", "role.parent_id.id"]
});

if (!jobData || jobData.status !== "published") {
  throw createError({
    statusCode: 404,
    statusMessage: "Job not found",
  })
}

const { data: relatedJobsData, status: relatedJobsStatus } = getRelatedJobs(jobData.id, jobData.role.parent_id.id);


let googleRichSearchJobData = {};
if (jobData.status === "published") {
  googleRichSearchJobData = {
    "@context": "https://schema.org/",
    "@type": "JobPosting",
    title: jobData.title,
    description: jobData.description
      ? jobData.description
      : jobData.role.name,
    datePosted: jobData.date_updated || jobData.date_created,
    hiringOrganization: {
      "@type": "Organization",
      name: jobData.company.name,
      logo: jobData.company.logo,
    },
    baseSalary: {
      "@type": "MonetaryAmount",
      currency: "MYR",
      value: {
        "@type": "QuantitativeValue",
        minValue: jobData.salary_min,
        maxValue: jobData.salary_max,
      },
    },
    employmentType: jobData.work_type,
    jobLocation: {
      "@type": "Place",
      address: {
        "@type": "PostalAddress",
        addressLocality: jobData.company?.city,
        addressRegion: jobData.company?.state,
        postalCode: jobData.company?.post_code,
      },
    },
  };
}

useHead({
  link: [
    {
      rel: "canonical",
      href: () => `https://seekers.my/jobs/${jobData.slug}`,
    },
  ],
  script: [
    {
      type: "application/ld+json",
      innerHTML: JSON.stringify(googleRichSearchJobData),
    },
  ],
});
useSeoMeta({
  title: () => `${jobData.title} | Seekers`,
  ogTitle: () => `${jobData.title} | Seekers`,
  ogType: "website",
  ogUrl: () => `https://seekers.my/jobs/${jobData.slug}`,
  description: () =>
    `${jobData.description ||
    `${jobData.title} job position at ${jobData.company?.name}`
    }`,
  ogDescription: () =>
    `${jobData.description ||
    `${jobData.title} job position at ${jobData.company?.name}`
    }`,
  ogImage: () => `${jobData.company.logo}`,
});

// const { data: candidateData, pending, refresh } = useCandidate().getProfile();
// const save = useCandidate().saveJob;
// const unsave = useCandidate().unsaveJob;

// const { data: relatedJobsData } = useJobs().getRelatedJobs(
//   jobData.value?.id as number,
// );

// if route has 'uid', save it to using useCookie for 24hrs
if (route.query.uid) {
  const referrerUID = useCookie("referrer_uid", { maxAge: 60 * 60 * 24 });
  referrerUID.value = route.query.uid as string;
}

// async function saveJob() {
//   await save(jobData.value?.id as number);
//   await refresh();
// }
// async function unsaveJob() {
//   await unsave(jobData?.value?.id as number);
//   await refresh();
// }
// const isSaved = computed(() => {
//   return candidateData?.value?.fav_job_ids?.some(
//     (id: number) => id == jobData?.value?.id,
//   );
// });

function closingSoon(date: string) {
  // return true if date is more than 21 days old from today
  return dayjs().diff(dayjs(date), "day") > 21;
}
</script>
