<template>
  <div class="text-center p-8 lg:12 min-h-[70vh]">
    <h1 class="font-semibold">Application Submitted</h1>
    <div class="divider max-w-2xl mx-auto"/>
    <img
      :src="jobData?.company.logo_url"
      class="mx-auto border rounded-full w-32 h-32 object-contain mt-4"
    >
    <p class="-mb-1 mt-4">{{ jobData?.company?.name }}</p>
    <h2 class="font-semibold">{{ jobData?.title }}</h2>
    <div class="divider max-w-2xl my-8 mx-auto"/>
    <p class="mt-6">Congratulation on your first steps!</p>
    <p class="mt-6 max-w-xl mx-auto">
      To further <b>increase your chances</b> of landing a job,<br >make sure
      to <b>add Education, Work and Portfolios</b> to your profile.
    </p>

    <p class="mt-6 max-w-xl mx-auto">
      This helps the Hiring Manager of {{ jobData?.company.name }} to know more
      about you and your experiences.
    </p>
    <p class="mt-6 max-w-xl mx-auto">
      Other companies will also be able to see your profile and may contact you
      for other job opportunities.
    </p>
    <nuxt-link
      to="/candidate/dashboard/application"
      class="btn btn-primary px-12 mt-8 font-semibold"
      >Go to My Applications</nuxt-link
    >
  </div>
</template>
<script setup>
useSeoMeta({
  robots: "noindex",
});
const route = useRoute();
const jobData = await useJobs().getBySlug(route.params.job_slug);
</script>
