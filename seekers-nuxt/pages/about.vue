<template>
  <div id="about-page">
    <div class="min-h-[60vh] bg-base-200">
      <div
        class="bg-gradient-to-r from-[#E5EBF5]/60 via-[#F5F7FC]/60 to-success/60"
      >
        <h1
          class="text-3xl font-semibold py-10 px-6 text-center text-primary md:max-w-screen-lg md:m-auto"
        >
          About Us
        </h1>
        <div class="bg-white w-full">
          <div class="py-10 md:max-w-screen-lg m-auto">
            <div class="max-w-4xl mx-auto">
              <!-- IMAGES -->
              <AboutImagesGrid class="p-4 pt-0" />

              <!-- STATS -->
              <AboutStatsAndAbout />

              <!-- DEVELOPMENT DIRECTION -->
              <AboutDevelopmentDirection />
            </div>
          </div>
          <!-- DREAM JOB WAITING -->
          <AboutDreamJobWaiting />

          <div class="py-10 md:max-w-screen-lg m-auto">
            <div class="max-w-4xl mx-auto">
              <!-- VISION -->
              <AboutVisions />
            </div>
          </div>

          <!-- COMPANY IMAGES -->
          <!-- <HomepageHighlightedCompanies
            :highlightedJobs="highlightedJobs"
            :highlightedJobsPending="highlightedJobsPending"
            :latestJobs="latestJobs"
            :latestJobsPending="latestJobsPending"
            :latestTechJobs="latestTechJobs"
            :latestTechJobsPending="latestTechjobsPending"
          /> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// const { data: highlightedJobs, pending: highlightedJobsPending } = useFetch<{
//   data: Job[];
// }>("/api/jobs/highlighted?limit=6", {
//   key: "highlighted",
// });
// const { data: latestJobs, pending: latestJobsPending } = useFetch<{
//   data: Job[];
// }>("/api/jobs/latest", { key: "latest" });
// const { data: latestTechJobs, pending: latestTechjobsPending } = useFetch<{
//   data: Job[];
// }>("/api/jobs/latest-tech", {
//   key: "latest-tech",
// });
</script>
