<template>
  <section id="recruiter-dashboard-latest-jobs">
    <RecruiterDashboardHeader
      title="Digital / Technical Jobs"
      subtitle="Catch the latest opportunities"
    />
    <RecruiterDashboardJobsList
      :jobs="data?.data"
      title="Digital / Technical Jobs"
    />
  </section>
</template>
<script setup>
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});

const { data } = useJobs().recruiterLatestJobs();
</script>
