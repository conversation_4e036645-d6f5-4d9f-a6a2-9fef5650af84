<template>
  <div id="recruiter-dashboard-profile">
    <RecruiterDashboardHeader
      title="My Profile"
      subtitle="Update your profile to get more job opportunities."
    />

    <div class="bg-white rounded-md p-8 border">
      <div>
        <!--Profile Picture-->
        <div class="flex gap-8 items-center">
          <img
            id="img-preview"
            src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1672815271/website/recruiterpage/profile/default-profile-picture_onirx5.png"
            alt="image-preview"
            class="h-32 w-32 object-cover rounded-xl"
          >
          <div>
            <label class="text-sm btn btn-success px-8"
              >Change Image
              <input
                id="profile-img"
                required
                type="file"
                name="profile-img"
                onchange="document.getElementById('img-preview').src = window.URL.createObjectURL(this.files[0])"
                class="hidden"
                accept=".jpg, .png"
              >
            </label>
            <p class="text-xs mt-1">
              *Max file size is 1MB, only .jpg & .png are accepted.
            </p>
          </div>
        </div>

        <!--Profile Details-->

        <!-- name -->
        <form
          id="recruiter-profile-form"
          class="grid lg:grid-cols-2 gap-x-12 gap-y-2 mt-4"
        >
          <div class="text-sm">
            <label for="name" class="label">
              <span class="label-text">Name</span>
            </label>
            <input
              id="name"
              v-model="profileSettingsForm.name"
              required
              type="text"
              name="name"
              class="input input-bordered w-full"
              placeholder="John Doe"
            >
          </div>

          <!-- ic -->
          <div class="text-sm">
            <label for="identification_card" class="label">
              <span class="label-text">IC Number</span>
            </label>
            <input
              id="identification_card"
              v-model="profileSettingsForm.ic"
              required
              type="number"
              name="identification_card"
              class="input input-bordered w-full"
              placeholder="123456789012"
            >
          </div>

          <!-- mobile -->
          <div class="text-sm">
            <label for="mobile" class="label">
              <span class="label-text">Phone</span>
            </label>
            <input
              id="mobile"
              v-model="profileSettingsForm.mobile"
              required
              type="text"
              name="phone"
              class="input input-bordered w-full"
              placeholder="+60123456789"
            >
          </div>

          <!-- email -->
          <div class="text-sm">
            <label for="email" class="label">
              <span class="label-text">Email</span>
            </label>
            <input
              id="email"
              v-model="profileSettingsForm.email"
              required
              type="email"
              name="email"
              disabled
              class="input input-bordered w-full"
              placeholder="<EMAIL>"
            >
          </div>

          <!-- address -->
          <div class="text-sm">
            <label for="location" class="label">
              <span class="label-text">Address</span>
            </label>
            <input
              id="location"
              v-model="profileSettingsForm.location"
              required
              type="text"
              name="address"
              class="input input-bordered w-full"
              placeholder="123, Jalan 1, Taman 1, 12345, Kuala Lumpur, Malaysia"
            >
          </div>

          <div class="grid grid-cols-2 gap-4">
            <!-- Bank Name -->
            <div class="text-sm">
              <label for="bank_name" class="label">
                <span class="label-text">Bank Name</span>
              </label>
              <select
                id="bank_name"
                v-model="profileSettingsForm.bank_name"
                name="bank_name"
                class="select select-bordered w-full"
                placeholder="123, Jalan 1, Taman 1, 12345, Kuala Lumpur, Malaysia"
              >
                <option :value="null" disabled>Select One</option>
                <option v-for="bank in BANKS" :value="bank">
                  {{ bank }}
                </option>
              </select>
            </div>

            <!-- Bank Account Number -->
            <div class="text-sm">
              <label class="label">
                <span class="label-text">Account Number</span>
              </label>
              <input
                v-model="profileSettingsForm.bank_no"
                required
                type="text"
                name="bank_no"
                class="input input-bordered w-full"
                placeholder="********"
              >
            </div>
          </div>

          <!-- Specialization -->

          <div class="text-sm">
            <label for="specialization" class="label">  
              <span class="label-text">Specialization</span>
            </label>
            <CustomMultiselect
              v-model="specializationsMultivalue"
              :options="SPECIALIZATION_OPTIONS"
              placeholder="Pick up to 5 industries you specialize in"
            />
          </div>

          <!-- age -->
          <div class="text-sm">
            <label for="age" class="label">
              <span class="label-text">Age</span>
            </label>
            <input
              id="age"
              v-model="profileSettingsForm.age"
              required
              type="number"
              name="age"
              class="input input-bordered w-full"
              placeholder="23"
            >
          </div>

          <!-- Languages -->
          <div class="text-sm">
            <label for="languages" class="label">
              <span class="label-text">Languages</span>
            </label>
            <CustomMultiselect
              v-model="languagesMultivalue"
              :options="LANGUAGE_OPTIONS"
              placeholder="English, Malay, Mandarin, etc."
            />
          </div>

          <!-- Company Name -->
          <div class="text-sm">
            <label for="company_name" class="label">
              <span class="label-text"
                >Company Name
                <span class="text-xs text-accent"
                  >(If you are currently working)</span
                >
              </span>
            </label>
            <input
              id="company_name"
              v-model="profileSettingsForm.company_name"
              required
              type="text"
              name="company_name"
              class="input input-bordered w-full"
              placeholder="Your company name"
            >
          </div>

          <!-- Designation -->
          <div class="text-sm">
            <label for="designation" class="label">
              <span class="label-text">Designation</span>
            </label>
            <input
              id="designation"
              v-model="profileSettingsForm.designation"
              required
              type="text"
              name="designation"
              class="input input-bordered w-full"
              placeholder="Your designation"
            >
          </div>

          <!-- Social Links -->
          <div class="text-sm grid">
            <label class="label">
              <span class="label-text">Social Links</span>
            </label>
            <div class="flex gap-2 items-center">
              <label for="facebook"
                ><Icon name="bxl:facebook-circle" class="w-5 h-5"
              /></label>
              <input
                id="facebook"
                v-model="profileSettingsForm.facebook"
                class="input input-bordered flex-grow"
                type="text"
                name="facebook"
                placeholder="https://www.facebook.com/yourname"
              >
            </div>
            <div class="flex gap-2 items-center mt-2">
              <label for="instagram"
                ><Icon name="bxl:instagram" class="w-5 h-5"
              /></label>
              <input
                id="instagram"
                v-model="profileSettingsForm.instagram"
                class="input input-bordered flex-grow"
                type="text"
                name="instagram"
                placeholder="https://www.instagram.com/yourname"
              >
            </div>

            <div class="flex gap-2 items-center mt-2">
              <label for="linkedin"
                ><Icon name="bxl:linkedin" class="w-5 h-5"
              /></label>
              <input
                id="linkedin"
                v-model="profileSettingsForm.linkedin"
                class="input input-bordered flex-grow"
                type="text"
                name="linkedin"
                placeholder="https://www.linkedin.com/yourname"
              >
            </div>
            <div class="flex gap-2 items-center mt-2">
              <label for="twitter"
                ><Icon name="bxl:twitter" class="w-5 h-5"
              /></label>
              <input
                id="twitter"
                v-model="profileSettingsForm.twitter"
                class="input input-bordered flex-grow"
                type="text"
                name="twitter"
                placeholder="https://www.twitter.com/yourname"
              >
            </div>
          </div>
        </form>
        <button
          type="button"
          class="btn btn-primary w-full mt-8 lg:w-96"
          :disabled="loading"
          @click="updateProfile"
        >
          <span v-if="loading">Submitting... </span>
          <span v-else> Update Profile</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});

const helper = useHelper();

const { data: data } = await useRecruiter().getProfile();

const specializationsMultivalue = ref<object[]>([]);
const languagesMultivalue = ref<object[]>([]);
const loading = ref(false);

const profileSettingsForm = ref({
  age: data?.value.age || null,
  bank_name: data?.value.bank_name || null,
  bank_no: data?.value.bank_no || null,
  company_name: data?.value.company_name || null,
  designation: data?.value.designation || null,
  email: data?.value.email || "hidden",
  facebook: data?.value.facebook || null,
  ic: data?.value.ic || null,
  image: data?.value.image || null,
  instagram: data?.value.instagram || null,
  languages: data?.value.languages || null,
  linkedin: data?.value.linkedin || null,
  location: data?.value.location || null,
  mobile: data?.value.mobile || null,
  name: data?.value.name || null,
  is_local: data?.value.is_local || 1,
  nationality: data?.value.nationality || "Malaysian",
  password: data?.value.password || null,
  password_confirmation: data?.value.password_confirmation || null,
  specialization: data?.value.specialization || null,
  twitter: data?.value.twitter || null,
});

//load languages data into languagesMultivalue
if (profileSettingsForm.value.languages) {
  const languagesArr = profileSettingsForm.value.languages.split(",");
  languagesArr.forEach((lang: any) => {
    languagesMultivalue.value.push({
      label: lang,
      value: lang,
    });
  });
}

//load specialization data into languagesMultivalue based on specializationOptions
if (profileSettingsForm.value.specialization) {
  const specializationArr = profileSettingsForm.value.specialization.split(",");
  specializationArr.forEach((specStr: any) => {
    SPECIALIZATION_OPTIONS.forEach((spec: any) => {
      if (spec.label === specStr) {
        specializationsMultivalue.value.push(spec);
      }
    });
  });
}

async function updateProfile() {
  const form: any = document.getElementById("recruiter-profile-form");
  if (form.checkValidity()) {
    // prep specialization data
    const specializationArr: any = [];
    specializationsMultivalue.value.forEach((item: any) => {
      specializationArr.push(item.label);
    });
    profileSettingsForm.value.specialization = specializationArr.join(",");

    // prep specialization data
    const languagesArr: any = [];
    languagesMultivalue.value.forEach((lang: any) => {
      languagesArr.push(lang.label);
    });
    profileSettingsForm.value.languages = languagesArr.join(",");

    loading.value = true;
    const { data, error } = await useRecruiter().updateProfile(
      profileSettingsForm.value,
    );
    loading.value = false;
    if (data) helper.successToast("Profile updated");
    else helper.errorToast("Opps..");
  } else {
    form.reportValidity();
  }
}
</script>
