<template>
  <section
    id="recruiter-dashboard-wallet"
    :class="{ 'animate-pulse': pending }"
  >
    <RecruiterDashboardHeader title="Wallet" />

    <!--Wallet View-->
    <div class="bg-white rounded border md:w-1/2 font-semibold mb-16 divide-y">
      <div class="grid grid-cols-2 justify-items-center text-sm md:text-base">
        <p class="text-primary pb-2 pt-6">Current Commission</p>
        <p class="pb-2 pt-6">Total Commission</p>
      </div>
      <div class="grid grid-cols-2 justify-items-center">
        <p class="flex items-start gap-1 text-primary text-xs py-8">
          MYR <span class="text-xl">{{ balance || 0 }}.</span>00
        </p>
        <p class="flex items-start gap-1 text-xs py-8">
          MYR <span class="text-xl">{{ totalIn || 0 }}.</span>00
        </p>
      </div>
    </div>

    <!--Transactions Table-->
    <div class="rounded-md shadow mb-16">
      <div class="rounded-t-md bg-white">
        <div class="flex p-4 justify-between items-center">
          <p class="text-sm font-semibold py-4">Transaction History</p>
          <!-- <div>
            <select name="" id="" value="week" class="btn p-2">
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="all">This Month</option>
            </select>
          </div> -->
        </div>
      </div>

      <div class="overflow-x-scroll md:overflow-x-hidden">
        <div
          class="grid md:grid-cols-8 bg-info bg-opacity-10 text-primary text-sm py-6"
        >
          <p class="md:col-span-1 text-center md:text-start md:pl-6">Date</p>
          <p class="md:col-span-4 text-center md:text-start">
            Transaction Details
          </p>
          <p class="md:col-span-1 text-center">Money In</p>
          <p class="md:col-span-1 text-center">Money Out</p>
          <p class="md:col-span-1 text-center">Balance</p>
        </div>

        <div
          v-for="transaction in transactions"
          class="grid md:grid-cols-8 py-3 bg-white border-b border-b-gray-100"
        >
          <!--FIRST ROW-->
          <div class="md:col-span-1 text-sm text-center md:text-start pl-3">
            {{ formatDate(transaction.updated_at) }}
          </div>
          <div class="md:col-span-4 text-sm text-center md:text-start">
            {{ transaction.note }}
          </div>
          <div class="md:col-span-1 text-sm text-center">
            {{ transaction.type == "in" ? transaction.amount : "" }}
          </div>
          <div class="md:col-span-1 text-sm text-center">
            {{ transaction.type == "out" ? transaction.amount : "" }}
          </div>
          <div class="md:col-span-1 text-sm text-center">
            {{ transaction.balance }}
          </div>
        </div>
      </div>
    </div>

    <!--Withdraw Form-->
    <div class="bg-white rounded-md border p-4 lg:w-1/2">
      <p class="text-sm font-semibold pt-4 pb-8">Request Withdrawal</p>
      <div v-if="!profileCheck">
        <p class="text-sm">
          Please update your Full Name, IC and Banking Details in your profile
          to enable withdrawals.
        </p>
        <button
          class="btn btn-primary px-12 mt-8"
          @click="navigateTo('/recruiter/dashboard/profile')"
        >
          Update Profile
        </button>
      </div>
      <FormKit
        v-if="!pendingRecruiter && profileCheck"
        type="form"
        :form-class="'grid gap-4 text-sm '"
        :actions="false"
        @submit="submitWithdrawRequest"
      >
        <FormKit
          type="text"
          label="Full Name"
          name="name"
          validation="required"
          disabled
          :value="recruiterData?.name"
        />

        <FormKit
          id="bank"
          type="select"
          label="Bank Name"
          name="bank_name"
          validation="required"
          :classes="{ input: 'appearance-none' }"
          :value="recruiterData?.bank_name"
        >
          <option v-for="bank in BANKS" :value="bank">{{ bank }}</option>
        </FormKit>
        <FormKit
          id="bank_no"
          type="number"
          label="Bank Number"
          name="bank_no"
          validation="required|number"
          placeholder="************"
          :value="recruiterData?.bank_no"
        />
        <FormKit
          id="amount"
          type="number"
          label="Ammount (RM)"
          placeholder="100"
          name="amount"
          :validation="`required|number|min:100|max:${balance}`"
        />
        <button type="submit" class="btn btn-primary w-64 mt-4">
          Submit Withdrawal
        </button>
      </FormKit>
    </div>
    <DevOnly>
      <!-- <pre>{{ transactions }}</pre> -->
    </DevOnly>
  </section>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import Swal from "sweetalert2";

definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});
const backendUrl = useRuntimeConfig().public.backendUrl;
const token = useRecruiter().token;
const { data: recruiterData, pending: pendingRecruiter } =
  useRecruiter().getProfile();

const { data: transactions, pending } = useFetch<Transaction[]>(
  "/api/recruiter/wallet/transactions",
  {
    headers: { Authorization: "Bearer " + token.value },
  }
);

function formatDate(date: string) {
  return dayjs(date).format("DD MMM YY");
}

const totalIn = computed(() => {
  return transactions.value
    ?.filter((transaction) => transaction.type == "in")
    .reduce((acc, transaction) => acc + transaction.amount, 0);
});

const totalOut = computed(() => {
  return transactions.value
    ?.filter(
      (transaction) =>
        transaction.type == "out" && transaction.status == "completed"
    )
    .reduce((acc, transaction) => acc + transaction.amount, 0);
});

const balance = computed(() => {
  if (totalIn.value && totalOut.value) {
    return totalIn.value - totalOut.value;
  }
  return 0;
});

const profileCheck = computed(() => {
  if (
    !recruiterData.value?.name ||
    !recruiterData.value?.ic ||
    !recruiterData.value?.bank_name ||
    !recruiterData.value?.bank_no
  )
    return false;
  return true;
});

async function submitWithdrawRequest(form: any) {
  const { isConfirmed } = await Swal.fire({
    title: "Please make sure your details are correct.",
    html: `
    <p>Full Name: ${form.name}</p>
    <p>Bank Name: ${form.bank_name}</p>
    <p>Bank Number: ${form.bank_no}</p>
    <p>Amount: ${form.amount}</p>
    <p class="text-sm mt-2">Any missing transfers are non-refundable.</p>`,
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "hsl(var(--p))",
    confirmButtonText: "Confirm",
    cancelButtonText: "Cancel",
  });

  if (isConfirmed == false) return;

  const res = await (
    await fetch(backendUrl + "/wallet_withdraw_request", {
      method: "POST",
      body: form,
      headers: { Authorization: "Bearer " + token.value },
    })
  ).json();

  if (res.error) {
    Swal.fire({
      title: "Error",
      text: res.error,
      icon: "error",
      confirmButtonColor: "hsl(var(--p))",
    });
  }
}
</script>
