<template>
  <div id="candidate-details-and-edit-page">
    <RecruiterDashboardHeader
      title="Candidate Details"
      subtitle="Add or Edit your Candidates"
    />
    <button v-if="!editMode" class="btn btn px-12" @click="editMode = true">
      Edit Candidate
    </button>
    <RecruiterDashboardCandidateForm
      v-if="editMode && !pending"
      class="animate__animated animate__fadeInDown"
      :candidate="candidateData"
      mode="edit"
      @submit="navigateTo('/recruiter/dashboard/candidates')"
    />
  </div>
</template>
<script setup>
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});
const route = useRoute();
const { data: candidateData, pending } = useRecruiter().getCandidateById(
  route.params.candidate_id,
);

const editMode = ref(true);
</script>
