<template>
  <div class="p-8 lg:p-12" :class="{ 'animate-pulse': pendingJob }">
    <div class="text-center">
      <h1 class="font-semibold">Job Application</h1>
      <div class="divider max-w-2xl mx-auto" />
      <img :src="jobData?.company.logo_url"
        class="mx-auto border rounded-full w-20 h-20 md:w-32 md:h-32 object-contain mt-4">
      <p class="-mb-1 mt-4">{{ jobData?.company?.name || "--------" }}</p>
      <h3 class="font-semibold">{{ jobData?.title || "--------" }}</h3>
      <p class="text-sm text-accent mt-6">
        Please ensure your candidate info is correct and fully filled.
      </p>
      <div class="divider max-w-2xl mx-auto" />

      <!-- if candidate not picked, list candidate or add new -->
      <div v-if="!pickedCandidate" class="flex flex-col gap-2 max-w-3xl mx-auto min-h-[50vh]">
        <h3>Pick a Candidate</h3>
        <div class="grid grid-cols-2 gap-2">
          <button class="btn" :class="{ 'btn-primary': !addMode }"
            :disabled="pendingCandidates || candidateData?.data.length === 0" @click="addMode = false">
            Existing
          </button>
          <button class="btn" :class="{ 'btn-primary': addMode }" @click="addMode = true">
            Add New
          </button>
        </div>
        <div v-if="addMode">
          <RecruiterDashboardCandidateForm v-if="addMode" :candidate="{}" mode="add" :recruiter-id="recruiterData?.id"
            class="-m-8 mt-0" @submit="handleAddedNewCandiate" />
        </div>

        <RecruiterDashboardCandidateCard v-for="candidate in candidateData?.data" v-if="!addMode" :key="candidate.id"
          :candidate="candidate" :navigation="false" :class="{
            'border-primary border-2': pickedCandidate?.id === candidate.id,
          }" @click="handlePickCandidate(candidate)" />
      </div>

      <!-- Once Candidate is picked, Show confirmation -->

      <div v-if="pickedCandidate" class="max-w-2xl mx-auto flex flex-col gap-4 py-3">
        <div class="">
          <h3>Picked Candidate</h3>
        </div>
        <RecruiterDashboardCandidateCard :candidate="pickedCandidate" class="border-primary border-2"
          :navigation="false" />

        <div class="text-left">
          <FormKit ref="candidateApplicationForm" type="form" :actions="false" :form-class="'grid gap-4 lg:gap-8'"
            @submit="submitExisting">
            <FormKit type="number" name="salary_expected" label="Expected Salary (RM)" validation="required" :value="pickedCandidate?.expected_salary === 0
              ? null
              : pickedCandidate?.expected_salary
              " placeholder="5000" />
            <FormKit type="number" name="salary_current" label="Current Salary (RM)" validation="required" :value="pickedCandidate?.current_salary === 0
              ? null
              : pickedCandidate?.current_salary
              " placeholder="3000" />

            <FormKit type="select" name="notice_period" label="Notice Period" validation="required" :value="'2 weeks'"
              :validation-messages="{
                length: 'Must pick a notice period',
              }">
              <option v-for="period in NOTICE_PERIODS" :value="period">
                {{ period }}
              </option>
            </FormKit>
          </FormKit>
        </div>

        <div class="grid grid-cols-[1fr_4fr] gap-4 mt-8 mb-12">
          <button class="btn" @click="pickedCandidate = null">
            <Icon name="heroicons:chevron-left" /> Back
          </button>
          <button class="btn btn-primary" @click="handleSubmitButton">
            Submit
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
definePageMeta({
  middleware: "recruiter-auth",
});
const route = useRoute();
const addMode = ref(false);
const pickedCandidate = ref<any>(null);
const candidateApplicationForm = ref<any>(null);

const jobData = {} as TODO

const handlePickCandidate = (candidate: any) => {
  candidate.profile_completed
    ? (pickedCandidate.value = candidate)
    : navigateTo("/recruiter/dashboard/candidates/" + candidate.id);
  scrollToTop();
};

const handleSubmitButton = () => {
  const node = candidateApplicationForm.value.node;
  node.submit();
};

const handleAddedNewCandiate = async (newCandidateForm: any) => {
  if (jobData.value) {
    const finalForm = {
      candidate_id: newCandidateForm.candidate_id,
      job_id: jobData.value.id,
      user_id: recruiterData.value.id,
      salary_current: newCandidateForm.current_salary,
      salary_expected: newCandidateForm.expected_salary,
      notice_period: newCandidateForm.notice_period,
    };

    const res = await $fetch<TODO>(backendUrl + "/applicants", {
      method: "POST",
      body: finalForm,
      headers: {
        authorization: "Bearer " + recruitertoken.value,
      },
    });

    if (!res.id) {
      errorToast(res.error);
      pickedCandidate.value = null;
      scrollToTop();
    } else {
      successToast("Candidate Application Submitted");
      navigateTo(`/recruiter/dashboard/jobs/${jobData.value.slug}/thankyou`);
    }
  }
};

const submitExisting = async (form: any) => {
  if (jobData.value && pickedCandidate.value && recruiterData.value) {
    const finalForm = {
      candidate_id: pickedCandidate.value.id,
      job_id: jobData.value.id,
      user_id: recruiterData.value.id,
      salary_current: pickedCandidate.value.current_salary,
      salary_expected: pickedCandidate.value.expected_salary,
      notice_period: pickedCandidate.value.notice_period,
    };

    const res = await $fetch<TODO>(backendUrl + "/applicants", {
      method: "POST",
      body: finalForm
    });

    if (!res.id) {
      errorToast(res.error);
      pickedCandidate.value = null;
      scrollToTop();
    } else {
      res.status = res.success;
      successToast("Candidate Application Submitted");
      navigateTo(`/recruiter/dashboard/jobs/${jobData.value.slug}/thankyou`);
    }
  }
};
</script>
