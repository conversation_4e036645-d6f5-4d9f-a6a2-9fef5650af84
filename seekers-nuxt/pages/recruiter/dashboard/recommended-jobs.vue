<template>
  <section id="recruiter-dashboard-recommended-jobs">
    <RecruiterDashboardHeader
      title="Recommended Jobs"
      subtitle="Jobs based on your specializations"
    />
    <RecruiterDashboardJobsList title="Recommended Jobs" :jobs="data?.data" />
  </section>
</template>

<script setup>
definePageMeta({
  layout: "recruiter",
  middleware: "recruiter-auth",
});
const { data, pending } = useRecruiter().recommendedJobs();
</script>
