<template>
  <div
    id="recruiter-login-page"
    class="grid grid-cols-1 lg:grid-cols-3 w-screen"
  >
    <div class="relative w-full">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463401/website/candidate/registration-bg_hpglmp.png"
        alt="login-bg"
        class="h-20 lg:h-screen w-full object-left-top object-cover"
        style="filter: hue-rotate(131deg) brightness(0.8) saturate(1.5)"
      />
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463402/website/candidate/registration-vector_va9uqh.svg"
        alt=""
        class="hidden lg:block lg:absolute bottom-28 left-0 object-cover"
      />
    </div>
    <div class="col-span-2 p-8 py-28">
      <div
        id="login-form"
        class="max-w-xl mx-auto flex flex-col gap-8 justify-center"
      >
        <h1>Recruiter Login</h1>
        <div>
          <p class="mb-2 text-sm">Email</p>
          <label for="email" class="sr-only">Email</label>

          <div class="relative">
            <input
              v-model="email"
              type="email"
              class="w-full rounded-lg border border-gray-200 p-4 pr-12 text-sm shadow-sm focus:outline-primary"
              placeholder="Enter email"
            />

            <div class="absolute inset-y-0 right-4 inline-flex items-center">
              <Icon name="heroicons:at-symbol" class="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        <div>
          <p class="mb-2 text-sm">Password</p>
          <label for="password" class="sr-only">Password</label>
          <div class="relative">
            <input
              v-model="password"
              :type="viewPassword ? 'text' : 'password'"
              class="w-full rounded-lg border border-gray-200 focus:outline-primary p-4 pr-12 text-sm shadow-sm"
              placeholder="Enter password"
              @keyup.enter="login"
            />

            <div class="absolute inset-y-0 right-4 inline-flex items-center">
              <Icon
                name="heroicons:eye"
                class="h-5 w-5 text-gray-400 cursor-pointer"
                @click="viewPassword = !viewPassword"
              />
            </div>
          </div>
        </div>

        <div class="flex w-full">
          <input
            id="rememberme"
            type="checkbox"
            class="form-checkbox h-4 w-4 accent-primary"
            :checked="true"
          />
          <label for="rememberme" class="ml-2 text-xs text-gray-400">
            Remember me
          </label>
          <nuxt-link
            to="/recruiter/forgot-password"
            class="ml-auto text-xs text-gray-400"
          >
            Forgot your password?
          </nuxt-link>
        </div>

        <div class="w-full">
          <button class="btn btn-primary text-sm w-full" @click="login">
            Log In
          </button>

          <p class="text-sm text-center mt-4">
            Dont have an account?
            <nuxt-link to="/recruiter/register" class="text-primary">
              Signup
            </nuxt-link>
          </p>
        </div>
        <!-- <div id="login-divider" class="grid grid-cols-11 grid-rows-2">
          <div class="col-span-5 border-b border-gray-200"></div>
          <div class="col-span-1 row-span-2 text-center text-xs">or</div>
          <div class="col-span-5 border-b border-gray-200"></div>
        </div>

        <div class="flex flex-col md:flex-row gap-2 md:gap-4 w-full">
          <div class="btn bg-info text-info-content flex-grow">
            <Icon name="bxl:facebook" class="h-5 w-5" />
            <span class="ml-2">Login with Facebook</span>
          </div>
          <div class="btn bg-error text-error-content flex-grow">
            <Icon name="bxl:google" class="h-5 w-5" />
            <span class="ml-2">Temp Bypass</span>
          </div>
          <div class="btn flex-grow">
            <Icon name="bxl:linkedin" class="h-5 w-5" />
            <span class="ml-2">Login with LinkedIn</span>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";

definePageMeta({
  title: "Recruiter Login",
  description: "Recruiter Login",
  keywords: "Recruiter Login",
  layout: "recruiter",
});
// const backendUrl = useRuntimeConfig().public.backendUrl;
const email = ref("");
const password = ref("");
const viewPassword = useState(() => false);
const recruiterLogin = useRecruiter().login;

// const token = useRecruiter().token;
// if (token.value) {
//   navigateTo("/recruiter/dashboard");
// }

// async function login() {
//   if (!email.value || !password.value) {
//     Swal.fire({
//       icon: "error",
//       title: "Oops...",
//       text: "Please fill in all fields",
//       confirmButtonColor: "#33A852",
//     });
//     return;
//   }

//   const formData = new FormData();
//   formData.append("email", email.value);
//   formData.append("password", password.value);

//   const res = await (
//     await fetch(backendUrl + "/login", {
//       method: "POST",
//       body: formData,
//     })
//   ).json();

//   if (res.success) {
//     token.value = res.data.token;
//     await navigateTo("/recruiter/dashboard");
//   } else {
//     Swal.fire({
//       icon: "error",
//       title: "Hmmm...",
//       text: "Please make sure email/password you entered is correct.",
//       confirmButtonColor: "#33A852",
//     });
//   }
// }

async function login() {
  await recruiterLogin(email.value, password.value);
  // const response = await $directus.login(email.value, password.value);
  // console.log(response);
  navigateTo("/recruiter/dashboard");
}
</script>
