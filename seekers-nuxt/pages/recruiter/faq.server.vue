<template>
  <section id="recruiter-faq-page" class="py-10 bg-gray-50 sm:py-16 lg:py-24">
    <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
      <div class="max-w-2xl mx-auto text-center">
        <h2 class="text-3xl font-semibold leading-tight text-black md:text-4xl">
          Frequently Asked Questions
        </h2>
        <p
          class="max-w-xl mx-auto mt-4 text-base leading-relaxed text-gray-600"
        >
          Home / FAQ
        </p>
      </div>

      <div class="max-w-3xl mx-auto mt-8 space-y-4 md:mt-16">
        <h2 class="font-semibold">Payments</h2>

        <!--Payments Question 1-->
        <div
          v-for="payment in payments1"
          tabindex="0"
          class="collapse collapse-plus bg-white border border-gray-200 shadow-lg hover:bg-gray-50"
        >
          <input type="checkbox" >
          <div class="collapse-title text-lg font-semibold py-5">
            {{ payment.question }}
          </div>
          <div class="collapse-content">
            <ul class="list-disc px-6 text-gray-500">
              <li class="text-xs py-1">
                <span class="text-base">{{ payment.answer1 }}</span>
              </li>
              <li class="text-xs">
                <span class="text-base">{{ payment.answer2 }}</span>
              </li>
              <li class="text-xs py-1">
                <span class="text-base">{{ payment.answer3 }}</span>
              </li>
              <li class="text-xs">
                <span class="text-base">{{ payment.answer4 }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!-- Payments Question 2-->
        <div
          v-for="payment in payments2"
          tabindex="0"
          class="collapse collapse-plus bg-white border border-gray-200 shadow-lg hover:bg-gray-50"
        >
          <input type="checkbox" >
          <div class="collapse-title text-lg font-semibold py-5">
            {{ payment.question }}
          </div>
          <div class="collapse-content">
            <p class="px-6 text-gray-500">{{ payment.answer }}</p>
          </div>
        </div>
      </div>

      <div class="max-w-3xl mx-auto mt-8 space-y-4 md:mt-16">
        <h2 class="font-semibold">Others</h2>

        <!-- Others Question 1-->
        <div
          v-for="other in others1"
          tabindex="0"
          class="collapse collapse-plus bg-white border border-gray-200 shadow-lg hover:bg-gray-50"
        >
          <input type="checkbox" >
          <div class="collapse-title text-lg font-semibold py-5">
            {{ other.question }}
          </div>
          <div class="collapse-content">
            <ul class="list-disc px-6 text-gray-500">
              <li class="text-xs py-1">
                <span class="text-base">{{ other.answer1 }}</span>
              </li>
              <li class="text-xs">
                <span class="text-base">{{ other.answer2 }}</span>
              </li>
              <li class="text-xs py-1">
                <span class="text-base">{{ other.answer3 }}</span>
              </li>
              <li class="text-xs">
                <span class="text-base">{{ other.answer4 }}</span>
              </li>
              <li class="text-xs py-1">
                <span class="text-base">{{ other.answer5 }}</span>
              </li>
              <li class="text-xs">
                <span class="text-base">{{ other.answer6 }}</span>
              </li>
            </ul>
          </div>
        </div>

        <!--Others Question 2 to 5-->
        <div
          v-for="other in others2"
          tabindex="0"
          class="collapse collapse-plus bg-white border border-gray-200 shadow-lg hover:bg-gray-50"
        >
          <input type="checkbox" >
          <div class="collapse-title text-lg font-semibold py-5">
            {{ other.question }}
          </div>
          <div class="collapse-content">
            <p class="px-6 text-gray-500">{{ other.answer }}</p>
            <p class="px-6 pt-4 text-gray-500">{{ other.answer2 }}</p>
          </div>
        </div>
      </div>

      <p class="text-center text-gray-600 textbase mt-9">
        Didn’t find the answer you are looking for?
        <a
          href="#"
          title=""
          class="font-medium text-blue-600 transition-all duration-200 hover:text-blue-700 focus:text-blue-700 hover:underline"
          >Contact our support</a
        >
      </p>
    </div>
  </section>
</template>

<script setup>
const payments1 = [
  {
    question: "What are the different commission scheme I can earn in Seekers?",
    answer1: "Qualified status = RM15",
    answer2: "Attended interview = RM30",
    answer3: "Sign offer letter = RM50",
    answer4:
      "Completion Commission = 1 month after the candidate join the hiring company, your commission is calculated based on candidate's 1st month salary x 24%",
  },
];

const payments2 = [
  {
    question:
      "What is the turn around time for bank transfer of commission upon withdrawal?",
    answer:
      "After requesting for withdrawal, the bank transfer will take 3 to 5 business day. ",
  },
];

const others1 = [
  {
    question: "What are the specialization of roles I can work for in Seekers?",
    answer1: "Sales",
    answer2: "Digital Marketing",
    answer3: "Japanese Speaking Roles",
    answer4: "HoReCa Sales",
    answer5: "Digital Sales",
    answer6: "Logistic Sales",
  },
];

const others2 = [
  {
    question: "What are the various method to source for candidate?",
    answer: "Visit our knowledge sharing page for more information.",
  },
  {
    question: "What are the message template I can use to chat with Candidate?",
    answer: "Visit our knowledge sharing page for more information.",
  },
  {
    question: "Will I need to deal with Hiring Manager or Hiring Company?",
    answer:
      "You do not need to deal with Hiring Manager, Seekers team will be representing your candidate to the hiring manager.",
    answer2:
      "You will only need to find the right candidate according to the job description.",
  },
  {
    question: "I need guidance, do you have a master recruiter to help?",
    answer:
      "Yes, we have, Michael Ng is our Master Recruiter. Do click here for a 1 to 1 schedule with him.",
  },
];
</script>
