<template>
  <div id="recruiter-register-page" class="grid grid-cols-1 lg:grid-cols-3 w-full">
    <div class="relative w-full">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463401/website/candidate/registration-bg_hpglmp.png"
        alt="register-bg" class="h-20 lg:h-screen w-full object-left-top object-cover"
        style="filter: hue-rotate(131deg) brightness(0.8) saturate(1.5)" />
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670463402/website/candidate/registration-vector_va9uqh.svg"
        alt="" class="hidden lg:block lg:absolute bottom-28 left-0 object-cover" />
    </div>
    <div class="col-span-2 p-8 py-28">
      <div id="register-form" class="max-w-4xl mx-auto flex flex-col gap-8 justify-center">
        <h1 class="text-center">Create a Recruiter Account</h1>

        <!-- STEP 1 FORM -->
        <div v-if="step == 1">
          <FormKit id="registration-example" v-model="basicInfoForm" type="form" :form-class="'hide'"
            submit-label="Next" :incomplete-message="false" :submit-attrs="{
              inputClass: 'btn btn-primary ',
              outerClass: 'max-w-xl mx-auto text-center',
            }" @submit="nextHandler">
            <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
              <FormKit type="text" name="name" label="Full Name" validation="required|length:5"
                placeholder="Full Name" />
              <FormKit type="email" name="email" label="Email Address" validation="required|email"
                placeholder="Your Email Address" />
              <FormKit type="group" name="password_group">
                <FormKit type="password" name="password" value="" label="Create Password"
                  placeholder="Enter Your Password" help="Enter password" validation="required|length:8" />
                <FormKit type="password" name="password_confirm" label="Confirm Password"
                  placeholder="Confirm Your Password" help="Confirm your new password" validation="required|confirm"
                  validation-label="Password confirmation" />
              </FormKit>
              <FormKit type="tel" name="mobile" label="Phone Number" placeholder="01144448888"
                validation="required|length:10,13" :validation-messages="{
                  matches: 'Phone number must be in the format xxxxxxxxxx',
                }" />

              <FormKit type="text" name="ic" label="IC Number" placeholder="Enter Your IC Number"
                validation="required|matches:/[0-9]/|length:12,12" :validation-messages="{
                  matches: 'Please insert number only without dashes (-)',
                  length: 'Please insert number only without dashes (-)',
                }" validation-visibility="blur" />

              <!-- <FormKit
                type="file"
                label="Upload Identification Card Copy"
                accept=".pdf"
                :classes="{
                  noFiles: 'hidden',
                  fileName: 'hidden',
                  fileRemove: 'hidden',
                }"
                validation="required"
              /> -->
            </div>
            <p class="text-xs text-center mt-8">
              By pressing next, you agree on Seekers's Privacy Statement, Terms
              & Conditions
            </p>
          </FormKit>
        </div>

        <!-- STEP 2 FORM -->
        <div v-if="step == 2">
          <p class="text-center mb-8">
            Please complete the screening questions below
          </p>
          <FormKit id="registration-example" v-model="screeningQuestionForm" type="form" :form-class="'hide'"
            submit-label="Register" :incomplete-message="false" :submit-attrs="{
              inputClass: 'btn btn-primary',
              outerClass: 'max-w-xl mx-auto text-center mt-4',
            }" @submit="register">
            <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
              <!-- Technical Recruiter select -->
              <FormKit type="select" label="1. Are you a Technical or a Non Technical Recruiter?"
                name="is_technical_recruiter" validation="required" placeholder="Please Select" :value="false" :options="[
                  { label: 'Technical Recruiter', value: true },
                  { label: 'Non Technical Recruiter', value: false },
                ]" />

              <!-- Experience select -->
              <FormKit type="select" label="2. How many years of experience do you have as a recruiter?"
                name="experience_years" validation="required" placeholder="Please Select"
                :options="RECRUITER_EXPERIANCE_OPTIONS" />
              <!-- Specialization select -->
              <FormKit type="select" label="3. What is your specialization?" name="specialization" validation="required"
                placeholder="Please Select" :options="SPECIALIZATION_OPTIONS" />
              <!-- Fulltime select -->
              <FormKit type="select" label="4. Are you working full-time now?" name="is_working_fulltime"
                validation="required" placeholder="Please Select" :options="[
                  { label: 'Yes', value: true },
                  { label: 'No', value: false },
                ]" />
              <!-- Commit hours select -->
              <FormKit type="select" label="5. How many hours can you commit per day doing freelance recruitment?"
                name="committed_hours_daily" validation="required" placeholder="Please Select" :options="[
                  { label: '1-3 hours', value: '1-3 hours' },
                  { label: 'More than 5 hours', value: 'more than 5 hours' },
                ]" />
              <!-- Source method select -->
              <FormKit type="select" label="6. What is your source method?" name="sourcing_method" validation="required"
                placeholder="Please Select" :options="[
                  'Social Media',
                  'Jobstreet',
                  'LinkedIn Premium',
                  'LinkedIn Recruiter',
                  'Competitor Headhunting',
                ]" :classes="{
                  inner: 'md:mt-6',
                }" />
            </div>
            <p class="text-xs text-center mt-8">
              By pressing register, you agree on Seekers's Privacy Statement,
              Terms & Conditions
            </p>
          </FormKit>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";

definePageMeta({
  title: "Recruiter Register",
  description: "Recruiter Register",
  keywords: "Recruiter Register",
  layout: "recruiter",
});

// hellobar script
useHead({
  script: [
    {
      src: "https://my.hellobar.com/bb324d1a61ba9504386213880443e889e88536f1.js",
      type: "text/javascript",
      async: true,
    },
  ],
});

// Form things
const step = ref(1);
const basicInfoForm: any = useState("basicInfoForm", () => null);
const registrationForm: any = ref(null);
const screeningQuestionForm: any = ref(null);
const user = useUser();

function nextHandler() {
  registrationForm.value = {
    ...basicInfoForm?.value,
    password: basicInfoForm.value.password_group.password,
  };
  delete registrationForm.value.password_group;
  step.value = 2;
  window.scrollTo(0, 0);
}

async function register() {
  const formData = {
    ...registrationForm.value,
    ...screeningQuestionForm.value,
  };

  console.log(formData)

  const recruiterProfileData = {
    name: formData.name,
    email: formData.email,
    mobile: formData.mobile,
    ic: formData.ic,
    is_technical_recruiter: formData.is_technical_recruiter,
    experience_years: formData.experience_years,
    specialization: formData.specialization,
    is_working_fulltime: formData.is_working_fulltime,
    committed_hours: formData.committed_hours_daily,
    sourcing_method: formData.sourcing_method,
  };

  try {
    
    // Try create and add recruiter profile
    await $fetch('/api/recruiter/register', {
      method: 'POST',
      body: { email: formData.email, password: formData.password, recruiterProfileData }
    })

    await user.login(formData.email, formData.password)

    // get recruiter id
    Swal.fire({
      title: "Registration Successful",
      text: "Redirecting to your dashboard",
      icon: "success",
      confirmButtonText: "OK",
    }).then(() => {
      navigateTo("/recruiter/dashboard");
    });

  } catch (error) {
    Swal.fire({
      icon: "error",
      title: "Registration Failed",
      text: "Please try again",
      confirmButtonText: "OK",
    });
  }
}
</script>
