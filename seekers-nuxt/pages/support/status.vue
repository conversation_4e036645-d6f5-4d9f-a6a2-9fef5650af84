<template>
  <section class="text-gray-600 body-font">
    <div class="container px-5 py-40 mx-auto">
      <div class="flex flex-col text-center w-full mb-16">
        <h1
          class="sm:text-3xl text-2xl font-medium title-font mb-4 text-gray-900"
        >
          System Status
        </h1>
      </div>
      <div class="flex flex-wrap -m-4 text-center">
        <div class="p-4 sm:w-1/4 w-1/2">
          <h2 class="title-font font-sm sm:text-3xl text-xl text-gray-900">
            Database
          </h2>
          <p class="leading-relaxed text-green-600">Healthy</p>
        </div>
        <div class="p-4 sm:w-1/4 w-1/2">
          <h2 class="title-font font-sm sm:text-3xl text-xl text-gray-900">
            Edge Compute
          </h2>
          <p class="leading-relaxed text-green-600 capitalize">
            {{ meili.status == "available" ? "Healthy" : "Down" }}
          </p>
        </div>
        <div class="p-4 sm:w-1/4 w-1/2">
          <h2 class="title-font font-sm sm:text-3xl text-xl text-gray-900">
            Web Workers
          </h2>
          <p class="leading-relaxed text-green-600">Healthy</p>
        </div>
        <div class="p-4 sm:w-1/4 w-1/2">
          <h2 class="title-font font-sm sm:text-3xl text-xl text-gray-900">
            App
          </h2>
          <p class="leading-relaxed text-green-600">Healthy</p>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const meili = await $fetch<{ status: string }>(
  "https://meili.jobsearch-asia.com/health",
);
</script>
