{"name": "seekers-nuxt", "private": true, "type": "module", "version": "2.0.0", "scripts": {"build": "nuxt build", "dev": "nuxi cleanup && nuxt dev", "deploy:dev": "wrangler deploy --env dev", "generate": "nuxt generate", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@directus/sdk": "^19.0.1", "@formkit/addons": "^1.6.9", "@formkit/auto-animate": "^0.8.2", "@formkit/vue": "^1.6.9", "@nuxt/devtools": "latest", "@nuxt/eslint": "^0.3.13", "@nuxt/fonts": "latest", "@nuxt/image": "1.9.0", "@nuxtjs/algolia": "^1.10.2", "@nuxtjs/device": "^3.2.4", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^9.3.1", "@nuxtjs/partytown": "^1.6.0", "@nuxtjs/tailwindcss": "6.12.0", "@vueuse/nuxt": "^10.11.1", "animate.css": "^4.1.1", "daisyui": "^4.12.24", "dayjs": "^1.11.13", "magic-regexp": "^0.8.0", "nuxt": "^3.16.0", "nuxt-icon": "0.6.10", "nuxt-markdown-render": "^2.1.0", "pdf-lib": "^1.17.1", "sweetalert2": "^11.17.2", "transition-style": "^0.1.3", "vite": "^5.4.14", "wrangler": "^3.114.1"}}