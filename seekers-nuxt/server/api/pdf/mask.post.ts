import { PDFDocument, rgb } from "pdf-lib";
import type { OCROutput } from "~/utils/types";

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  // Fetch OCR Data and unmasked PDF concurrently
  const [ocrData, pdfBlob] = await Promise.all([
    $fetch<OCROutput>("/api/pdf/ocrspace", {
      body: { url: body.url },
    }),
    $fetch<Blob>(body.url),
  ]);

  const pdfDoc = await PDFDocument.load(await pdfBlob.arrayBuffer());

  // Create a list of masks to be applied to the PDF
  const getRedactions = (pageNumber: number) => {
    const redactions: Redaction = [];
    const lines = ocrData.ParsedResults[pageNumber].TextOverlay.Lines;
    const words = lines.flatMap((line) => line.Words);

    // Go through each mask and check if it matches any word
    cvMasks().forEach((mask) => {
      words.forEach((word) => {
        // If the word matches the mask, add it to the list of masks
        if (mask.regex.test(word.WordText)) {
          redactions.push({
            left: word.Left,
            top: word.Top,
            width: word.Width,
            height: word.Height,
            maskWith: mask.replacement,
          });
        }
      });
    });
    return redactions;
  };

  // Go through each page of the PDF
  pdfDoc.getPages().forEach((page, index) => {
    // Get the coordinates to be redacted
    const redactions = getRedactions(index + 1);

    redactions.forEach((redaction) => {
      // Draw a rectangle over the coordinates to be masked
      page.drawRectangle({
        x: redaction.left,
        y: redaction.top,
        width: redaction.width,
        height: redaction.height,
        color: rgb(0, 0, 0),
      });

      // Draw text over the rectangle
      page.drawText(redaction.maskWith, {
        x: redaction.left,
        y: redaction.top,
        size: 10,
        color: rgb(1, 1, 1),
      });
    });
  });

  // Remove all metadata from the PDF
  pdfDoc.setAuthor("Seekers");
  pdfDoc.setCreationDate(new Date());
  pdfDoc.setCreator("Seekers");
  pdfDoc.setKeywords(["seekers", "mask", "candidate"]);
  pdfDoc.setLanguage("en");
  pdfDoc.setModificationDate(new Date());
  pdfDoc.setProducer("Seekers");
  pdfDoc.setSubject("Seekers");
  pdfDoc.setTitle("Seekers");

  // Serialize the PDFDocument back to bytes
  const pdfBytes = await pdfDoc.save();
  return pdfBytes;
});
