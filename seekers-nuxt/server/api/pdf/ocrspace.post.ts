export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  const formData = new FormData();
  formData.append("url", body.url);
  formData.append("filetype", "PDF");
  formData.append("isOverlayRequired", "true");
  formData.append("scale", "true");

  // sends POST formData request to OCR.space API
  const res = await $fetch("https://apipro1.ocr.space/parse/image", {
    headers: {
      apikey: useRuntimeConfig().ocrspaceKey,
    },
    method: "POST",
    body: formData,
  });

  return res;
});
