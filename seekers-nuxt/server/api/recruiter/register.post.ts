import { createItem} from '@directus/sdk'

type RecruiterProfileData = {
  name: string;
  email: string;
  mobile: string;
  ic: string;
  is_technical_recruiter: boolean;
  experience_years: string;
  specialization: string;
  is_working_fulltime: boolean;
  committed_hours: string;
  sourcing_method: string;
};

export default defineEventHandler(async (event) => {
  const body = await readBody<{ email: string, password: string, recruiterProfileData: RecruiterProfileData }>(event);
  const { email, password, recruiterProfileData } = body;

  // Create User
  const user = await $fetch<unknown>('/register', {
    method: 'POST',
    body: { email, password }
  })

  // Create Recruiter Profile
  const recruiterProfile = await serverDirectus.request(createItem('recruiter_profile', recruiterProfileData))

  return { user, recruiterProfile }
})
