export default defineEventHandler(async (event) => {

  const body = await readBody(event);

  if (body.post.current.tags.some((tag: any) => tag.slug === "hash-hidden")) {
    return "hidden. not added";
  }

  // Format the data to be indexed
  const record = {
    objectID: body.post.current.id,
    title: body.post.current.title,
    excerpt: body.post.current.excerpt,
    url: body.post.current.url,
  };

  // TODO: Add to blog index in meilisearch

  return "done";
});
