import algoliasearch from "algoliasearch";

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();

  const body = await readBody(event);

  // Connect and authenticate with your Algolia app
  const client = algoliasearch(config.algoliaAppId, config.algoliaApiKey);
  const index = client.initIndex("seekers_ghost_blog");
  index.deleteObject(body.post.current.id).wait();
  return `${body.post.current.id} deleted`;
});
