import algoliasearch from "algoliasearch";

export default defineEventHandler(async (event) => {
  const config = useRuntimeConfig();
  const blogApiUrl = config.public.blogApiUrl;
  const blogApiKey = config.public.blogApiKey;
  const algoliaAppId = config.public.algoliaAppId;
  const algoliaApiKey = config.public.algoliaApiKey;

  //   const body = await readBody(event);

  const data = await (
    await fetch(
      `${blogApiUrl}posts/?key=${blogApiKey}&limit=all&fields=title,id,excerpt,url&include=tags`,
    )
  ).json();

  // Connect and authenticate with your Algolia app
  const client = algoliasearch(algoliaAppId, algoliaApiKey);

  // Format the data to be indexed
  const records: any = [];
  data.posts.forEach((post: any) => {
    // if post.tags array contains slug "hash-hidden", return null
    if (post.tags.some((tag: any) => tag.slug === "hash-hidden")) {
      return;
    }
    records.push({
      objectID: post.id,
      title: post.title,
      excerpt: post.excerpt,
      url: post.url,
    });
  });

  const index = client.initIndex("seekers_ghost_blog");
  index.setSettings({
    searchableAttributes: ["title", "excerpt"],
  });
  index.saveObjects(records).wait();
  return "done";
});
