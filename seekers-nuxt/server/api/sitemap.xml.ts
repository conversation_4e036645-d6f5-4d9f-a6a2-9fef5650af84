export default defineEventHandler(async (event) => {
  const backendUrl = useRuntimeConfig().backendUrl;
  let jobSlugs = [];
  let companySlugs = [];
  const resJobs = await fetch(backendUrl + "/sitemap/jobs");
  const resCompanies = await fetch(backendUrl + "/sitemap/companies");
  jobSlugs = await resJobs.json();
  companySlugs = await resCompanies.json();

  const frontendUrl = "https://seekers.my";
  const sitemapStart = `<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;
  const sitemapEnd = `</urlset>`;
  const today = new Date().toISOString();

  const sitemapArray = [];
  // add homepage to sitemapArray
  sitemapArray.push(
    `<url><loc>${frontendUrl}</loc><lastmod>${today}</lastmod><changefreq>Daily</changefreq><priority>1.0</priority></url>`,
  );

  // add static pages to sitemapArray
  const staticRoutes = [
    "/about",
    "/company",
    "/contact",
    "/employer",
    "/employer/faq",
    "/employer/whitepaper",
    "/jobs",
    "/candidate/login",
    "/privacy",
    "/recruiter",
    "/recruiter/faq",
    "/recruiter/login",
    "/recruiter/register",
    "/register",
    "/hrtrends",
    "/terms",
  ];
  sitemapArray.push(
    ...staticRoutes.map((route) => {
      return `<url><loc>${frontendUrl}${route}</loc><lastmod>${today}</lastmod><changefreq>Daily</changefreq><priority>0.8</priority></url>`;
    }),
  );

  // add jobs to sitemapArray
  sitemapArray.push(
    ...jobSlugs.map((slug: string) => {
      return `<url><loc>${frontendUrl}/jobs/${slug}</loc><lastmod>${today}</lastmod><changefreq>Daily</changefreq><priority>0.9</priority></url>`;
    }),
  );

  // add company pages to sitemapArray
  sitemapArray.push(
    ...companySlugs.map((slug: string) => {
      return `<url><loc>${frontendUrl}/company/${slug}</loc><lastmod>${today}</lastmod><changefreq>Weekly</changefreq><priority>0.8</priority></url>`;
    }),
  );

  // join all sitemap xml together
  const sitemap = sitemapStart + sitemapArray.join("") + sitemapEnd;
  setHeaders(event, { "Content-Type": "application/xml" });
  return sitemap;
});
