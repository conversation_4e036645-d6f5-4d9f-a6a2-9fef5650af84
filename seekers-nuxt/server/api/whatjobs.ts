export default defineEventHandler(async (event) => {
  let data = `<?xml version="1.0" encoding="UTF-8"?>`;
  data += `<jobs xmlns="https://whatjobs.com/XMLSchema"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="https://whatjobs.com/XMLSchema https://whatjobs.com/XMLSchema.xsd ">`;
  try {
    // Get Data from Backend
    const jobs: any[] = await $fetch(
      "https://backend.seekers.my/api/v1/s3kr3t4dm1n/whatjobs",
    );

    for (const element of jobs) {
      data += `<job>
  <id><![CDATA[${element.id}]]></id>
  <url><![CDATA[https://seekers.my/jobfinder/jobs/${element.slug}]]></url>
  <title><![CDATA[${element.title}]]></title>
  <location><![CDATA[${
    element.city ? element.city : "Kuala Lumpur"
  }]]></location>
  <salary><![CDATA[${element.salary_max}]]></salary>
  <category><![CDATA[${element.industry.name}]]></category>
  <desc><![CDATA[${element.responsibility}]]></desc>
  <company-name><![CDATA[${element.company.name}]]></company-name>
  </job>`;

      setHeaders(event, { "Content-Type": "application/xml" });
      data += `</jobs>`;
      return data;
    }
  } catch (e) {
    return "Error";
  }
});
