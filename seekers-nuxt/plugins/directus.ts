import {
	createDirectus,
	authentication,
	readMe,
	rest,
	readItem,
	readItems,
	createItem,
	updateItem,
	deleteItem,
	readAssetBlob,
	readAssetRaw,
	uploadFiles
} from '@directus/sdk';
import type { Schema } from '../utils/types';

const directus = createDirectus<Schema>(process.env.NUXT_PUBLIC_DIRECTUS_URL ?? 'https://directus.seekers.my')
	.with(authentication('cookie', { credentials: 'include' }))
	.with(rest());
export default defineNuxtPlugin(() => {
	return {
		provide: {
			directus,
			authentication,
			readMe,
			readItem,
			readItems,
			updateItem,
			createItem,
			deleteItem,
			readAssetBlob,
			readAssetRaw,
			uploadFiles
		},
	};
});