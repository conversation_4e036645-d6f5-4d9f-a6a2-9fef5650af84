export default defineNuxtRouteMiddleware(async (to, from) => {
  const { $directus } = useNuxtApp();
  if (import.meta.client) {
    console.log("Checking token");
    const token = await $directus.getToken();

    if (!token) return navigateTo("/candidate/login", { external: true});
    const { profile, getProfile } = useUser();

    // If no candidate profile, try getProfile
    if (!profile.value?.candidate_profile) {
      const profileData = await getProfile();
      if (!profileData?.candidate_profile) return navigateTo("/register", { external: true });
    }
  }
});