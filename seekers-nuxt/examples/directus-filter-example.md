# Directus Filter Helper Example

This example demonstrates how to use the `createDirectusFilter` helper function to convert searchFilter values into Directus filter format.

## Basic Usage

```typescript
// Import the helper function
const { createDirectusFilter } = useHelper();

// Get the searchFilter from useJobs
const { searchFilter } = useJobs();

// Create a Directus filter object
const filter = createDirectusFilter(searchFilter.value);

// Use the filter in a Directus request
const { data } = await useAsyncData(
  'jobs',
  () => $directus.request($readItems('jobs', {
    key: 'jobs',
    fields: ['title', 'description', 'state', 'job_type', 'salary_min', 'salary_max'],
    filter: filter
  }))
);
```

## Filter Transformation Examples

### Example 1: Basic Search with Keywords and Location

```typescript
// searchFilter values:
const searchFilter = {
  keywords: "developer",
  location: "kuala lumpur",
  // other fields are empty or default values
};

// Transformed filter:
{
  _and: [
    {
      title: {
        _contains: "developer"
      }
    },
    {
      state: {
        _eq: "kuala lumpur"
      }
    }
  ]
}
```

### Example 2: Advanced Search with Multiple Criteria

```typescript
// searchFilter values:
const searchFilter = {
  keywords: "software engineer",
  location: "selangor",
  role: "4", // Software Engineer category
  includeFullTime: true,
  includeContract: true,
  salary_min: 5000,
  salary_max: 10000,
  levels: "senior_executive"
};

// Transformed filter:
{
  _and: [
    {
      title: {
        _contains: "software engineer"
      }
    },
    {
      state: {
        _eq: "selangor"
      }
    },
    {
      role: {
        _eq: "4"
      }
    },
    {
      job_type: {
        _in: ["full_time", "contract"]
      }
    },
    {
      _and: [
        {
          salary_min: {
            _gte: 5000
          }
        },
        {
          salary_max: {
            _lte: 10000
          }
        }
      ]
    },
    {
      level: {
        _eq: "senior_executive"
      }
    }
  ]
}
```

## Notes

- The function only includes non-empty and non-null values in the filter
- If no valid filter criteria are provided, an empty object `{}` is returned
- The function handles both individual boolean flags (includeFullTime, includeContract, includeInternship) and the jobTypes array
- Salary filters use _gte (greater than or equal) and _lte (less than or equal) operators
