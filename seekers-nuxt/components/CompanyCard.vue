<template>
  <div>
    <div
      class="flex flex-col gap-4 bg-white items-center border m-4 pt-3 pb-6 rounded-md hover:drop-shadow-sm transition duration-300 ease-in-out text-center"
    >
      <!-- Logo -->
      <nuxt-link v-if="img" :to="`/company/${company?.slug}`">
        <img
          :src="img"
          alt="company logo"
          class="w-16 h-16 object-cover rounded-full border"
        />
      </nuxt-link>
      <div v-else class="w-16 h-16 rounded-full bg-gray-200 animate-pulse" />

      <!-- name -->
      <p v-if="company.name" class="font-semibold h-12 line-clamp-2">
        {{ company.name }}
      </p>
      <div v-else class="h-12 rounded-lg w-2/3 bg-gray-200" />

      <!-- location -->
      <div
        v-if="company.city"
        class="capitalize flex items-center gap-1 text-xs text-accent"
      >
        <Icon name="heroicons:map-pin" />
        {{ company.city }}
      </div>
      <div v-else class="h-4 rounded-lg w-1/3 bg-gray-200" />

      <!-- industry -->
      <div
        v-if="company.industry"
        class="flex items-center gap-1 text-xs text-accent"
      >
        <Icon name="heroicons:briefcase" />
        {{ company?.industry.name }}
      </div>
      <div v-else class="h-4 rounded-lg w-1/3 bg-gray-200" />

      <!-- open jobs -->
      <nuxt-link
        :to="`/company/${company?.slug}`"
        type="button"
        class="btn btn-success btn-sm rounded-full py-2 px-8"
      >
        <!-- {{
          !company.open_jobs_count
            ? "View company"
            : company.open_jobs_count > 1
            ? company.open_jobs_count + " Open Jobs"
            : company.open_jobs_count + " Open Job"
        }} -->
        View Company
      </nuxt-link>
    </div>

    <!-- <pre>company: {{ company.name }}</pre> -->
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  company: Company;
  img: string;
}>();
</script>
