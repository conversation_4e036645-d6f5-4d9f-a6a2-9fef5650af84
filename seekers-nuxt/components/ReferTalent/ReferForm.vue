<template>
  <div id="refer-talent-form" class="bg-[#F0F8F8] py-12 mb-24">
    <div class="md:max-w-7xl md:mx-auto mx-4">
      <div class="flex items-center gap-2 mb-4">
        <div class="w-[23%] md:w-[10%] border-t border-[#D8D8D8]" />
        <p class="font-semibold text-[#55BDB3]">REFER-A-TALENT</p>
      </div>
      <div class="text-center">
        <h2 class="font-bold md:text-4xl">
          Need Extra RM500 in Cash? Refer-A-Talent to Us!
        </h2>
        <p class="text-[#696969] text-sm mt-4 mb-8">
          Do you want to refer someone to Seekers? Just take one simple step:
          fill in all the details here, and we'll reach out to your referral on
          your behalf.
        </p>
      </div>

      <div v-if="route.query.job" class="grid justify-center text-center text-[#696969] text-lg">
        <p>Company: {{ jobData?.company?.name }}</p>
        <p>Role: {{ jobData?.title }}</p>
        <p>Salary: RM {{ jobData?.salary_min }} - RM {{ jobData?.salary_max }}</p>
      </div>

      <CustomZohoFormReferATalent class="md:max-w-5xl md:mx-auto" />
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const referrerUID = useCookie("referrer_uid");

const jobData = await useJobs().getBySlug(route.query.job as string);
</script>
