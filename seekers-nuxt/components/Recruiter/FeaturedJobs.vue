<template>
  <section id="featured-jobs" class="bg-success bg-opacity-60 py-12">
    <div class="max-w-7xl md:mx-auto m-4">
      <h2 class="font-semibold">Featured Jobs</h2>

      <div class="flex md:items-center mb-8">
        <p class="md:mr-auto">
          Work as much or as little as you like, and you can choose projects
          that are significant for you.
        </p>
        <select v-model="activeDropdown" class="select">
          <option v-for="drop in drops" :value="drop">
            {{ drop }}
          </option>
        </select>
      </div>

      <JobCardsList
        v-if="activeDropdown == 'Featured'"
        :jobs-list="highlightedJobs?.data"
        :is-loading="pendingHighlighted"
        :show-pagination="false"
      />
      <JobCardsList
        v-if="activeDropdown == 'Recent'"
        :jobs-list="latestJobs?.data"
        :is-loading="pendingLatest"
        :show-pagination="false"
      />
      <JobCardsList
        v-if="activeDropdown == 'Popular'"
        :jobs-list="latestTechJobs?.data"
        :is-loading="pendingTech"
        :show-pagination="false"
      />
    </div>
  </section>
</template>

<script setup lang="ts">
const { data: highlightedJobs, pending: pendingHighlighted } = useFetch<any>(
  "/api/jobs/highlighted?limit=6",
  { key: "highlighted-jobs" },
);

const { data: latestJobs, pending: pendingLatest } = useFetch<any>(
  "/api/jobs/latest",
  { key: "latest-jobs" },
);

const { data: latestTechJobs, pending: pendingTech } = useFetch<any>(
  "/api/jobs/latest-tech",
  { key: "latest-tech" },
);

const activeDropdown = ref("Featured");
const drops = ["Featured", "Recent", "Popular"];
</script>
