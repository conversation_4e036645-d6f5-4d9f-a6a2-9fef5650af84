<template>
  <section id="people-saying" class="bg-success bg-opacity-60 py-24">
    <div class="max-w-7xl md:mx-auto pb-10">
      <div class="text-center mb-12">
        <h2 class="font-semibold">What people are saying</h2>
        <p>Wonder if this is really working?</p>
      </div>

      <div
        class="flex overflow-x-scroll snap-x snap-mandatory scroll-smooth scrollbar-view scroll-container relative ml-4"
      >
        <div
          v-for="(item, index) in items"
          class="carousel-item bg-white flex-col w-[300px] rounded-md mx-3 p-4 drop-shadow-lg md:drop-shadow-none"
        >
          <img :src="item.img" alt="" class="w-16 h-16" >
          <p class="font-semibold text-sm text-gray-600 mb-1 py-4">
            {{ item.position }}
          </p>
          <p class="mb-6 flex-grow line-clamp-[10] text-sm">
            {{ item.description }}
          </p>
          <p class="font-semibold text-sm text-gray-600">{{ item.name }}</p>
        </div>
      </div>
      <div
        class="right-arrow text-xs text-center absolute left-1/2 -translate-x-1/2 text-accent animate-pulse"
      >
        Swipe right for more >>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
onMounted(() => {
  const container = document.querySelector(".scroll-container") as HTMLElement;
  const leftArrow = document.querySelector(".left-arrow") as HTMLElement;
  const rightArrow = document.querySelector(".right-arrow") as HTMLElement;

  container.addEventListener("scroll", () => {
    const isAtStart = container.scrollLeft <= 100;
    const isAtEnd =
      container.scrollLeft >=
      container.scrollWidth - container.clientWidth - 200;

    if (!isAtStart) {
      rightArrow.style.display = "none";
    }
  });
});

const items = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1671162651/website/recruiterpage/index/people-say-1_fbh9wp.jpg",
    position: "IT Project Manager",
    description:
      "Seekers freelance recruitment program is a simple and easy way to earn extra in a free time. Instead of wasting time on other social media platform, I would rather prefer Seekers and recommend to those who want to earn extra income and help candidates find jobs.",
    name: "Mr Rahim B.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1671162651/website/recruiterpage/index/people-say-2_bncb0q.jpg",
    position: "Full Time Online Recruitment Specialist",
    description:
      "Seekers is not just a job seeking portal but also a place for freelance recruiters to earn extra income while enjoying the freedom to work offsite. Seekers offer roles from wide range of industries and recruiters like me will have plenty of options to search in terms of candidates placement and not forgetting the attractive commissions offered.",
    name: "Mr L. Weng Kan",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1671162651/website/recruiterpage/index/people-say-3_e41ric.jpg",
    position: "Full Time Online Recruiter",
    description:
      "I have been with Seekers since the past few years. A very trustable & efficient recruitment program platform. They both help me to equip me with result driven messages and method to hunt the right candidate.",
    name: "Mr Desmin",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1671165874/website/recruiterpage/index/people-say-4_v4kizn.jpg",
    position: "Online Freelance Recruiter",
    description:
      "Hi, I am Haffiz, a freelance recruiter program user who has been using the program for about 2 years now, Seekers has helped me a lot in finding more money, my commission has also increased. At the same time, I also gained a lot of knowledge & experience from Mr. Michael Ng. Seekers can be used by anyone regardless of age, the important thing is to be diligent & focus only 🙂. Thank you, Seekers!",
    name: "Haffiz",
  },
];
</script>

<style scoped>
.scrollbar-view::-webkit-scrollbar {
  height: 0.5rem;
}

.scrollbar-view::-webkit-scrollbar-thumb {
  background: #888;
}

.scrollbar-view::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.scrollbar-view::-webkit-scrollbar-thumb:horizontal {
  background: gray;
  border-radius: 10px;
}

.scrollbar-view::-webkit-scrollbar-track-piece:end {
  margin-right: 400px;
}

.scrollbar-view::-webkit-scrollbar-track-piece:start {
  margin-left: 400px;
}
</style>
