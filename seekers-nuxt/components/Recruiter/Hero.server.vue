<template>
  <section id="recruiter-hero">
    <div class="">
      <div
        class="bg-success grid grid-cols-1 md:grid-cols-5 p-12 md:p-20 justify-items-center items-center md:max-w-[90%] mx-auto md:rounded-3xl"
      >
        <div
          id="hero-text"
          class="col-span-2 animate__animated animate__fadeIn"
        >
          <h1 class="font-semibold text-5xl">
            Do you Love To Match And Connect Job Seekers with Hiring Companies?
          </h1>
          <p class="font-semibold mt-8">
            Introducing Seekers Technology Malaysia, only for experienced
            recruitment specialists and talent acquisition specialists.
          </p>
          <div class="mt-8">
            <nuxt-link
              v-if="!auth"
              to="/recruiter/register"
              class="btn btn-primary mr-3"
              >New Registration</nuxt-link
            >
            <nuxt-link
              :to="auth ? '/recruiter/dashboard/' : '/recruiter/login'"
              class="btn btn-primary"
              >{{ auth ? "Go to Dashboard" : "Dashboard Login" }}</nuxt-link
            >
          </div>
        </div>
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1671073688/website/recruiterpage/index/recruiter-hero_mr5y6n.svg"
          alt=""
          class="bg-success md:col-span-3 mt-8 md:mt-0"
        >
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const auth = useRecruiter().token;
</script>
