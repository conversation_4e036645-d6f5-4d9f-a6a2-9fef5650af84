<template>
  <section id="my-applicants">
    <div class="bg-white rounded-md border">
      <div class="flex p-4 justify-between items-center">
        <p class="text-sm font-semibold pl-4">My Candidates</p>
        <nuxt-link to="/recruiter/dashboard/candidates" class="btn"
          >Manage Candidate</nuxt-link
        >
      </div>
      <div class="grid md:grid-cols-2 gap-6 px-4 pb-4">
        <RecruiterDashboardCandidateCard
          v-for="applicant in applicantsData?.data"
          :candidate="applicant"
        />
      </div>
    </div>
  </section>
</template>

<script setup>
const { data: applicantsData } = useRecruiter().getCandidates();
</script>
