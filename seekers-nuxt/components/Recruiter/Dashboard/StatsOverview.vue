<template>
  <section id="recruiter-dashboard-header">
    <RecruiterDashboardHeader
      :title="'Hey, ' + props.name"
      subtitle="Welcome back!"
    />
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
      <div
        v-for="item in items"
        class="flex border rounded-md p-4 bg-white items-center cursor-pointer"
        @click="navigateTo(item.link)"
      >
        <div
          class="w-12 h-12 rounded-md flex items-center justify-center"
          :class="item.iconBackground"
        >
          <Icon :name="item.iconName" class="w-8 h-8" :class="item.class" />
        </div>
        <div class="text-end ml-auto">
          <p :class="item.class">
            {{ item.numbers }}
          </p>
          <p class="text-xs">{{ item.description }}</p>
        </div>
      </div>
    </div>
    <DevOnly>
      <!-- <pre>{{ profileData }}</pre> -->
    </DevOnly>
  </section>
</template>

<script setup lang="ts">
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  totalJobs: {
    type: Number,
    required: true,
  },
  totalApplications: {
    type: Number,
    required: true,
  },
  totalWallet: {
    type: Number,
    required: true,
  },
});
const items = [
  {
    iconName: "heroicons:briefcase",
    iconBackground: "bg-info ",
    numbers: props.totalApplications || 0,
    class: "text-sky-600",
    description: "Applications",
    link: "/recruiter/dashboard/candidates",
  },
  {
    iconName: "heroicons:document-text",
    iconBackground: "bg-warning",
    numbers: props.totalJobs || 0,
    class: "text-yellow-400",
    description: "Positions",
    link: "/recruiter/dashboard/recommended-jobs",
  },
  {
    iconName: "mdi:account-cancel",
    iconBackground: "bg-error",
    numbers: "0",
    class: "text-red-500",
    description: "Rejections",
    link: "/recruiter/dashboard/candidates",
  },
  {
    iconName: "heroicons:banknotes",
    iconBackground: "bg-success",
    numbers: "RM " + props.totalWallet || 0,
    class: "text-primary",
    description: "Commissions",
    link: "/recruiter/dashboard/wallet",
  },
];
</script>
