<template>
  <section id="sharing-knowledge" class="py-24 md:p-24">
    <div class="max-w-7xl md:mx-auto mx-4">
      <h2 class="font-semibold mb-6">Sharing Knowledge</h2>
      <div class="flex flex-col md:flex-row items-center">
        <div class="md:mb-6">
          <p>
            By referencing various resources, it can make it easier for
            recruiter to hire a top talent candidate.
          </p>
          <p>Here are some ideas you can adopt in your recruitment strategy.</p>
        </div>
        <div class="text-center md:ml-auto my-3 text-blue-600">
          <nuxt-link to="/hrtrends" no-prefetch>Browse All ></nuxt-link>
        </div>
      </div>

      <!--PLACEHOLDER-->
      <div class="grid md:grid-cols-4 md:max-w-7xl md:mx-auto gap-4">
        <div v-for="post in blogData?.posts">
          <nuxt-link :to="post.url" target="blank">
            <img
              :src="post.feature_image.replace('/images', '/images/size/w300')"
              alt=""
              class="rounded-md w-full"
            >
            <p class="text-xs pt-2">
              {{ dayjs(post.updated_at).format("DD MMMM YYYY") }}
            </p>
            <p class="pt-1">{{ post.title }}</p>
          </nuxt-link>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const { data: blogData } = useBlog().latestPosts();
</script>
