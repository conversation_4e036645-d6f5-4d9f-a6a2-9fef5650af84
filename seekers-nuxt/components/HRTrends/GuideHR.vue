<template>
  <div id="guide-hr" class="md:max-w-screen-2xl md:mx-auto my-40 mx-4">
    <div class="text-center">
      <h3 class="text-3xl mb-2">Guide for HR</h3>
      <p>
        Discover our exclusive selection of articles tailored specifically for
        employer like yourself.
      </p>
      <p>
        To gain access to these limited articles, kindly share a brief
        description about yourself and your interests!
      </p>
    </div>

    <div class="grid md:grid-cols-4 gap-12 items-center my-12">
      <!-- EBOOK GUIDE -->
      <div v-for="(guide, index) in guides">
        <img :src="guide.img" alt="" >
        <div>
          <label
            :for="`download-modal-${guide.id}`"
            class="btn btn-primary rounded-full flex w-1/2 mx-auto my-4"
            @click="openDownloadModal(index)"
            >Download
            <Icon name="heroicons:arrow-down-tray-20-solid" class="ml-2"
          /></label>
        </div>
      </div>

      <!-- JS-Based MODAL -->
      <div v-if="modalOpened" class="fixed top-0 left-0">
        <div class="relative">
          <div
            class="bg-black bg-opacity-10 w-screen h-screen"
            @click="modalOpened = false"
          />
          <div
            class="bg-white flex flex-col items-center rounded-lg p-12 absolute w-[85%] md:w-[70%] h-[80%] md:h-[60%] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 overflow-y-auto overscroll-x-none overscroll-contain"
          >
            <div class="w-full h-full grid">
              <h3 class="text-lg text-center font-semibold mb-6">
                {{ guides[selectedModal].title }}
              </h3>
              <div class="-mt-6 pb-12">
                <FormKit
                  id="thisForm"
                  type="form"
                  action="https://crm.zoho.com/crm/WebToLeadForm"
                  name="WebToLeads5550753000001892002"
                  method="post"
                  enctype="multipart/form-data"
                  accept-charset="UTF-8"
                  :form-class="'grid lg:grid-cols-2 gap-4 '"
                  :submit-attrs="{
                    inputClass: 'btn btn-primary ',
                    outerClass: 'max-w-xl mx-auto text-center mt-4',
                  }"
                  :actions="false"
                  incomplete-message="Please fill in all required fields."
                >
                  <!-- START: Do not modify this code. -->
                  <FormKit
                    type="text"
                    style="display: none"
                    name="xnQsjsdp"
                    value="4248f013feb7618574c6d4ad305ed61dfcda44d040b3a5f24e29517f1c11c978"
                  />
                  <FormKit id="zc_gad" type="hidden" name="zc_gad" value="" />
                  <FormKit
                    type="text"
                    style="display: none"
                    name="xmIwtLD"
                    value="803ac109a10fe4a76477444ba344c92468290f948953ac165c1bb5e914fa0876"
                  />
                  <FormKit
                    type="text"
                    style="display: none"
                    name="actionType"
                    value="TGVhZHM="
                  />
                  <FormKit
                    type="text"
                    style="display: none"
                    name="returnURL"
                    :value="guides[selectedModal].url"
                  />
                  <!-- END: Do not modify this code. -->

                  <FormKit
                    id="First_Name"
                    type="text"
                    name="First Name"
                    label="First Name*"
                    maxlength="40"
                    validation="required"
                  />

                  <FormKit
                    id="Last_Name"
                    type="text"
                    name="Last Name"
                    label="Last Name*"
                    maxlength="80"
                    validation="required"
                  />
                  <FormKit
                    id="Email"
                    type="email"
                    ftype="email"
                    name="Email"
                    label="Email*"
                    maxlength="100"
                    validation="required"
                  />
                  <FormKit
                    id="Mobile"
                    type="text"
                    name="Mobile"
                    label="Mobile Number (For WhatsApp)*"
                    maxlength="30"
                    validation="required"
                  />
                  <FormKit
                    id="Company"
                    type="text"
                    name="Company"
                    label="Company*"
                    maxlength="200"
                    validation="required"
                  />
                  <FormKit
                    id="Designation"
                    type="text"
                    name="Designation"
                    label="Designation*"
                    maxlength="100"
                    validation="required"
                  />
                  <div/>
                  <div class="grid grid-cols-2 gap-4 lg:col-span-2">
                    <button
                      class="btn lg:w-48 btn-primary lg:justify-self-end"
                      type="submit"
                    >
                      Download
                    </button>
                    <button
                      class="btn lg:w-48 btn-primary"
                      type="button"
                      @click="$formkit.reset('thisForm')"
                    >
                      Reset
                    </button>
                  </div>
                </FormKit>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// import Swal from "sweetalert2";
// import { createInput } from "@formkit/vue";
// import zohoMultiselect from "~/components/Custom/FormKitMultiselect.vue";

// const customMultiselect = createInput(zohoMultiselect, {
//   props: ["selectOptions"],
// });
const form = ref({});

const modalOpened = ref(false);
const selectedModal = ref(0);

const openDownloadModal = (index) => {
  selectedModal.value = index;
  modalOpened.value = true;
};

// function submitForm() {
//   // retrieve the core node (several ways to do this):
//   const node = form.value.node;
//   // submit the form!
//   node.submit();
// }

// const submitHandler = () => {
//   // convert form into formData
//   const formData = new FormData();
//   for (const key in form.value) {
//     formData.append(key, form.value[key]);
//   }
//   fetch("https://crm.zoho.com/crm/WebToLeadForm", {
//     method: "POST",
//     body: formData,
//   })
//     .then((res) => res.json())
//     .then((res) => {
//     });
//   // navigateTo(guides[selectedModal].url);
//   window.location.href = guides[selectedModal.value].url;
// };

function formSubmitPopup() {
  Swal.fire({
    icon: "success",
    title: "Success",
    text: "Thank you for your submission",
    timer: 1500,
    timerProgressBar: true,
  });
}

const guides = [
  {
    id: 1,
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1679295181/website/sharing-knowledge%20page/guide-1_r8pok9-min_sraxdw.jpg",
    title: "Headhunting Company vs. Job Portal",
    url: "https://seekers.my/blog/untitled/",
  },
  {
    id: 2,
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1679295181/website/sharing-knowledge%20page/guide-2_anndym-min_cungvx.jpg",
    title: "Methods & Tips to Hire",
    url: "https://seekers.my/blog/it-recruitment/",
  },
  {
    id: 3,
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1679295180/website/sharing-knowledge%20page/guide-3_als752-min_telr0k.jpg",
    title: "Interview Invitation & Confirmation",
    url: "https://seekers.my/blog/schedule-interviews-properly-the-templates-methods-for-free/",
  },
  {
    id: 4,
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1679295180/website/sharing-knowledge%20page/guide-4_tgmmqr-min_a3pwpn.jpg",
    title: "Complete Guideline 2021 in Malaysia",
    url: "https://seekers.my/blog/offer-letter/",
  },
];
</script>
