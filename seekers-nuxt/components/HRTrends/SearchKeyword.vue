<template>
  <div id="search-by-keywords" class="md:col-span-2">
    <div class="bg-success p-4 rounded-md">
      <p class="font-semibold mb-2">Search by Keywords</p>
      <div class="flex items-center bg-white rounded-md mb-6 px-3">
        <Icon
          name="heroicons:magnifying-glass-solid"
          class="text-primary h-6 w-6"
        />
        <input
          id="search_keyword"
          v-model="searchKeyword"
          name="search_keyword"
          type="text"
          placeholder="keywords"
          class="input input-ghost text-xs w-full focus:outline-none ml-0"
        >
      </div>

      <div
        v-if="searchKeyword"
        id="search-results"
        class="p-2 bg-white rounded -mt-4 mb-6"
      >
        <div v-if="isSearching">Loading...</div>
        <div v-for="hit in result.hits.splice(0, 5)" v-else class="text-sm">
          <div class="hover:bg-success p-2 rounded-md">
            <nuxt-link :to="hit.url" class="font-semibold">{{
              hit.title
            }}</nuxt-link>
          </div>
        </div>
      </div>

      <!-- CATEGORIES -->
      <div class="mb-6">
        <p class="font-semibold mb-2">Categories</p>
        <div class="text-sm">
          <ul class="list-disc list-inside grid gap-2">
            <li>
              <a
                href="https://seekers.my/blog/tag/hiring-advice/"
                target="_blank"
                >Hiring Advice</a
              >
            </li>
            <ul class="px-6 grid gap-1">
              <li>
                -
                <a
                  href="https://seekers.my/blog/tag/labour-law/"
                  target="_blank"
                  >Labour Law</a
                >
              </li>
              <li>
                -
                <a href="https://seekers.my/blog/tag/others/" target="_blank"
                  >Others</a
                >
              </li>
            </ul>
            <li>
              <a
                href="https://seekers.my/blog/tag/templates-guides/"
                target="_blank"
                >Templates & Guides</a
              >
            </li>
            <li>
              <a href="https://seekers.my/blog/tag/expatriate/" target="_blank"
                >Expatriate</a
              >
            </li>
          </ul>
        </div>
      </div>

      <!-- RECENT POST -->
      <div class="">
        <p class="font-semibold mb-2">Recent Post</p>
        <div class="text-xs">
          <nuxt-link
            v-for="blog in blogData?.posts"
            :to="blog?.url"
            target="_blank"
            class="flex gap-4 items-center mb-4 last:mb-2"
          >
            <img
              :src="blog?.feature_image.replace('/images', '/images/size/w300')"
              alt=""
              class="h-14 w-14 rounded-md object-cover"
            >
            <div>
              <p class="font-semibold">{{ blog?.title }}</p>
              <p>
                {{ dayjs(blog?.updated_at).format("DD MMMM YYYY") }}
              </p>
            </div>
          </nuxt-link>
        </div>
      </div>

      <!-- TAGS -->
      <!-- <div>
        <p class="font-semibold mb-2">Tags</p>
        <p
          v-for="tag in tags"
          class="btn no-animation cursor-default ml-2 mb-2 text-sm"
        >
          {{ tag }}
        </p>
      </div> -->
    </div>
    <DevOnly>
      <!-- <pre>{{ blogData }}</pre> -->
    </DevOnly>
  </div>
</template>

<script setup lang="ts">
import { watchDebounced } from "@vueuse/core";
import dayjs from "dayjs";

const { result, search } = useAlgoliaSearch("seekers_ghost_blog");
const searchKeyword = useState(() => "");
const isSearching = useState(() => false);

watch(searchKeyword, () => {
  if (searchKeyword) isSearching.value = true;
});

watchDebounced(
  searchKeyword,
  async () => {
    await search({ query: searchKeyword.value });
    isSearching.value = false;
  },
  { debounce: 500, maxWait: 1000 },
);

const { data: blogData } = useBlog().latestPosts();

const tags = [
  "jobs",
  "recruitments",
  "employers",
  "interview",
  "recruiter",
  "skills",
];
</script>
