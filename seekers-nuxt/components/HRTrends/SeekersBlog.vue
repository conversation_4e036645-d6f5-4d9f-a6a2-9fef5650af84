<template>
  <section id="seekers-blog" class="md:max-w-7xl md:mx-auto m-4 md:my-24">
    <h2 class="font-semibold text-blue-800 mb-4">Seekers Blog</h2>
    <div class="md:flex mb-8">
      <p>
        Perform like a professional HR? It's all about the modern knowledge of
        smart tools.<br >Let's catch up the new trend & latest news here.
      </p>
      <nuxt-link to="/hrtrends" no-prefetch class="md:ml-auto text-blue-600"
        >Browse All ></nuxt-link
      >
    </div>

    <div class="grid md:grid-cols-4 md:max-w-7xl md:mx-auto gap-4">
      <div v-for="post in blogData?.posts">
        <nuxt-link :to="post.url" target="blank">
          <img
            :src="post.feature_image.replace('/images', '/images/size/w300')"
            alt=""
            class="rounded-md w-full"
          >
          <p class="text-xs pt-2">
            {{ dayjs(post.updated_at).format("DD MMMM YYYY") }}
          </p>
          <p class="pt-1">{{ post.title }}</p>
        </nuxt-link>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const { data: blogData } = useBlog().getSeekersBlogSection();
</script>
