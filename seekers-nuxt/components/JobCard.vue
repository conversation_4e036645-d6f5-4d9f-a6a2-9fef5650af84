<template>
  <div>
    <nuxt-link :to="goToJobPage" no-prefetch
      class="flex flex-col gap-2 bg-white items-center border m-4 pt-3 pb-6 rounded-md hover:drop-shadow-sm transition duration-300 ease-in-out">
      <div class="w-full grid grid-rows-2 grid-flow-col gap-y-1 relative overflow-hidden pr-2">
        <p v-if="job?.features !== null" class="badge badge-success py-3 px-3 absolute -left-2 text-xs">
          Featured
        </p>
        <p v-if="job?.position_type == 'permanent'" class="badge badge-info py-3 px-3 justify-self-end text-xs">
          Full Time
        </p>
        <p v-else class="invisible badge badge-info py-3 px-3 justify-self-end" />
      </div>
      <div v-if="job?.company?.logo ? directusAssetsUrl(job?.company?.logo) : null" class="avatar -mt-8">
        <div class="w-24 rounded-full border">
          <img :src="directusAssetsUrl(job?.company?.logo)" alt="" class="w-full" style="object-fit: contain" />
        </div>
      </div>
      <div v-else class="avatar placeholder -mt-8">
        <div class="bg-neutral-focus text-neutral-content rounded-full w-24">
          <span class="text-3xl">{{ job?.title }}</span>
        </div>
      </div>

      <div class="flex flex-col flex-grow gap-1 text-center px-2">
        <h3 class="mt-2 h-8 text-xs text-primary">{{ job?.industry?.name }}</h3>
        <div class="flex justify-center items-center my-1 h-24 font-semibold line-clamp-2">
          {{ job?.title }}
        </div>
        <p class="mb-4 text-xs font-light">
          <Icon name="heroicons:map-pin" class="w-4 h-4 mr-2 text-gray-500" />{{
            job?.company?.city || "Malaysia "
          }}
        </p>
        <div class="flex gap-2 w-full h-4 justify-center">
          <p v-for="tag in job?.tags?.slice(0, 2)" class="badge badge-sm text-xs break-keep capitalize line-clamp-1">
            {{ tag }}
          </p>
          <p v-if="job?.tags_data?.length > 2" class="badge badge-sm text-xs break-keep capitalize line-clamp-1">
            {{ job?.tags_data?.length - 2 }} +
          </p>
        </div>
      </div>
    </nuxt-link>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();

const { directusAssetsUrl } = useHelper();
const { job } = defineProps({
  job: Object,
});

// function closingSoon(date: string) {
//   // return true if date is more than 21 days old from today
//   return dayjs().diff(dayjs(date), "day") > 21;
// }

// // return true if date is within 24hours from now
// function isNew(date: string) {
//   if (date) {
//     return dayjs().diff(dayjs(date), "day") < 1;
//   } else {
//     return false;
//   }
// }

const goToJobPage = computed(() => {
  //if url has word recruiter, go to /recruiter/dashboard/jobs/${job_slug}
  if (route.fullPath.includes("recruiter")) {
    return `/recruiter/dashboard/jobs/${job?.slug}`;
  } else {
    return `/jobs/${job?.slug}`;
  }
  //else go to candidate job page
});
</script>
