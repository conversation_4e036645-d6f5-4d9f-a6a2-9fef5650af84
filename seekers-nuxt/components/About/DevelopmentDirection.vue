<template>
  <div class="p-10">
    <h3 class="text-primary text-2xl font-semibold mb-4">
      Development Direction
    </h3>
    <p class="text-sm mb-2">3 Players, 3 Issues, Resolve With 1 Platform.</p>
    <div class="grid md:grid-rows-1 md:grid-cols-3 items-center gap-4">
      <div v-for="development in developments">
        <img
          :src="development.img"
          :alt="development.alt"
          class="mx-auto p-4 h-40 w-46"
        >
        <nuxt-link :to="development.to">
          <div class="bg-success/50 p-2 rounded-md">
            <p class="text-primary font-semibold mb-2">
              {{ development.title }}
            </p>
            <p class="text-sm">{{ development.detail }}</p>
            <div class="text-end">
              <Icon name="heroicons:arrow-long-right" />
            </div>
          </div>
        </nuxt-link>
      </div>
    </div>
  </div>
</template>

<script setup>
const developments = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675066867/website/about/direction-1_uyu7nt.png",
    alt: "Job Seekers",
    title: "Job Seekers",
    detail: `Human Resource is one of the core capitals of this world. Seekers' technology and team will assist people to find more career opportunities fairly, at the right time.`,
    to: "/",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675066867/website/about/direction-2_rjmwug.png",
    alt: "Employers",
    title: "Employers",
    detail:
      "Only 5% of Job Seekers are searching for job posts. They provide Headhunting Technology to pursue maximum hiring results by reaching the 95% of passive candidates.",
    to: "/employer",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1675066867/website/about/direction-3_fdhg1y.png",
    alt: "Recruiters",
    title: "Recruiters",
    detail:
      "To keep providing a sustainable income source, they focus on headhunting which requires human-touch forever, and is performable by anyone from anywhere.",
    to: "/recruiter",
  },
];
</script>
