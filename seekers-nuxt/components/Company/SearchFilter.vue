<template>
  <div id="job-filter">
    <div class="sticky top-12 flex flex-col gap-4">
      <!-- Keywords -->
      <div>
        <p class="mb-2 font-semibold">
          Search by Keywords<span v-if="searchKeyword != ''"
            class="text-xs font-light ml-2 text-error-content cursor-pointer" @click="searchKeyword = ''">Clear</span>
        </p>
        <input v-model="searchKeyword" type="text" name="search" placeholder="Job title or keywords"
          class="input w-full text-xs" />
      </div>

      <!-- States -->
      <div>
        <p class="mb-2 font-semibold">Location<span v-if="searchFilter.state != ''"
            class="text-xs font-light ml-2 text-error-content cursor-pointer"
            @click="searchFilter.state = ''">Clear</span></p>
        <select class="select w-full outline-0" v-model="searchFilter.state" placeholder="">
          <option class="options" :value="''">All</option>
          <option class="options" v-for="state in MALAYSIAN_STATES" :value="state.value">
            {{ capitalize(state.value) }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const searchKeyword = ref("");
const { searchFilter } = useCompanies()
watchDebounced(
  searchKeyword,
  async () => {
    searchFilter.value.keywords = searchKeyword.value;
  },
  { debounce: 500, maxWait: 3000 }
);
</script>