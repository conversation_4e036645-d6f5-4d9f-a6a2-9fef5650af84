<template>
  <nuxt-link :to="`/jobs/${job?.slug}`" class="flex items-center shadow rounded-md p-4 gap-4 text-sm mb-3">
    <img :src="directusAssetsUrl(job.company.logo)" alt="" class="w-10 h-10 rounded-md object-contain">
    <div>
      <div class="flex items-center">
        <p class="font-semibold">
          {{ job?.title }}
        </p>
      </div>
      <div class="grid md:flex md:items-center md:gap-4 text-xs my-2">
        <p class="text-gray-500 capitalize">
          <Icon name="heroicons:briefcase" class="w-4 h-4 mr-1" />{{
            job?.role
          }}
        </p>
        <p v-if="job?.state !== null" class="text-gray-500 my-1 md:my-0">
          <Icon name="heroicons:map-pin" class="w-4 h-4 mr-1" />{{ job.state }}
        </p>
        <p class="text-gray-500 mb-1 md:mb-0">
          <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />{{
            getRelatedDate(job?.date_updated || '')
          }}
        </p>
        <p class="text-gray-500">
          <Icon name="heroicons:banknotes" class="w-4 h-4 mr-1" />{{
            `RM ${job?.salary_min} - RM ${job?.salary_max}`
          }}
        </p>
      </div>
      <div class="flex items-center gap-4">
        <p class="badge badge-success text-xs">Full Time</p>
        <p class="badge badge-info text-xs">Permanent</p>
        <p v-if="closingSoon(job?.date_created || '')" class="badge badge-warning text-xs">
          Urgent
        </p>
      </div>
    </div>
  </nuxt-link>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
const { directusAssetsUrl } = useHelper();
const { job } = defineProps<{ job: Job }>()

function closingSoon(date: string) {
  // return true if date is more than 21 days old from today
  return dayjs().diff(dayjs(date), "day") > 21;
}
</script>
