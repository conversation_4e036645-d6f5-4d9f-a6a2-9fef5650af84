<template>
  <div id="job-filter">
    <div class="sticky top-12 flex flex-col gap-4">
      <!-- Keywords -->
      <div>
        <p class="mb-2 font-semibold">
          Search by Keywords<span v-if="searchKeyword != ''"
            class="text-xs font-light ml-2 text-error-content cursor-pointer" @click="searchKeyword = ''">Clear</span>
        </p>
        <input v-model="searchKeyword" type="text" name="search" placeholder="Job title or keywords"
          class="input w-full text-xs" />
      </div>

      <!-- States -->
      <div>
        <p class="mb-2 font-semibold">Location<span v-if="searchFilter.state != ''"
          class="text-xs font-light ml-2 text-error-content cursor-pointer" @click="searchFilter.state = ''">Clear</span></p>
        <select class="select w-full outline-0" v-model="searchFilter.state" placeholder="">
          <option class="options" :value="''">All</option>
          <option class="options" v-for="state in MALAYSIAN_STATES" :value="state.value">
            {{ capitalize(state.value) }}
          </option>
        </select>
      </div>

      <!-- Categories -->
      <div>
        <p class="mb-2 font-semibold">Category<span v-if="searchFilter.role != ''"
          class="text-xs font-light ml-2 text-error-content cursor-pointer" @click="searchFilter.role = ''">Clear</span></p>
        <select v-model="searchFilter.role" class="select w-full outline-0" placeholder="">
          <option class="options" disabled selected :value="''">
            Choose a category
          </option>
          <option v-for="category in JOB_CATEGORIES" :key="category.id" :value="category.id">
            {{ category.name }}
          </option>
        </select>
      </div>

      <!-- Salary slider -->
      <div v-if="filterExpanded || !isMobile" id="salary-slider">
        <p class="mb-2 font-semibold">Min Salary</p>
        <input
          id="fromSlider"
          v-model="rangeSalaryMin"
          type="range"
          class="range range-xs range-primary w-full"
          :min="0"
          :max="15000"
        >
        <!-- <CustomRangeSlider
          :min="0"
          :max="15000"
          range-color="#34A853"
          slider-color="#e0e0e0"
          :from="searchFilter.salary_min"
          :to="searchFilter.salary_max"
          @from-change="searchFilter.salary_min = $event"
          @to-change="searchFilter.salary_max = $event"
        /> -->
        <p
          class="rounded-lg p-2 bg-gray-300 w-max mx-auto text-xs font-semibold text-accent px-2"
        >
          {{ rangeSalaryMin == 0 ? 'All' : `RM${rangeSalaryMin}` }}
        </p>
      </div>

      <div id="job-type-toggles">
        <p class="mb-2 font-semibold">Job Type</p>
        <div class="grid gap-4">
          <div class="flex gap-4 items-center text-sm">
            <input v-model="includeFullTime" type="checkbox" class="toggle scale-75 toggle-primary"/>
            <span>Full Time</span>
          </div>
          <div class="flex gap-4 items-center text-sm">
            <input v-model="includeContract" type="checkbox" class="toggle scale-75 toggle-primary"/>
            <span>Contract</span>
          </div>
          <div class="flex gap-4 items-center text-sm">
            <input v-model="includeInternship" type="checkbox" class="toggle scale-75 toggle-primary"/>
            <span>Internship</span>
          </div>
        </div>
      </div>

      <!-- <div id="date-posted">
        <p class="mb-2 font-semibold">Date Posted</p>
        <div class="grid gap-4">
          <div
            class="flex gap-4 items-center text-sm"
            v-for="option in postedDateOptions"
          >
            <input
              type="radio"
              class="radio bg-white checked:radio-primary radio-sm md:radio-xs"
              v-model="searchPostedDate"
              :value="option.value"
              name="PostedDate"
            />
            <span>{{ option.text }}</span>
          </div>
        </div>
      </div> -->

      <div v-if="filterExpanded || !isMobile" id="exp-level">
        <p class="mb-2 font-semibold">Experience Level</p>
        <div class="grid gap-4">
          <div class="flex gap-4 items-center text-sm">
            <input v-model="selectedAllExperienceLevels" type="checkbox"
              class="checkbox rounded bg-white checked:checkbox-primary checkbox-sm md:checkbox-xs"
              @click="clearOtherLevelCheckboxes" />
            <span>All</span>
          </div>
          <div v-for="option in experienceLevels" class="flex gap-4 items-center text-sm">
            <input v-model="option.checked" type="checkbox"
              class="checkbox rounded bg-white checked:checkbox-primary checkbox-sm md:checkbox-xs" />
            <span>{{ option.text }}</span>
          </div>
        </div>
      </div>
      <!-- <button
        v-if="!filterExpanded && isMobile"
        class="mt-2 md:hidden btn"
        @click="filterExpanded = true"
      >
        More Filters
      </button>
      <button
        v-if="filterExpanded && isMobile"
        class="mt-8 md:hidden btn btn-primary"
        @click="applyFilterMobile"
      >
        Apply Filter
      </button> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const { isMobile } = useDevice();
const route = useRoute();
const router = useRouter();
const searchKeyword = ref("");
const { searchFilter } = useJobs()
const filterExpanded = ref(false);
const selectedAllExperienceLevels = ref(true);
const includeContract = ref(false);
const includeFullTime = ref(false);
const includeInternship = ref(false);
const rangeSalaryMin = ref(searchFilter.value.salary_min);

const experienceLevels = ref<Array<typeof EXPERIENCE_LEVELS[number] & { checked: boolean }>>([
  { text: "Entry Level", value: "entry_level" , checked: false },
  { text: "Junior Executive", value: "junior_executive" , checked: false },
  { text: "Senior Executive", value: "senior_executive", checked: false },
  { text: "Manager", value: "manager", checked: false },
  { text: "Senior Manager", value: "senior_manager", checked: false },
  { text: "C-Level", value: "c_level", checked: false },
]);

// Add a watcher for searchKeyword with debounce
watchDebounced(
  searchKeyword,
  async () => {
    searchFilter.value.keywords = searchKeyword.value;
  },
  { debounce: 500, maxWait: 3000 }
);
watchDebounced(
  rangeSalaryMin,
  async () => {
    searchFilter.value.salary_min = rangeSalaryMin.value;
  },
  { debounce: 500 }
);

function clearOtherLevelCheckboxes() {
  experienceLevels.value.forEach(level => level.checked = false);
}

// Add a watcher for experience levels
watch(
  experienceLevels,
  () => {
    const checkedLevels = experienceLevels.value.filter(level => level.checked);
    selectedAllExperienceLevels.value = checkedLevels.length === 0;
    searchFilter.value.experience_levels = checkedLevels.map(level => level.value);
  },
  { deep: true }
);

// Add a watcher for position types
watch(
  [includeContract, includeFullTime, includeInternship],
  () => {
    const positionTypes = [];
    if (includeContract.value) positionTypes.push("contract");
    if (includeFullTime.value) positionTypes.push("permanent");
    if (includeInternship.value) positionTypes.push("internship");
    searchFilter.value.position_types = positionTypes;
  }
);
</script>
