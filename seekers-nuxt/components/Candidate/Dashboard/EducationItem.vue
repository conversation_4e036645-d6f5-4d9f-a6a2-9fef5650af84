<template>
  <CustomVerticalStep :char="addNew ? '+' : data?.institute?.charAt(0).toUpperCase()" :last-step="lastStep"
    bg-color="error" content-color="error-content" :class="{ 'opacity-50': isLoading }">
    <!-- Display mode -->
    <div v-if="!editMode && !addNew" id="education-item" class="grid pl-6 pb-6 gap-1">
      <div class="flex md:items-center w-full gap-1 md:gap-3">
        <div class="flex flex-col md:items-center md:flex-row gap-1 md:gap-3">
          <div class="md:hidden text-xs -mb-1">
            {{ dayjs(data.start).format("YYYY") }} -
            {{ dayjs(data.end).format("YYYY") }}
          </div>
          <p class="text-lg">{{ data.title }}</p>
          <div class="hidden md:block">
            <div class="bg-error text-error-content text-xs rounded-full p-1 px-2 w-max">
              {{ dayjs(data.start).format("YYYY") }} -
              {{ dayjs(data.end).format("YYYY") }}
            </div>
          </div>
        </div>
        <button v-if="editAllowed" class="btn btn-circle btn-sm md:btn-xs ml-auto md:ml-3" @click="editMode = true">
          <Icon name="heroicons:pencil" class="h-4 w-4 md:w-3 md:h-3 animate__animated animate__fadeIn" />
        </button>
      </div>
      <p class="text-error-content text-sm">
        {{ data.institute }}
      </p>
      <p class="mt-3 text-gray-500 text-sm">
        {{ data.description }}
      </p>
    </div>

    <!-- Add new -->
    <p v-if="addNew && !editMode" class="text-error-content p-1 pl-6 cursor-pointer" @click="editMode = true">
      Add New
    </p>

    <!-- Edit mode -->
    <div v-if="editMode" class="grid gap-4 pl-6 pb-12 animate__animated animate__fadeIn animate__fast">
      <select v-model="data.qualification" placeholder="Bachelor of, Master, PhD, etc." type="select"
        class="select outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96">
        <option :value="undefined" disabled>Choose Education Type</option>
        <option v-for="option in qualificationOptions" :value="option.value">
          {{ option.label }}
        </option>
      </select>
      <input v-model="data.title" placeholder="Bachelor of, Master, PhD, etc." type="text"
        class="input text-sm outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96" />
      <input v-model="data.institute" placeholder="University, College, etc." type="text"
        class="input text-sm outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96" />

      <div class="flex flex-col md:flex-row md:items-center gap-3 text-xs">
        <span> From: </span>
        <FormKit type="date" :value="dayjs(data.start).format('YYYY-MM-DD')" :input-class="'p-4 mt-0 text-xs'"
          @change="data.start = $event.target.value" />
        <span>To:</span>
        <FormKit type="date" :value="dayjs(data.end).format('YYYY-MM-DD')" :input-class="'p-4 mt-0 text-xs'"
          @change="data.end = $event.target.value" />
      </div>
      <textarea v-model="data.description"
        placeholder="Describe your education. Eg, majors, minors, labs, paper, achivements, etc." type="textarea"
        class="outline outline-gray-200 focus:outline-primary rounded p-2 w-full h-40 md:h-24 text-sm" />

      <div v-if="editMode" class="flex gap-4 flex-wrap">
        <button class="btn btn-primary px-6 md:px-12 py-2" @click="addNew ? create() : save()">
          Save
        </button>
        <button class="btn px-6 md:px-12 py-2" @click="editMode = false">
          Cancel
        </button>
        <button v-if="!addNew" class="btn" @click="deleteItem">
          <Icon name="heroicons:trash" />
        </button>
      </div>
    </div>
  </CustomVerticalStep>

  <!-- <pre>{{ value }}</pre> -->
</template>
<script setup lang="ts">
import dayjs from "dayjs";
const { value, lastStep } = defineProps<{
  value: EducationItem;
  lastStep?: boolean;
  addNew?: boolean;
  editAllowed?: boolean;
}>();
const emit = defineEmits(['changed']);
const isLoading = useState(() => false);

const qualificationOptions = useHelper().qualificationsList;
const editMode = ref(false);
const data = ref({ ...value });
const { getProfile, updateEducation, deleteEducation, createEducation } = useCandidate();

function validateData() {
  if (
    !data.value.start ||
    !data.value.end ||
    !data.value.institute ||
    !data.value.title ||
    !data.value.description
  ) {
    alert("Please fill all the fields");
    return false;
  } else {
    return true;
  }
}

async function create() {
  if (validateData()) {
    await createEducation(data.value);
    await getProfile();
    editMode.value = false;
  }
}

async function save() {
  if (validateData()) {
    await updateEducation(data.value.id as number, data.value);
    await getProfile();
    editMode.value = false;
  }
}

const deleteItem = async () => {
  await deleteEducation(data.value.id as number);
  await getProfile();
  editMode.value = false;
}
</script>
