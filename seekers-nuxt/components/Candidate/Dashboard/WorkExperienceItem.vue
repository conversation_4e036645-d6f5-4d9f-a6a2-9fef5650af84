<template>
  <CustomVerticalStep :char="addNew ? '+' : data?.company_name?.charAt(0).toUpperCase()" :last-step="lastStep"
    bg-color="success" content-color="success-content" :class="{ 'opacity-50': isLoading }">
    <!-- Display mode -->
    <div v-if="!editMode && !addNew" id="education-item" class="grid pl-6 pb-6 gap-1">
      <div class="flex md:items-center w-full gap-1 md:gap-3">
        <div class="flex flex-col md:items-center md:flex-row gap-1 md:gap-3">
          <div class="md:hidden text-xs -mb-1">
            {{ dayjs(data.start).format("YYYY") }} -
            {{ dayjs(data.end).format("YYYY") }}
          </div>
          <p class="text-lg capitalize">{{ data.job_title }}</p>
          <div class="hidden md:block">
            <div class="bg-success text-succes-content text-xs rounded-full p-1 px-2 w-max">
              {{ dayjs(data.start).format("YYYY") }} -
              {{
                data.current_job ? "Present" : dayjs(data.end).format("YYYY")
              }}
            </div>
          </div>
        </div>
        <button v-if="editAllowed" class="btn btn-circle btn-sm md:btn-xs ml-auto md:ml-3" @click="editMode = true">
          <Icon name="heroicons:pencil" class="h-4 w-4 md:w-3 md:h-3 animate__animated animate__fadeIn" />
        </button>
      </div>
      <p class="text-success-content text-sm capitalize">
        {{ data.company }}
      </p>
      <p class="mt-3 text-gray-500 text-sm">{{ data.description }}</p>
    </div>

    <!-- Add new -->
    <p v-if="addNew && !editMode" class="text-success-content p-1 pl-6 cursor-pointer" @click="editNew">
      Add New
    </p>

    <!-- Edit mode -->
    <div v-if="editMode" class="grid gap-4 pl-6 pb-12 animate__animated animate__fadeIn animate__fast">
      <input v-model="data.job_title" placeholder="Role title or Position" type="text"
        class="input text-sm outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96" />
      <input v-model="data.company" placeholder="Company Name" type="text"
        class="input text-sm outline outline-gray-200 focus:outline-primary rounded p-2 w-full md:w-96" />

      <div class="flex flex-col md:flex-row md:items-center gap-3 text-xs">
        <span> From: </span>
        <FormKit type="date" :value="dayjs(data.start).format('YYYY-MM-DD')" :input-class="'p-4 mt-0 text-xs'"
          @change="data.start = $event.target.value" />
        <span>To:</span>
        <FormKit type="date" :value="dayjs(data.end).format('YYYY-MM-DD')" :input-class="'p-4 mt-0 text-xs'"
          @change="data.end = $event.target.value" :disabled="data.current_job == true" />
      </div>
      <div class="flex flex-col items-center md:flex-row gap-3 text-xs">
        <span>Current Job:</span>
        <FormKit type="checkbox" v-model="data.current_job" />
      </div>
      <textarea v-model="data.description" placeholder="Describe your responsibilities and achievements" type="textarea"
        class="outline outline-gray-200 focus:outline-primary rounded p-2 w-full h-40 md:h-24 text-sm" />

      <div v-if="editMode" class="flex gap-4 flex-wrap">
        <button class="btn btn-primary px-6 md:px-12 py-2" @click="addNew ? create() : save()">
          Save
        </button>
        <button class="btn px-6 md:px-12 py-2" @click="editMode = false">
          Cancel
        </button>
        <button v-if="!addNew" class="btn" @click="deleteItem">
          <Icon name="heroicons:trash" />
        </button>
      </div>
    </div>
  </CustomVerticalStep>

  <!-- <pre>{{ data }}</pre> -->
  <!-- <pre>{{ candidateProfileId }}</pre> -->
</template>
<script setup lang="ts">
import dayjs from "dayjs";

const { value, lastStep } = defineProps<{
  value: WorkExperienceItem;
  lastStep?: boolean;
  addNew?: boolean;
  editAllowed?: boolean;
}>();
const isLoading = useState(() => false);

const editMode = ref(false);
const data = ref({ ...value });
const { getProfile, deleteWorkExperience, createWorkExperience, updateWorkExperience } = useCandidate();

function validateData() {
  if (
    !data.value.start ||
    !data.value.end ||
    !data.value.job_title ||
    !data.value.company ||
    !data.value.description
  ) {
    alert("Please fill all the fields");
    return false;
  } else {
    return true;
  }
}

function editNew() {
  data.value = {
    start: dayjs().format("YYYY-MM-DD"),
    end: dayjs().format("YYYY-MM-DD"),
    company: "",
    job_title: "",
    description: "",
    current_job: false,
  };
  editMode.value = true;
}
async function create() {
  if (validateData()) {
    isLoading.value = true;
    await createWorkExperience(data.value);
    await getProfile();
    isLoading.value = false;
    editMode.value = false;
  }
}

async function save() {
  if (validateData()) {
    isLoading.value = true;
    await updateWorkExperience(data.value);
    await getProfile();
    isLoading.value = false;
    editMode.value = false;
  }
}

const deleteItem = async () => {
  await deleteWorkExperience(data.value.id as number);
  await getProfile();
  editMode.value = false;
}
</script>
