<template>
  <div id="candidate-portfolio">
    <div class="flex items-center mb-4 gap-4">
      <h3 class="font-semibold">Portfolio</h3>
      <nuxt-link to="/candidate/dashboard/portfolio">
        <Icon name="heroicons:pencil" class="w-5 h-5" />
      </nuxt-link>
    </div>
    <div class="grid grid-cols-2 md:flex gap-3 md:gap-6">
      <div
        v-for="item in data"
        class="w-full h-32 md:w-40 md:h-32 object-cover rounded-lg hover:brightness-90 transition-all cursor-pointer drop-shadow-xl"
      >
        <div
          v-if="isImage(item)"
          class="cursor-pointer border rounded-lg w-full h-32 md:w-40 md:h-32 grid justify-center text-center items-center text-accent"
        >
          <img
            :src="item.url || item.link"
            :alt="`portfolio-img-${item.title}`"
          >
        </div>
        <a
          v-if="!isImage(item)"
          :href="getUrl(item.link ?? item.url)"
          target="_blank"
        >
          <div
            class="cursor-pointer border rounded-lg w-full h-32 md:w-40 md:h-32 grid justify-center text-center items-center text-accent"
          >
            <p>
              {{ item?.title }}<br >
              <span class="text-xs">{{ item.description }}</span>
            </p>
          </div>
        </a>
      </div>
      <button
        v-if="data?.length < 4"
        class="cursor-pointer border rounded-lg w-full h-32 md:w-40 md:h-32 grid justify-center items-center text-accent hover:brightness-90"
        @click="navigateTo('/candidate/dashboard/portfolio')"
      >
        <Icon name="heroicons:document-plus-20-solid" class="w-6 h-6" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
const { data } = defineProps({
  data: {
    type: Array<any>,
    required: true,
  },
});

const getUrl = (link: string) => {
  // If item.link , return item.link . Else return item.url
  link.replace(/^(?!(?:https?:\/\/))/, "https://");

  // format url if missing http/https
  if (!link.includes("https")) {
    return `https://${link}`;
  }
  return link;
};

//method to check if file extention is image
const isImage = (item: any) => {
  let ext = "";
  if (item.url) {
    ext = item.url.split(".").pop();
  } else if (item.link) {
    ext = item.link.split(".").pop();
  } else {
    return false;
  }
  return (
    ext.includes("jpg") ||
    ext.includes("png") ||
    ext.includes("gif") ||
    ext.includes("jpeg") ||
    ext.includes("svg") ||
    ext.includes("webp")
  );
};
</script>
