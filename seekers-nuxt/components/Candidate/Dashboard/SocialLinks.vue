<template>
  <div class="bg-info p-3 md:p-6 xl:p-8 px-6 rounded-lg text-sm">
    <div class="flex gap-3 items-center">
      <p class="mr-auto">
        Social Media
        <span>
          <Icon v-if="!editMode" name="heroicons:pencil"
            class="ml-2 w-4 h-4 animate__animated animate__fadeIn cursor-pointer" @click="editSocialLinks" />
        </span>
      </p>

      <div v-if="!editMode" class="flex gap-3" >
        <a v-if="socials?.facebook" :href="appendhttps(socials?.facebook)" target="_blank">
          <Icon name="bxl:facebook" class="w-6 h-6 text-[#4267B2]" />
        </a>
        <a v-if="socials?.twitter" :href="appendhttps(socials?.twitter)" target="_blank">
          <Icon name="bxl:twitter" class="w-6 h-6 text-[#1DA1F2]" />
        </a>
        <a v-if="socials?.instagram" :href="appendhttps(socials?.instagram)" target="_blank">
          <Icon name="bxl:instagram" class="w-6 h-6 text-[#833AB4]" />
        </a>
        <a v-if="socials?.linkedin" :href="appendhttps(socials?.linkedin)" target="_blank">
          <Icon name="bxl:linkedin" class="w-6 h-6" />
        </a>
        <a v-if="socials?.github" :href="appendhttps(socials?.github)" target="_blank">
          <Icon name="bxl:github" class="w-6 h-6" />
        </a>
        <a v-if="socials?.website" :href="appendhttps(socials?.website)" target="_blank">
          <Icon name="heroicons:globe-alt" class="w-6 h-6" />
        </a>
      </div>
    </div>
    <div v-if="editMode" class="mt-6 grid grid-cols-[1fr_16fr] gap-4 items-center text-accent text-xs">
      <Icon name="bxl:facebook" class="h-5 w-5" /><input v-model="socials.facebook" class="input input-sm py-5"
        type="text" name="facebook" @keyup.enter="save" />
      <Icon name="bxl:twitter" class="h-5 w-5" /><input v-model="socials.twitter" class="input input-sm py-5"
        type="text" name="twitter" @keyup.enter="save" />
      <Icon name="bxl:instagram" class="h-5 w-5" /><input v-model="socials.instagram" class="input input-sm py-5"
        type="text" name="instagram" @keyup.enter="save" />
      <Icon name="bxl:linkedin" class="h-5 w-5" /><input v-model="socials.linkedin" class="input input-sm py-5"
        type="text" name="linkedin" @keyup.enter="save" />
      <Icon name="bxl:github" class="h-5 w-5" /><input v-model="socials.github" class="input input-sm py-5" type="text"
        name="github" @keyup.enter="save" />
      <Icon name="heroicons:globe-alt" class="h-5 w-5" /><input v-model="socials.website" class="input input-sm py-5"
        type="text" name="website" @keyup.enter="save" />
      <button class="btn btn-primary col-span-2" @click="save">Save</button>
    </div>
  </div>
</template>

<script setup lang="ts">
const editMode = useState<boolean>();
const { updateProfile } = useCandidate();
const { socials } = defineProps<{ socials: Socials }>();

function editSocialLinks() {
  editMode.value = true;
}
function appendhttps(url: string) {
  // check if url has https, if not append.
  let newUrl = url;
  if (!url.includes("https")) {
    newUrl = "https://" + url;
  }
  //if url has http, not https, replace http with https
  if (url.includes("http") && !url.includes("https")) {
    newUrl = url.replace("http", "https");
  }
  return newUrl;
}

async function save() {
  await updateProfile(socials)
  editMode.value = false;
}
</script>
