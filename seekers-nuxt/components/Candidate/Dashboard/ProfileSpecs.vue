<template>
  <div class="grid gap-3">
    <div class="flex text-sm gap-6">
      <Icon name="heroicons:calendar" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Experience</p>
        <p class="font-light text-accent capitalize">
          {{
            specs?.years_of_experience ? specs?.years_of_experience : "-"
          }}
          Years
        </p>
      </div>
    </div>
    <div class="flex text-sm gap-6">
      <Icon name="hugeicons:coins-01" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Current Salary</p>
        <p class="font-light text-accent capitalize">
          RM{{ specs?.salary_current ? specs?.salary_current : "-" }}
        </p>
      </div>
    </div>
    <div class="flex text-sm gap-6">
      <Icon name="cil:cash" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Expected Salary</p>
        <p class="font-light text-accent capitalize">
          RM{{ specs?.salary_expected ? specs?.salary_expected : "-" }}
        </p>
      </div>
    </div>
    <div class="flex text-sm gap-6">
      <Icon name="heroicons:finger-print-solid" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Gender</p>
        <p class="font-light text-accent capitalize">{{ specs?.gender }}</p>
      </div>
    </div>
    <div class="flex text-sm gap-6">
      <Icon name="heroicons:phone" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Mobile</p>
        <p class="font-light text-accent capitalize">{{ specs?.mobile }}</p>
      </div>
    </div>
    <div class="flex text-sm gap-6">
      <Icon name="heroicons:clock" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Age</p>
        <p class="font-light text-accent capitalize">
          <!-- {{ calculateAge(specs?.birth_year) }} -->
          {{ dayjs().diff(dayjs(specs?.birthday), "year") }} Years
        </p>
      </div>
    </div>
    <div class="flex text-sm gap-6">
      <Icon name="heroicons:language" class="w-6 h-6 text-primary min-w-6" />
      <div>
        <p class="mb-1">Languages</p>
        <p
          v-if="specs?.languages"
          v-for="language in specs?.languages"
          class="font-light text-accent capitalize break-word"
        >
          {{ language }}
        </p>
        <p
          v-else
          class="btn btn-text"
          @click="navigateTo('/candidate/dashboard/settings')"
        >
          Add Languages
        </p>
      </div>
    </div>
    <div v-if="specs?.is_local" class="flex text-sm gap-6">
      <Icon name="humbleicons:flag" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Nationality</p>
        <p class="font-light text-accent capitalize">
          {{ specs?.is_local ? "Malaysian" : "Expatriate" }}
        </p>
      </div>
    </div>
    <div v-if="specs?.state" class="flex text-sm gap-6">
      <Icon name="humbleicons:map" class="w-6 h-6 text-primary" />
      <div>
        <p class="mb-1">Location</p>
        <p class="font-light text-accent capitalize">
          {{ specs?.state }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import dayjs from "dayjs";

const { specs } = defineProps({
  specs: {
    type: Object,
  },
});

// function calculate age based on birth year
// function calculateAge(birthYear) {
//   return new Date().getFullYear() - birthYear;
// }
</script>
