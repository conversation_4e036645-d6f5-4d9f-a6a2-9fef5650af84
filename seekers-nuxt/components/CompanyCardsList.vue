<template>
  <div>
    <div class="md:grid lg:grid-cols-2 xl:grid-cols-3 md:mx-auto md:max-w-7xl mb-4 animate__animated animate__fadeIn">
      <CompanyCard v-for="company in companyList" :company="company" :img="directusAssetsUrl(company.logo)" />
    </div>
  </div>
</template>

<script setup lang="ts">
const { directusAssetsUrl } = useHelper();
const { companyList } = defineProps<{
  companyList: Company[] | null;
}>()

</script>
