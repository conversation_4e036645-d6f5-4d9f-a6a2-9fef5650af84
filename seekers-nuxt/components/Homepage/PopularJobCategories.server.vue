<template>
  <section id="popular-categories" class="bg-success bg-opacity-50 py-24">
    <h2 class="text-center font-semibold mb-8">Popular Job Categories</h2>

    <div v-if="categories" class="grid md:grid-cols-3 md:mx-auto md:max-w-7xl gap-4 mx-4">
      <nuxt-link v-for="category in categories" :to="'/jobs?category=' + category.slug"
        class="grid grid-rows-1 grid-cols-[2fr_10fr] border hover:drop-shadow transition-all cursor-pointer rounded-md bg-white items-center p-4 gap-4 group hover:text-primary">
        <img :src="directusAssetsUrl(category.image)" class="w-16 h-16 object-contain" alt="Image of Something">

        <div>
          <h3 class="font-semibold text-base">{{ category.name }}</h3>
        </div>
      </nuxt-link>
    </div>
    <div v-else>
      <div class="grid md:grid-cols-3 md:mx-auto md:max-w-7xl gap-4 mx-4">
        <div v-for="i in 9"
          class="h-24 animate-pulse border hover:drop-shadow rounded-md bg-gray-300 items-center p-4 gap-4" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const { $directus, $readItems } = useNuxtApp();
const { directusAssetsUrl } = useHelper();
const categories = await $directus.request($readItems('job_categories'));
</script>
