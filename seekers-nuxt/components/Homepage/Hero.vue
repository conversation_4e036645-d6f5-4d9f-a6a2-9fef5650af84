<template>
  <div id="homepage-hero" class="bg-gradient-to-tl from-[#A7DAB5] via-success to-[#F9FDFA]">
    <div class="grid grid-cols-1 md:grid-cols-5 p-12 xl:p-0 justify-items-center items-center max-w-7xl mx-auto">
      <div id="hero-text" class="col-span-3 scale-90 lg:scale-100">
        <h1 class="text-5xl animate__animated animate__fadeIn">
          <span class="font-semibold">FIND PROFESSIONAL JOBS</span><br>IN 3
          EASY STEPS
        </h1>
        <p class="mt-8 animate__animated animate__fadeIn animate__delay-1s">
          Find Jobs, Employment & Career Opportunities
        </p>
        <div id="searchbox"
          class="hidden lg:flex mt-8 bg-white border rounded-lg drop-shadow items-center animate__animated animate__fadeIn animate__delay-1s">
          <div class="flex items-center p-2 pl-4 flex-grow">
            <Icon name="heroicons:magnifying-glass-solid" class="text-primary h-6 w-6" />
            <label for="search_keyword" />
            <input id="search_keyword" v-model="keywords" name="search_keyword" type="text"
              placeholder="Job title or keywords" class="input input-ghost text-xs w-full focus:outline-none"
              @keyup.enter="search">
          </div>

          <div class="btn btn-primary mr-2 px-8" @click="search">Find Jobs</div>
        </div>
        <div v-if="popularTagJobs" id="popular-searches"
          class="hidden lg:flex gap-4 mt-4 items-center animate__animated animate__fadeIn animate__delay-1s">
          <div class="font-semibold text-sm">Popular Searches :</div>
          <nuxt-link v-for="tag in popularTagJobs" :key="tag.id" :to="`/jobs?tag_id=${tag.id}`"
            class="btn btn-xs !border-black text-black text-xs break-keep capitalize">
            {{ tag.name.en }}
          </nuxt-link>
        </div>

        <div class="lg:hidden my-8 flex flex-col gap-4">
          <div class="flex items-center px-2 bg-white rounded-lg border">
            <Icon name="heroicons:magnifying-glass-solid" class="text-primary h-6 w-6" />
            <input id="search_keyword_mobile" v-model="keywords" name="search_keyword_mobile" type="text" placeholder="Job title or keywords"
              class="input input-ghost text-xs w-full focus:outline-none" @keyup.enter="search">
          </div>

          <div class="btn btn-primary px-8 z-10" @click="search">Find Jobs</div>
          <div v-if="popularTagJobs" id="mobile-popular-searches" class="flex gap-2 mt-2">
            <div class="font-semibold text-xs">Popular Searches :</div>
            <div class="flex flex-wrap gap-2 w-full h-4">
              <nuxt-link v-for="tag in popularTagJobs" :key="tag.id" :to="`/jobs?tag_id=${tag.id}`"
                class="btn btn-xs !border-black text-xs text-black break-keep capitalize">
                {{ tag.name.en }}
              </nuxt-link>
            </div>
          </div>
        </div>
      </div>
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670721984/website/homepage/hero-img_uernct.png"
        alt="Hero Images"
        class="col-span-2 mt-16 h-[420px] object-contain w-full animate__animated animate__fadeIn animate__delay-2s z-10">
    </div>
    <img
      src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674496774/website/homepage/hero-white-trig_tag6ug.png"
      alt="hero-bg-trig" class="w-full h-48 -mt-40 md:-mt-28 translate-y-4">
  </div>
</template>

<script setup>
const keywords = ref("");

const { data: popularTagJobs } = {
  data: null
};

function search() {
  navigateTo(`/jobs?keywords=${keywords.value}`);
}
</script>
