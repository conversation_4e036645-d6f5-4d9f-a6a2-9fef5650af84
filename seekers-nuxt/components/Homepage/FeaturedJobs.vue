<template>
  <section id="featured-jobs" class="py-12 my-12">
    <div class="text-center">
      <h2 class="font-semibold mb-2">{{ activeTab }}</h2>
      <p class="mb-6">
        Know your worth and find the job that qualify your life
      </p>

      <div class="grid grid-flow-col auto-cols-fr gap-2 max-w-lg justify-between border rounded-full p-1 mx-auto mb-4">
        <button v-for="tab in tabs" class="btn border-0 rounded-full p-2 cursor-pointer" :class="{
          'btn-primary text-white': activeTab == tab,
          'bg-transparent': activeTab != tab,
        }" @click="activeTab = tab">
          {{ tab }}
        </button>
      </div>
      <div>
        <!-- <JobCardsList v-if="activeTab == 'Popular'" :jobs-list="highlightedJobs" :is-loading="highlightedJobsStatus == 'pending'"
          :show-pagination="false" />
        <JobCardsList v-if="activeTab == 'Latest'" :jobs-list="latestJobs" :is-loading="latestJobsStatus == 'pending'"
          :show-pagination="false" /> -->
      </div>
      <nuxt-link to="/jobs" class="btn btn-primary px-8" type="button">View All</nuxt-link>
    </div>
  </section>
</template>

<script setup lang="ts">

const activeTab = ref("Popular");

const tabs = ref(["Popular", "Latest"]);
</script>
