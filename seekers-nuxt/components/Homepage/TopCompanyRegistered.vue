<template>
  <section id="top-company" class="md:max-w-7xl md:mx-auto mb-32 mx-4">
    <h2 class="font-semibold text-center mb-2">Top Company Registered</h2>
    <div class="flex flex-col md:flex-row text-center mb-4">
      <p class="mb-4 mx-auto">
        Some of the companies we have helped recruit excellent applicants over
        the years.
      </p>
      <nuxt-link to="/company" class="text-primary">Browse All ></nuxt-link>
    </div>
    <div>
      <div
        class="flex overflow-x-scroll snap-x snap-mandatory scroll-smooth scrollbar-view pb-10"
      >
        <div
          v-for="job in nonConfidentialAndUniqueJobs"
          v-if="!pending"
          :id="'jobcarousel-id-' + job?.id"
          class="carousel-item flex-col border rounded-md mx-3 w-56 h-56 p-8 justify-center items-center"
        >
          <img
            :src="job?.company?.logo_url"
            alt=""
            class="w-16 h-16 justify-self-center my-4 object-contain"
          >
          <h3 class="font-semibold text-base text-center mb-1">
            {{ job?.company?.name }}
          </h3>
          <p class="mb-6 flex items-center">
            <Icon name="heroicons:map-pin" class="w-4 h-4" />{{
              job?.company?.city
            }}
          </p>
          <nuxt-link
            :to="'/company/' + job?.company?.slug"
            class="btn btn-success hover:btn-primary rounded-full w-60"
          >
            {{ job?.company?.open_jobs_count }} Open Positions
          </nuxt-link>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
type props = {
  jobs: { data: Job[] };
  pending: boolean;
};
const props = defineProps<props>();

const nonConfidentialAndUniqueJobs = computed(() => {
  if (!props.jobs) return null;
  const nonConfidentialJobs: Array<any> = [];
  props.jobs.data.forEach((job: any) => {
    if (job?.company?.name.toLowerCase().includes("confidential")) return;
    nonConfidentialJobs.push(job);
  });

  const uniqueCompanies = new Set();
  const result = nonConfidentialJobs.reduce((acc, job) => {
    const company = job.company.id;
    if (!uniqueCompanies.has(company)) {
      acc?.push(job);
      uniqueCompanies.add(company);
    }

    return acc;
  }, []);
  return result;
});
</script>

<style scoped>
.scrollbar-view::-webkit-scrollbar {
  height: 0.5rem;
}

.scrollbar-view::-webkit-scrollbar-thumb {
  background: #888;
}

.scrollbar-view::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.scrollbar-view::-webkit-scrollbar-thumb:horizontal {
  background: gray;
  border-radius: 10px;
}

.scrollbar-view::-webkit-scrollbar-track-piece:end {
  margin-right: 400px;
}

.scrollbar-view::-webkit-scrollbar-track-piece:start {
  margin-left: 400px;
}
</style>
