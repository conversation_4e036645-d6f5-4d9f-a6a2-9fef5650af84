<template>
  <section
    id="successful-candidates"
    class="md:max-w-7xl md:mx-auto my-36 mx-4"
  >
    <h2 class="text-center font-semibold">Our Successful Candidates</h2>
    <p class="text-center mb-8">
      We love to help our candidates achieve a successful interview.
    </p>
    <div class="grid md:grid-cols-2">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1670721907/website/homepage/successful-candidates_zvcgb6.jpg "
        alt=""
        class="rounded-md place-self-center mb-8"
      >
      <div class="flex-col">
        <div v-for="(item, index) in items" class="">
          <div
            v-if="index == currentSlide"
            class="box-content flex-none snap-start flex-col w-full mb-6 animate__animated animate__fadeIn"
          >
            <h3 class="text-primary mb-6 flex">
              {{ item.title
              }}<span class="ml-auto"
                ><img
                  src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674494533/website/homepage/m0dmiy4ptn4zso5onkl1.png"
                  alt=""
                  class="w-6 h-6"
              ></span>
            </h3>
            <p class="mb-8">
              {{ item.description }}
            </p>
            <p class="font-bold">{{ item.position }}</p>
          </div>
        </div>
        <button
          class="btn btn-success hover:btn-primary mr-2"
          :class="{ 'btn-disabled': currentSlide <= 0 }"
          type="button"
          @click="currentSlide > 0 ? currentSlide-- : null"
        >
          ❮
        </button>

        <button
          class="btn btn-success hover:btn-primary"
          :class="{ 'btn-disabled': currentSlide >= 2 }"
          type="button"
          @click="currentSlide < items.length - 1 ? currentSlide++ : null"
        >
          ❯
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const items = [
  {
    title: "Feel grateful to meet seekers & their team!",
    description:
      "It started when I am feeling lost for my previous job until I meet seekers, they have changed the way how I think on the hiring process and they provide lifetime alternatives for me to stay presentable & competitive during in an interview. All I can say is, I feel grateful to meet seekers & their team & this is how I strive to catch my dream job that makes me happy all day now!",
    position: "Marketing Executive",
  },
  {
    title: "I have got back my desired salary.",
    description:
      "Many thanks to Seekers for helping me go through the 3 interviews and got me my job and got back my desired salary. They are professional at the same time down to earth. I will recommend my friends who are looking job to Seekers in future.",
    position: "Media Consultant Analyst",
  },
  {
    title: "Secured my current position as an SEO Programmer.",
    description:
      "Thanks to Seekers MY that helped me to secure my current position as a SEO Programmer. Especially to Mr. Michael Ng which has assisted and guided me to get prepared for the interview and tests. He gave the best advises and is patience and willing to answer everything that I asked about. Thanks to Mr. Michael again for helping me to get my current job.",
    position: "Wordpress Developer (SEO)",
  },
];

const currentSlide = ref(0);
</script>
