<template>
  <div id="navbar" class="navbar bg-base-100 p-4 xl:px-8 h-20">

    <div class="flex-1 gap-8">
      <!-- LOGO -->
      <nuxt-link to="/">
        <img src="@/static/seekers-logo.png" alt="logo" class="h-8 w-24 object-contain">
      </nuxt-link>

      <!-- Menu Items -->
      <nuxt-link v-for="menu in menuList" :to="menu.link">
        <span class="hidden lg:block navbar-item text-accent text-sm lg:text-xs xl:text-sm pl-3">{{ menu.text
        }}</span>
      </nuxt-link>

      <!-- Dropdown Menu Items -->
      <div class="dropdown dropdown-hover text-accent text-sm lg:text-xs xl:text-sm hidden lg:block">
        <nuxt-link to="/employer" tabindex="0" class="m-1 pl-2 lg:flex lg:items-center lg:gap-1">Employer
          <Icon name="heroicons:chevron-down-solid" />
        </nuxt-link>
        <ul tabindex="0" class="dropdown-content bg-base-100 rounded w-max">
          <li>
            <nuxt-link to="/global-hr" class="block p-3 hover:bg-gray-100">Global HR</nuxt-link>
          </li>

          <li>
            <nuxt-link to="/hire-digital-tech-talent" class="block p-3 hover:bg-gray-100">Hire Digital & Tech
              Talent</nuxt-link>
          </li>

          <li>
            <nuxt-link to="/rpo-services" class="block p-3 hover:bg-gray-100">RPO Services</nuxt-link>
          </li>
        </ul>
      </div>

      <div class="dropdown dropdown-hover text-accent text-sm lg:text-xs xl:text-sm hidden lg:block">
        <nuxt-link to="/hrtrends" tabindex="0" class="m-1 pl-2 lg:flex lg:items-center lg:gap-1">HR Trends
          <Icon name="heroicons:chevron-down-solid" class="lg:w-3 lg:h-3" />
        </nuxt-link>
        <ul tabindex="0" class="dropdown-content bg-base-100 hover:bg-gray-100 rounded w-max">
          <nuxt-link to="/salary-guide" class="block p-3 shadow">Salary Guide</nuxt-link>
        </ul>
      </div>

      <div class="dropdown dropdown-hover text-accent text-sm lg:text-xs xl:text-sm hidden lg:block">
        <div class="m-1 pl-2 lg:flex lg:items-center lg:gap-1">
          Referral Program
          <Icon name="heroicons:chevron-down-solid" />
        </div>
        <ul tabindex="0" class="dropdown-content bg-base-100 rounded w-max">
          <li>
            <nuxt-link to="/refer-a-business" class="block p-3 hover:bg-gray-100">Refer-A-Business</nuxt-link>
          </li>

          <li>
            <nuxt-link to="/refer-a-talent" class="block p-3 hover:bg-gray-100">Refer-A-Talent</nuxt-link>
          </li>
        </ul>
      </div>
    </div>
    <div>
      <!-- Candidate Buttons -->
      <div v-if="isCandidateRoutes" class="flex gap-8 xl:gap-6 2xl:gap-8 ml-auto">
        <nuxt-link v-auto-animate to="/candidate/dashboard/profile" class="ml-auto">
          <button type="button"
            class="hidden lg:block btn btn-success btn-rounded px-8 xl:px-6 py-2 h-full text-sm lg:text-xs xl:text-sm">
            {{ authenticated ? "My Profile" : "Login / Register" }}
          </button>
        </nuxt-link>

        <nuxt-link no-prefetch to="/employer" :class="route.fullPath.includes('employer') ? 'hidden' : 'block'">
          <button type="button"
            class="hidden lg:block btn btn-primary btn-rounded px-12 lg:px-10 py-2 h-full text-sm lg:text-xs xl:text-sm">
            Post Job
          </button>
        </nuxt-link>
      </div>

      <!-- Recruiter Page Buttons -->
      <nuxt-link no-prefetch v-if="isRecruiterRoutes" :to="authenticated ? '/recruiter/dashboard' : '/recruiter/login'">
        <button type="button"
          class="hidden lg:block btn btn-accent btn-rounded px-8 py-2 h-full text-sm lg:text-xs 2xl:text-sm">
          {{ authenticated ? "Dashboard" : "Login / Register" }}
        </button>
      </nuxt-link>

      <!-- Mobile Menu Button -->
      <button class="ml-auto lg:hidden cursor-pointer">
        <Icon v-if="!showMobileMenu" name="heroicons:bars-3-bottom-right"
          class="h-6 w-6 animate__animated animate__fadeIn animate__faster" @click="showMobileMenu = !showMobileMenu" />
        <Icon v-if="showMobileMenu" name="heroicons:x-mark"
          class="h-6 w-6 animate__animated animate__fadeIn animate__faster" @click="showMobileMenu = !showMobileMenu" />
      </button>
    </div>

    <!-- Extended Mobile Menu -->
    <div v-if="showMobileMenu" id="mobile-menu"
      class="bg-transparent flex flex-col w-full h-full absolute top-20 z-30 backdrop-blur lg:hidden"
      @click="showMobileMenu = false">
      <nuxt-link no-prefetch v-for="menuItem in mobileMenuList" id="mobile-menu-item" :to="menuItem.link"
        class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-[0.99]"
        @click="showMobileMenu = false">
        {{ menuItem.text }}
      </nuxt-link>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const showMobileMenu = useState(() => false);
const { authenticated } = useUser();


const isCandidateRoutes = computed(() => {
  return (
    route.path.includes("candidate") ||
    route.path === "/" ||
    route.path === "/jobs"
  );
});

const isRecruiterRoutes = computed(() => {
  return route.path.includes("recruiter");
});

const isContentRoutes = computed(() => {
  return !isCandidateRoutes.value && !isRecruiterRoutes.value;
});

const menuList = [
  {
    text: "Find Jobs  ",
    link: "/jobs",
  },
  {
    text: "Candidates",
    link: "/candidate/dashboard/profile",
  },
  {
    text: "Recruiter",
    link: "/recruiter",
  },
];

const mobileMenuList = [
  {
    text: "Find Jobs",
    link: "/jobs",
  },
  {
    text: "Candidates",
    link: "/candidate/dashboard/profile",
  },
  {
    text: "Recruiter",
    link: "/recruiter",
  },
  {
    text: "Employer",
    link: "/employer",
  },
  {
    text: "Global HR",
    link: "/global-hr",
  },
  {
    text: "Hire Digital & Tech Talent",
    link: "/hire-digital-tech-talent",
  },
  {
    text: "RPO Services",
    link: "/rpo-services",
  },
  {
    text: "HR Trends",
    link: "/hrtrends",
  },
  {
    text: "Salary Guide",
    link: "/salary-guide",
  },
  {
    text: "Refer-A-Business",
    link: "/refer-a-business",
  },
  {
    text: "Refer-A-Talent",
    link: "/refer-a-talent",
  },
];
</script>
