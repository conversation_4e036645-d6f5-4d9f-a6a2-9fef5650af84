<template>
  <div id="navbar">
    <div class="h-20"/>
    <div class="fixed top-0 z-20 w-full backdrop-blur bg-white bg-opacity-60">
      <nav class="flex gap-8 items-center m-auto py-4 px-4 lg:px-12">
        <nuxt-link to="/"
          ><img
            src="@/static/seekers-logo.png"
            alt="logo"
            class="h-8 w-24 object-contain"
        ></nuxt-link>

        <nuxt-link v-for="menu in menuList" :to="menu.link"
          ><span class="hidden md:block navbar-item text-accent text-sm">{{
            menu.text
          }}</span></nuxt-link
        >

        <div
          class="dropdown dropdown-hover text-accent text-sm hidden md:block"
        >
          <nuxt-link to="/employer" tabindex="0" class="m-1 pl-2"
            >Employer <Icon name="heroicons:chevron-down-solid"
          /></nuxt-link>
          <ul
            tabindex="0"
            class="dropdown-content bg-base-100 hover:bg-gray-100 rounded w-full"
          >
            <li>
              <nuxt-link to="/global-hr" class="block p-2 pl-3 shadow"
                >Global HR</nuxt-link
              >
            </li>
          </ul>
        </div>

        <div
          class="dropdown dropdown-hover text-accent text-sm hidden md:block"
        >
          <nuxt-link to="/hrtrends" tabindex="0" class="m-1 pl-2"
            >HR Trends <Icon name="heroicons:chevron-down-solid"
          /></nuxt-link>
          <ul
            tabindex="0"
            class="dropdown-content bg-base-100 hover:bg-gray-100 rounded w-full"
          >
            <li>
              <nuxt-link to="/salary-guide" class="block p-2 pl-3 shadow"
                >Salary Guide</nuxt-link
              >
            </li>
          </ul>
        </div>

        <nuxt-link v-if="!token" to="/recruiter/login" class="ml-auto">
          <button
            type="button"
            class="hidden md:block btn btn-accent btn-rounded px-8 py-2 h-full text-sm"
          >
            Login / Register
          </button>
        </nuxt-link>

        <nuxt-link v-else to="/recruiter/dashboard" class="ml-auto">
          <button
            type="button"
            class="hidden md:block btn btn-accent btn-rounded px-8 py-2 h-full text-sm"
          >
            Dashboard
          </button>
        </nuxt-link>
        <div class="ml-auto md:hidden cursor-pointer">
          <Icon
            v-if="!showMobileMenu"
            name="heroicons:bars-3-bottom-right"
            class="h-6 w-6 animate__animated animate__fadeIn animate__faster"
            @click="showMobileMenu = !showMobileMenu"
            >></Icon
          >
          <Icon
            v-if="showMobileMenu"
            name="heroicons:x-mark"
            class="h-6 w-6 animate__animated animate__fadeIn animate__faster"
            @click="showMobileMenu = !showMobileMenu"
          />
        </div>
      </nav>

      <div
        v-if="showMobileMenu"
        id="mobile-menu"
        class="bg-transparent flex flex-col w-full h-full absolute z-30"
        @click="showMobileMenu = false"
      >
        <nuxt-link
          v-for="menu in menuList"
          id="mobile-menu-item"
          :to="menu.link"
          class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white"
          @click="showMobileMenu = false"
          >{{ menu.text }}</nuxt-link
        >

        <nuxt-link
          to="/employer"
          class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-95"
          >Employer</nuxt-link
        >
        <nuxt-link
          to="/global-hr"
          class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-95"
          >Global HR</nuxt-link
        >

        <nuxt-link
          to="/hrtrends"
          class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-95"
          >HR Trends</nuxt-link
        >

        <nuxt-link
          to="/salary-guide"
          class="p-4 px-12 w-full border-t border-gray-200 text-base-400 text-sm bg-white bg-opacity-95"
          >Salary Guide</nuxt-link
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const showMobileMenu = useState(() => false);
const token = useRecruiter().token;

const menuList = [
  {
    text: "Find Jobs  ",
    link: "/jobs",
  },
  {
    text: "Candidates",
    link: "/candidate/dashboard/profile",
  },
  {
    text: "Recruiter",
    link: "/recruiter",
  },
  // {
  //   text: "Employer",
  //   link: "/employer",
  // },
  // {
  //   text: "Sharing Knowledge",
  //   link: "/sharingknowledge",
  // },
];
</script>
