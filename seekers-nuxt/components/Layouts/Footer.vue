<template>
  <footer aria-label="Site Footer" class="bg-accent text-white">
    <div
      class="max-w-screen-xl px-4 py-16 mx-auto space-y-8 sm:px-6 lg:space-y-16 lg:px-8"
    >
      <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
        <div>
          <img
            src="@/static/seekers-logo.png"
            alt="logo"
            class="max-h-12 object-contain"
          >

          <p class="max-w-xs mt-4 text-base-200 text-sm">
            <PERSON>nsi Pekerjaan Job Search Asia Sdn. Bhd. ( 201401013575 (
            1089659-H ))
          </p>
          <p class="max-w-xs mt-4 text-base-200 text-sm">
            Level 32 Menara Allianz Sentral, 203 Jalan Tun Sambanthan, 50470
            Kuala Lumpur, Malaysia <EMAIL>
          </p>
          <div>
            <a
              href="https://www.trustedmalaysia.com/best-recruitment-agencies-malaysia/"
              rel="noreferrer"
              target="_blank"
              class=""
            >
              <img
                src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1694704775/website/footer/Trusted-min_ygz3eg.png"
                class="w-auto h-14 mt-4"
              >
            </a>
          </div>
        </div>

        <div
          class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:col-span-2 lg:grid-cols-4"
        >
          <div>
            <p class="font-bold text-xl">For Candidates</p>

            <nav aria-label="Footer Navigation - Services" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in candidateMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>

          <div>
            <p class="font-bold text-xl">For Employer</p>

            <nav aria-label="Footer Navigation - Company" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in employerMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                  :no-prefetch="menu.to.includes('srs')"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>

          <div>
            <p class="font-bold text-xl">Recruiter</p>

            <nav aria-label="Footer Navigation - Company" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in recruiterMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>

          <div>
            <p class="font-bold text-xl">About Us</p>

            <nav aria-label="Footer Navigation - Legal" class="mt-8">
              <div class="space-y-4 text-xs">
                <nuxt-link
                  v-for="menu in otherMenus"
                  :to="menu.to"
                  class="block text-neutral transition hover:opacity-75"
                >
                  {{ menu.name }}
                </nuxt-link>
              </div>
            </nav>
          </div>
        </div>
      </div>

      <div class="pt-8 mt-8 border-t border-gray-100">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <p class="text-xs text-left">
            Agensi Pekerjaan Job Search Asia Sdn. Bhd. ( 201401013575 (
            1089659-H ))<br >© {{ new Date().getFullYear() }}. All rights
            reserved.
          </p>

          <nav aria-label="Footer Navigation - Social Media">
            <div class="flex gap-6 justify-end">
              <a
                v-for="link in socialLinks"
                :href="link.href"
                rel="noreferrer"
                target="_blank"
                class="text-neutral transition hover:opacity-75"
              >
                <span class="sr-only">{{ link.name }}</span>

                <Icon :name="link.icon" class="w-6 h-6" />
              </a>
            </div>
          </nav>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
const candidateMenus = [
  {
    name: "Browse Jobs",
    to: "/jobs",
  },
  {
    name: "Browse Categories",
    to: "/#popular-categories",
  },
  {
    name: "Candidate Dashboard",
    to: "/candidate/dashboard",
  },
  {
    name: "Find Companies",
    to: "/company",
  },
  { name: "Salary Guide", to: "/salary-guide" },
];

const employerMenus = [
  {
    name: "SRS Login",
    to: "https://srs.seekers.my/login",
  },
  {
    name: "SRS Register",
    to: "https://srs.seekers.my/registration",
  },
  // {
  //   name: "Whitepaper",
  //   to: "/employer/whitepaper",
  // },
  {
    name: "Employer Homepage",
    to: "/employer",
  },
  {
    name: "Employer FAQ",
    to: "/employer/faq",
  },
  { name: "Global HR", to: "/global-hr" },
  { name: "HR Trends", to: "/hrtrends" },
  { name: "Hire Digital & Tech Talent", to: "/hire-digital-tech-talent" },
  { name: "Refer-A-Business", to: "/refer-a-business" },
  { name: "Refer-A-Talent", to: "/refer-a-talent" },
  { name: "RPO Services", to: "/rpo-services" },
];

const recruiterMenus = [
  {
    name: "Job List For Recruiter",
    to: "/recruiter/dashboard/jobs",
  },
  {
    name: "Dashboard",
    to: "/recruiter/dashboard",
  },
  {
    name: "Recommended Jobs",
    to: "/recruiter/dashboard/jobs",
  },
  {
    name: "Latest Jobs",
    to: "/recruiter/dashboard/jobs",
  },
  {
    name: "Recruiter Registration",
    to: "/recruiter/register",
  },
  {
    name: "Recruiter Login",
    to: "/recruiter/login",
  },
  {
    name: "Recruiter FAQ",
    to: "/recruiter/faq",
  },
];

const otherMenus = [
  {
    name: "About Seekers.my",
    to: "/about",
  },
  {
    name: "Privacy Policy",
    to: "/privacy",
  },
  {
    name: "Terms of Use",
    to: "/terms",
  },
  {
    name: "Contact Us",
    to: "/contact",
  },
];

const socialLinks = [
  {
    name: "Facebook",
    href: "https://www.facebook.com/seekers.malaysia",
    icon: "bxl:facebook-circle",
  },
  {
    name: "Instagram",
    href: "https://www.instagram.com/seekersmy/",
    icon: "bxl:instagram",
  },
  {
    name: "Twitter",
    href: "https://twitter.com/seekersmy?lang=en",
    icon: "bxl:twitter",
  },
];
</script>
