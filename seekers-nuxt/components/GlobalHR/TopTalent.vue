<template>
  <div class="text-center md:py-44 lg:p-44 my-24 md:my-12 mx-4">
    <h2 class="font-bold md:text-5xl">
      {{ t("h2Title") }}
    </h2>
    <p class="text-gray-500 mt-4 mb-8 text-lg md:px-24">
      {{ t("paragraph") }}
    </p>
    <a
      href="#global-hr-reach-out"
      class="btn text-white !font-bold px-12"
      :class="locale == 'en' ? 'bg-[#BB2033]' : 'bg-[#1967D2]'"
      >{{ t("button") }}</a
    >
  </div>
</template>

<script setup>
const { t, locale } = useI18n({
  useScope: "local",
});
</script>

<i18n lang="json">
{
  "en": {
    "h2Title": "Hiring Top Talent From Any Location Globally",
    "paragraph": "We offer our assistance in sourcing top-tier talent from multiple countries in Southeast Asia and South Asia, including Malaysia, Singapore, Vietnam, India, Indonesia, Philippines, and Pakistan.",
    "button": "Find A Global Talent"
  },
  "ja": {
    "h2Title": "世界中の優秀な人材の獲得を今すぐに開始する",
    "paragraph": "アジアを中心にマレーシア、シンガポール、ベトナム、インド、インドネシア、フィリピンなどにおける人材の採用を支援します。",
    "button": "採用の相談をする"
  }
}
</i18n>
