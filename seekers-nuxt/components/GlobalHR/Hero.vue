<template>
  <div :class="locale == 'en' ? 'bg-[#BB2033]' : 'bg-[#1967D2]'">
    <div class="text-right pr-4 pt-4">
      <label class="ml-auto cursor-pointer label w-min gap-2">
        <span class="label-text"
          ><Icon name="circle-flags:jp" class="text-xl"
        /></span>
        <input
          type="checkbox"
          class="toggle toggle-sm"
          :checked="locale === 'en'"
          @change="toggleLanguage"
        >
        <span class="label-text"
          ><Icon name="circle-flags:en" class="text-xl"
        /></span>
      </label>
    </div>
    <div
      class="grid grid-cols-1 md:grid-cols-2 p-12 md:pb-0 md:px-20 xl:pt-0 justify-items-center items-center xl:h-[30rem] 2xl:h-[34rem]"
    >
      <div id="hero-text" class="animate__animated animate__fadeIn lg:pl-24">
        <i18n-t
          v-if="locale == 'en'"
          keypath="h1Title"
          tag="h1"
          class="font-bold text-5xl md:text-4xl xl:text-5xl text-white"
        >
          <template #action>
            <span class="text-[#FFD43B]">Hire & Manage</span>
          </template>
        </i18n-t>
        <h1
          v-if="locale == 'ja'"
          class="font-bold text-[3.6rem] leading-none md:text-6xl text-white"
        >
          海外で人材雇用するための現地法人設立はもう不要です
        </h1>
        <p class="font-semibold mt-8 text-white/70">
          {{ t("paragraph") }}
        </p>
        <div class="mt-8">
          <a href="#global-hr-reach-out" class="btn mr-3 !font-semibold">{{
            t("button")
          }}</a>
        </div>
      </div>
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1684089950/website/global-hr%20page/globalhr-hero-new-min_mitax4.png"
        alt=""
      >
    </div>
    <!-- <custom-debug-i18n /> -->
    <img
      src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1684090966/website/global-hr%20page/Slice_4_we0yln.svg"
      alt=""
      class="w-screen"
    >
  </div>
</template>

<script setup lang="ts">
const { t, locale, setLocale } = useI18n({
  useScope: "local",
});

const toggleLanguage = () => {
  setLocale(locale.value == "en" ? "ja" : "en");
};
</script>

<style scoped>
input:checked {
  background-color: bg-red-500;
}

input:checked ~ span:last-child {
  --tw-translate-x: 3rem;
}
</style>

<i18n lang="json">
{
  "en": {
    "h1Title": "Let Seekers {action} your worldwide team",
    "paragraph": "Manage the employment and compensation of your remote employees, including tax and legal compliance, payroll processing, and other HR-related tasks.",
    "button": "Get Started"
  },
  "ja": {
    "h1Title": "海外で人材雇用するための現地法人設立はもう不要です",
    "paragraph": "海外での人材採用・雇用手続き・給与支払いを全てお任せ",
    "button": "無料相談する"
  }
}
</i18n>
