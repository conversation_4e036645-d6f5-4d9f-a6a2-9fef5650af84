<template>
  <div id="faq-section" class="relative my-20">
    <div class="xl:pt-24 xl:pr-24 pb-[60vw] xl:pb-0 bg-gray-200 rounded-xl">
      <div
        class="bg-none bg-no-repeat bg-contain h-screen"
        :class="
          locale == 'en'
            ? 'xl:bg-[url(https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1686395033/website/global-hr%20page/Device_-_Macbook_Air_En_cw1xqq.png)]'
            : 'xl:bg-[url(https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1686396534/website/global-hr%20page/Device_-_Macbook_Air_Ja_q37dza.png)]'
        "
      >
        <!-- FAQ_EN -->
        <div
          v-if="locale == 'en'"
          class="p-8 xl:px-[14vw] xl:pb-[8vw] 2xl:pb-[10vw] xl:pt-[4vw] 2xl:pt-[8vw] xl:pr-[16vw] 2xl:pr-[18vw]"
        >
          <h2
            class="text-5xl xl:text-4xl 2xl:text-5xl text-white font-semibold mb-8 2xl:mb-16"
          >
            FREQUENTLY ASKED QUESTIONS
          </h2>
          <div class="grid xl:grid-cols-2">
            <div class="flex xl:block flex-col gap-4">
              <div
                v-for="(faq, index) in faqsEn"
                class="flex flex-col gap-4 p-4 cursor-pointer hover:bg-gray-100 shadow-xl xl:translate-x-8 xl:translate-y-4"
                :class="{
                  'bg-slate-100': activeFaq === index,
                  'bg-white': activeFaq !== index,
                }"
                @click="activeFaq = index"
              >
                <div class="flex items-center gap-4">
                  <div class="h-6 w-6 rounded-full">
                    <Icon
                      name="ic:baseline-circle"
                      class="h-6 w-6"
                      :class="
                        activeFaq == index ? 'text-[#F9AB00]' : 'text-[#FFD43B]'
                      "
                    />
                  </div>
                  <p class="font-semibold text-sm">
                    {{ faq.question }}
                  </p>
                  <Icon
                    name="heroicons:chevron-right"
                    class="ml-auto"
                    :class="
                      activeFaq == index ? 'text-[#F9AB00]' : 'text-[#FFD43B]'
                    "
                  />
                </div>
                <!-- FAQ ANSWERS MOBILE -->
                <div
                  v-if="activeFaq === index"
                  class="w-full border-t pt-8 pb-4 xl:hidden animate__animated animate__fadeIn"
                >
                  {{ faq.answer }}
                </div>
              </div>
            </div>

            <!-- FAQ ANSWERS DESKTOP -->
            <div
              id="faq-aswers"
              class="hidden xl:block p-10 pl-20 xl:h-[28rem] 2xl:h-[26rem] bg-white rounded-xl"
            >
              {{ activeFaq ? faqsEn[activeFaq].answer : faqsEn[0].answer }}
            </div>
          </div>
        </div>

        <!-- FAQ_JA -->
        <div
          v-if="locale == 'ja'"
          class="p-8 xl:px-[14vw] xl:pb-[10vw] xl:pt-[4vw] 2xl:pt-[8vw] xl:pr-[18vw]"
        >
          <h2 class="text-5xl text-white font-semibold mb-8 xl:ml-6 2xl:mb-16">
            よくあるご質問
          </h2>
          <div class="grid xl:grid-cols-2">
            <div class="flex xl:block flex-col gap-4">
              <div
                v-for="(faq, index) in faqsJa"
                class="flex flex-col gap-4 p-4 cursor-pointer hover:bg-gray-100 shadow-xl xl:translate-x-8 xl:translate-y-4"
                :class="{
                  'bg-slate-100': activeFaq === index,
                  'bg-white': activeFaq !== index,
                }"
                @click="activeFaq = index"
              >
                <div class="flex items-center gap-4">
                  <div class="h-6 w-6 rounded-full">
                    <Icon
                      name="ic:baseline-circle"
                      class="h-6 w-6"
                      :class="
                        activeFaq == index
                          ? 'text-[#1967D2]'
                          : 'text-[#1967D2] opacity-40'
                      "
                    />
                  </div>
                  <p class="font-semibold text-sm">
                    {{ faq.question }}
                  </p>
                  <Icon
                    name="heroicons:chevron-right"
                    class="ml-auto"
                    :class="
                      activeFaq == index
                        ? 'text-[#1967D2]'
                        : 'text-[#1967D2] opacity-40'
                    "
                  />
                </div>
                <!-- FAQ ANSWERS MOBILE -->
                <div
                  v-if="activeFaq === index"
                  class="w-full border-t pt-8 pb-4 xl:hidden animate__animated animate__fadeIn"
                >
                  {{ faq.answer }}
                </div>
              </div>
            </div>

            <!-- FAQ ANSWERS DESKTOP -->
            <div
              id="faq-aswers"
              class="hidden xl:block p-10 pl-20 h-[25rem] bg-white rounded-xl"
            >
              {{ activeFaq ? faqsJa[activeFaq].answer : faqsJa[0].answer }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { locale } = useI18n();
const activeFaq = ref<number | null>(0);

const faqsEn = [
  {
    question: "What will be the nature of the employment arrangement?",
    answer:
      "We can handle various types of employment arrangements, such as full-time employees, contract employees, and freelancers. However, there may be cases where we cannot accommodate depending on the recruitment requirements, so please contact us for further details.",
  },
  {
    question: "What types of employee can we request for recruitment?",
    answer:
      "You can request recruitment mainly for positions such as software engineers, digital marketing, sales, and customer support. Please contact us first to learn more.",
  },
  {
    question:
      "What are the cases where companies request global talent recruitment?",
    answer:
      "Some cases where companies request global talent recruitment include cost reduction (moving some business processes overseas), expanding into foreign markets, conducting market research, and customer support.",
  },
  {
    question: "What countries can we request recruitment from?",
    answer:
      "We mainly handle various countries in Asia, but please contact us for more details.",
  },
  {
    question: "Can you explain the pricing structure?",
    answer:
      "We customize our services based on the preferences and requirements of our clients. Please contact us for more details.",
  },
  {
    question: "What size or type of companies can use this service?",
    answer:
      "We can accommodate recruitment needs starting from hiring one person, regardless of the customer's industry or business size.",
  },
];

const faqsJa = [
  {
    question: "雇用形態はどのような内容になりますか？",
    answer:
      "正社員、契約社員、フリーランスなど複数の雇用形態に対応可能です。募集内容により対応できないケースもありますため、詳細はお問い合わせ下さい。",
  },
  {
    question: "どのような人材の採用を依頼可能ですか？",
    answer:
      "エンジニア、マーケティング、営業、カスタマーサポートを中心に依頼可能です。まずはお問い合わせください。",
  },
  {
    question: "どのようなニーズで海外人材採用を依頼するケースがありますか？",
    answer:
      "コスト削減（業務の一部を海外へ移転）、他国での市場開拓、市場調査、カスタマーサポートなどがあります。",
  },
  {
    question: "どの国での採用を依頼可能ですか？",
    answer: "主にアジア各国対応していますが、詳細はお問い合わせ下さい。",
  },
  {
    question: "料金体系についてはどのような内容になりますか？",
    answer:
      "お客様の希望や要望によりカスタマイズしております。詳細はお問い合わせ下さい。",
  },
  {
    question: "どのような規模や企業が利用可能ですか？",
    answer: "お客様の業種や事業規模に関わらず1名の採用から対応しております。",
  },
];
</script>
