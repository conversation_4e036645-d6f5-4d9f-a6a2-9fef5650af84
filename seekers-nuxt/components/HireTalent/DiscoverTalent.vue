<template>
  <div
    id="discover-digital-tech-positions"
    class="grid md:grid-cols-[2fr_3fr] gap-8 md:max-w-7xl mx-8 md:mx-auto my-48"
  >
    <div class="text-center md:text-start md:py-12">
      <h2 class="text-4xl font-semibold">
        Discover <span class="text-[#E71D36]">Digital & Tech</span> Talent
        Positions here
      </h2>
      <p class="my-6">
        Seekers offers a pool of talented individuals specializing in Digital
        and Tech fields.
      </p>
      <div class="flex justify-center md:justify-start gap-12">
        <div>
          <p class="text-[#FF1D03] text-3xl">70k</p>
          <p>Total Candidates</p>
        </div>
        <div>
          <p class="text-[#2EC4B6] text-3xl">5.5k</p>
          <p>Total Clients</p>
        </div>
      </div>
      <nuxt-link
        to="#hire-talent-free-consultation"
        class="btn bg-[#E71D36] text-white mt-6"
        >Book Free Consultation</nuxt-link
      >
    </div>
    <div class="grid md:grid-cols-2 lg:grid-cols-3 justify-center gap-8">
      <div
        v-for="position in positions"
        class="p-[1px] rounded-2xl shadow-[0_4px_13px_0_rgba(0,0,0,0.06)] h-72 w-64 md:w-fit"
        :class="position.borderClass"
      >
        <div class="p-4 rounded-2xl h-full" :class="position.bgClass">
          <img
            :src="position.img"
            :alt="position.img + 'image'"
            class="mx-auto"
          >
          <div class="">
            <h3 class="leading-6 font-semibold mt-2 mb-4">
              {{ position.title }}
            </h3>
            <p class="text-sm">{{ position.list }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const positions = [
  {
    borderClass: "bg-gradient-to-b from-[#E71D36]/60 to-white",
    bgClass: "bg-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688022175/website/hire-digital-tech-talent/software-developer_sj0mdt.png",
    title: "Software Developer",
    list: "PHP, Laravel, .NET, JavaScript, iOS, Android, Python, Java, and more..",
  },
  {
    borderClass: "bg-gradient-to-b from-[#2EC4B6]/60 to-white",
    bgClass: "bg-[#F6FFFE]",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688022175/website/hire-digital-tech-talent/digital_mvkhct.png",
    title: "Digital",
    list: "Digital Marketing, SEO Specialist, SEM Specialist, and more..",
  },
  {
    borderClass: "bg-gradient-to-b from-[#E71D36]/60 to-white",
    bgClass: "bg-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688022175/website/hire-digital-tech-talent/data-related-jobs_xuzmvq.png",
    title: "Data-related Jobs",
    list: "Data Scientist, Data Analyst, Data Engineer, and more..",
  },
  {
    borderClass: "bg-gradient-to-b from-[#2EC4B6]/60 to-white",
    bgClass: "bg-[#F6FFFE]",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688022175/website/hire-digital-tech-talent/technical_wpqxda.png",
    title: "Technical",
    list: "IT Project Manager, Network Engineer, DevOps Engineer, and more..",
  },
  {
    borderClass: "bg-gradient-to-b from-[#E71D36]/60 to-white",
    bgClass: "bg-white",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688022175/website/hire-digital-tech-talent/creative_a0cums.png",
    title: "Creative",
    list: "Graphic Designer, UI/UX Designer, Video Editor, and more..",
  },
  {
    borderClass: "bg-gradient-to-b from-[#2EC4B6]/60 to-white",
    bgClass: "bg-[#F6FFFE]",
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688022175/website/hire-digital-tech-talent/business_ecysl6.png",
    title: "Business",
    list: "Business Development, Product Manager, Business Analyst, and more..",
  },
];
</script>
