<template>
  <div
    id="inquire-today-hire-tomorrow"
    class="bg-[#E71D36] text-white pt-12 pb-24"
  >
    <div class="md:max-w-7xl md:mx-auto text-center mx-4 relative">
      <h2 class="mb-2 text-4xl">
        Inquire <span class="font-bold">Today</span>, Hire
        <span class="font-bold">Tomorrow</span>
      </h2>
      <p>
        Find Out What Happens After You Inquire with Seekers. Learn More Here
      </p>
      <div class="grid md:grid-cols-5 gap-8 mt-12">
        <div v-for="inquiry in inquiries">
          <img
            :src="inquiry.img"
            :alt="inquiry.img + 'image'"
            class="mx-auto w-24 h-24"
          >
          <div>
            <p class="font-semibold text-lg mt-8 mb-2">{{ inquiry.step }}</p>
            <p class="text-sm">{{ inquiry.action }}</p>
          </div>
        </div>
      </div>
      <div
        class="hidden lg:flex md:max-w-7xl mx-auto justify-center items-center lg:gap-32 xl:gap-28 absolute lg:right-[15%] lg:w-[70%] xl:w-max lg:top-[38%] xl:right-[13%]"
      >
        <div v-for="arrow in arrows">
          <img :src="arrow.arrow" alt="" class="" >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const inquiries = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/book-consultation_zd5yc1.png",
    step: "Book FREE Consultation",
    action:
      "Schedule a complimentary consultation with our team to discuss your hiring needs and goals.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/share-requirement_vxh8uq.png",
    step: "Share Your Requirements",
    action:
      "Share your detailed job requirements, including the skills, experience, and qualifications you are seeking in a candidate.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/match-candidate_g7rbpe.png",
    step: "Matching Candidate Profiles",
    action:
      "Our recruiters will screen, interview candidates, and matching the best fit for your organization.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/finalize-offer_qlceep.png",
    step: "Finalize Job Offer",
    action:
      "Once you have identified the candidates you are interested in, we will assist you in finalizing the job offer.",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688049977/website/hire-digital-tech-talent/new-hire_jwd6gv.png",
    step: "Onboard New Hire",
    action:
      "After the candidate accepts the job offer, we will support you in the onboarding process.",
  },
];

const arrows = [
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-up_jnw0oa.png",
  },
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-down_krznmz.png",
  },
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-up_jnw0oa.png",
  },
  {
    arrow:
      "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1688366486/website/hire-digital-tech-talent/Ellipse-down_krznmz.png",
  },
];
</script>
