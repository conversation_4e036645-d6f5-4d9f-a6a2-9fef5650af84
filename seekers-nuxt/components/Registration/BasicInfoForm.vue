<template>
  <div>
    <FormKit
      ref="basicInfoForm"
      type="form"
      id="basicInfoForm"
      v-model="formData"
      :actions="false"
      :form-class="'hide'"
      submit-label="Next"
      @submit="$emit('submit')"
      :incomplete-message="false"
      :submit-attrs="{
        inputClass: 'btn btn-primary ',
        outerClass: 'max-w-xl mx-auto text-center',
      }"
    >
      <div id="form-inputs" class="grid lg:grid-cols-2 gap-2 gap-x-8">
        <FormKit
          type="text"
          name="name"
          label="Full name (as in IC/Passport)"
          validation="required|length:5"
          placeholder="<PERSON>"
        />
        <FormKit
          type="email"
          name="email"
          :disabled="initialData.email != ''"
          label="Email address"
          validation="required|email"
          placeholder="<EMAIL>"
        />
        <FormKit type="group" name="password_group">
          <FormKit
            v-if="initialData.email == ''"
            type="password"
            name="password"
            value=""
            label="Password"
            help="Enter a new password"
            validation="required|length:8"
            validation-visibility="dirty"
          />
          <FormKit
            v-if="initialData.email == ''"
            type="password"
            name="password_confirm"
            label="Confirm password"
            help="Confirm your new password"
            validation="required|confirm"
            validation-visibility="dirty"
            validation-label="Password confirmation"
          />
        </FormKit>
        <FormKit
          type="tel"
          name="mobile"
          label="Phone number"
          placeholder="+6011-44448888"
          validation="required|length:11,13"
          :validation-messages="{
            matches: 'Phone number must be in the format +xxxxxxxxxx',
          }"
          validation-visibility="dirty"
        />

        <FormKit
          type="date"
          label="Birth Date"
          name="birth_date"
          validation="required"
          min="1950-01-01"
        />

        <FormKit
          type="select"
          name="is_local"
          label="Nationality"
          validation="required"
          placeholder="Malaysian"
          :value="true"
          :classes="{ selectIcon: 'px-2' }"
        >
          <option :value="true">Malaysian</option>
          <option :value="false">Expatriate</option>
        </FormKit>

        <FormKit
          type="select"
          name="state"
          label="State"
          validation="required|length:4"
          placeholder="Pick a state"
          :value="'Kuala Lumpur'"
          :options="MALAYSIAN_STATES"
        />

        <FormKit
          type="select"
          label="Gender"
          name="gender"
          :options="[
            {
              value: 'female',
              label: 'Female',
            },
            {
              value: 'male',
              label: 'Male',
            },
          ]"
        />
      </div>
      <p class="text-xs text-center mt-8">
        By pressing next, you agree on Seekers's Privacy Statement, Terms &
        Conditions
      </p>
    </FormKit>
    <button
      class="btn btn-primary w-full"
      @click="(basicInfoForm as any).node.submit()"
    >
      Next
    </button>
  </div>
</template>

<script lang="ts" setup>
const emit = defineEmits(["submit"]);

const props = defineProps<{
  initialData: BasicInfoForm;
}>();
const basicInfoForm = ref(null);
const formData = ref<BasicInfoForm>(props.initialData);
</script>
