<template>
  <div>
    <!-- Refer A Talent Mobile -->
    <div
      class="bg-[url(https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1692129472/website/candidate/refer-a-talent-banner-rs_pvmbmh.jpg)] lg:hidden">
      <div class="p-8 md:p-24">
        <div class="grid gap-4 bg-white p-8 xl:w-1/2 rounded-xl">
          <p class="text-4xl font-semibold">Refer-A-Talent</p>
          <p class="mt-4 mb-2">
            Know someone perfect for this role? Refer them to Seekers and
            <span class="font-semibold">earn RM500</span>! Join our referral
            program now and help us find top talent!
          </p>
          <nuxt-link :to="`/refer-a-talent?job=${jobSlug}#refer-talent-form`"
            class="btn btn-primary w-fit">Refer-A-Talent Now!</nuxt-link>
        </div>
      </div>
    </div>

    <!-- Refer A Talent Desktop -->
    <div class="relative hidden lg:block p-8 lg:p-24 xl:p-40" style="background-image: url('https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1692129472/website/candidate/refer-a-talent-banner-rs_pvmbmh.jpg'); 
      background-size: cover; 
      background-position: center;">
      <div class="bg-white rounded-xl p-8 w-1/2">
        <p class="lg:text-3xl xl:text-4xl font-semibold">Refer-A-Talent</p>
        <p class="mt-4 mb-2 lg:text-sm xl:text-base">
          Know someone perfect for this role? Refer them to Seekers and
          <span class="font-semibold">earn RM500!</span>
          <br />
          Join our referral program now and help us find top talent!
        </p>
        <nuxt-link :to="`/refer-a-talent?job=${jobSlug}#refer-talent-form`" class="btn btn-primary w-fit">Refer-A-Talent
          Now!</nuxt-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const { jobSlug } = defineProps<{ jobSlug: string }>();
</script>

<style></style>