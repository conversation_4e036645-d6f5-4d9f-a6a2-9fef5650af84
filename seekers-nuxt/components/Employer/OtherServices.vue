<template>
  <section id="other-seekers-services" class="max-w-7xl md:mx-auto mx-4 py-24">
    <h2 class="text-[#CE643A] text-3xl font-semibold mb-16">
      Other Seekers Services
    </h2>
    <div class="grid md:grid-cols-3 md:grid-rows-2 gap-6">
      <div
        v-for="service in services"
        class="p-[0.5px] rounded-2xl shadow-[0_4px_13px_0_rgba(0,0,0,0.06)] group"
        :class="service.class"
      >
        <div :class="service.bgClass" class="h-full">
          <nuxt-link
            :to="service.link"
            target="blank"
            class="grid bg-white/80 p-4 pb-12 rounded-2xl h-full group-hover:bg-[#CE643A] group-hover:text-white"
            :class="service.bgClass"
          >
            <Icon
              name="fluent:arrow-right-12-filled"
              class="p-2 rounded-full w-12 h-12 group-hover:bg-white ml-auto"
              :class="service.bgSolid"
            />
            <p class="text-sm" :class="service.textColor">
              {{ service.title }}
            </p>
            <p class="text-lg font-semibold" :class="service.textColor">
              {{ service.text }}
            </p>
          </nuxt-link>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const services = [
  {
    link: "/global-hr",
    title: "Global HR Service",
    text: "Hire remote employee in other countries without legal entity",
    class: "bg-gradient-to-b from-[#CE643A] to-white",
    bgClass: "bg-white rounded-2xl",
    bgSolid: "text-[#CE643A]",
  },
  {
    link: "/hire-digital-tech-talent",
    title: "Hire Digital & Tech Talent - Your Global Digital Recruitment",
    text: "We connect employers with top Digital & Tech talent globally",
    class:
      "md:col-start-2 md:col-end-4 bg-gradient-to-b from-[#CE643A] to-white",
    bgSolid: "text-[#CE643A]",
  },
  {
    link: "/salary-guide",
    title: "Malaysia Salary Guide",
    text: "Find latest high-paying Digital/Tech jobs now!",
    class: "bg-gradient-to-b from-[#CE643A] to-white",
    bgSolid: "text-[#CE643A]",
  },
  {
    link: "/rpo-services",
    title: "Seekers RPO Technology",
    text: "Digital & Tech Recruitment Outsourcing in Malaysia",
    bgClass: "!bg-[#CE643A] rounded-2xl",
    bgSolid: "text-white group-hover:text-[#CE643A]",
    textColor: "text-white",
  },
  {
    link: "/refer-a-business",
    title: "Seekers Recruitment Referral Program",
    text: "Earn up to RM5,000 | Introduce qualified hiring manager to Us",
    class: "bg-gradient-to-b from-[#CE643A] to-white",
    bgClass: "bg-white rounded-2xl",
    bgSolid: "text-[#CE643A]",
  },
];
</script>
