<template>
  <section
    id="comparison-table"
    class="max-w-7xl md:mx-auto mx-4 grid lg:grid-cols-11 py-24"
  >
    <div id="table-1" class="lg:col-start-1 lg:col-end-6 md:mr-20">
      <div class="grid grid-cols-2">
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1694580935/website/employerpage/Snail_Toy.H15.2k_weoy38.png"
          class="col-start-2 bg-[#9CA7B2] px-4 pt-10 w-full md:h-40 object-contain"
        >
        <h2
          class="col-start-2 bg-[#9CA7B2] px-8 text-xl text-white font-bold py-4"
        >
          Direct<br >Hiring
        </h2>
      </div>
      <div v-for="compare in directHiring" class="grid grid-cols-2 text-[13px]">
        <p
          class="font-semibold md:ml-20 pl-4 bg-[#ECEDF2] rounded-l-md p-1 mt-1"
          :class="compare.pointClass"
        >
          {{ compare.point }}
        </p>
        <div
          class="bg-[#9CA7B2] px-4 pt-1 flex items-center gap-1"
          :class="compare.class"
        >
          <Icon name="mdi:check-bold" class="w-3 h-3 text-white" />
          <p class="w-fit text-white">
            {{ compare.text }}
          </p>
        </div>
      </div>
    </div>

    <p
      class="flex justify-center items-center text-4xl font-semibold lg:col-start-6 py-12 lg:py-0"
    >
      vs
    </p>

    <div id="table-2" class="lg:col-start-7 lg:col-end-12 md:mr-20">
      <div class="grid grid-cols-2">
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1694582390/website/employerpage/Toy_Cannon.G15.2k_eub5uw.png"
          class="col-start-2 bg-[#CE643A] px-4 pt-10 w-full md:h-40 object-contain"
        >
        <h2
          class="col-start-2 bg-[#CE643A] px-8 text-xl text-white font-bold py-4"
        >
          Seekers<br >Headhunting
        </h2>
      </div>
      <div
        v-for="compare in seekersHiring"
        class="grid grid-cols-2 text-[13px]"
      >
        <p
          class="font-semibold md:ml-20 pl-4 bg-[#CE643A]/30 rounded-l-md p-1 mt-1"
          :class="compare.pointClass"
        >
          {{ compare.point }}
        </p>

        <div class="bg-[#CE643A] px-4 pt-1">
          <div class="flex items-center gap-1" :class="compare.class">
            <Icon
              name="mdi:check-bold"
              class="w-3 h-3 text-white"
              :class="compare.iconClass"
            />
            <p class="w-fit text-white">
              {{ compare.text }}
            </p>
          </div>
          <div class="">
            <nuxt-link :to="compare.link" :class="compare.btnClass">
              {{ compare.btn }}
            </nuxt-link>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const directHiring = [
  {
    point: "Cost",
    text: "Spend more on paid advertisement",
  },
  {
    point: "Database",
    text: "Limited",
  },
  {
    point: "Hunt Process",
    text: "Tons of irrelevant profiles",
  },
  {
    point: "Guarantee",
    text: "No Guarantee",
  },
  {
    point: "Job Seekers",
    text: "Only aim for active job seekers",
    class: "pb-32",
    pointClass: "h-min",
  },
];

const seekersHiring = [
  {
    point: "Cost",
    text: "Pay once hired",
  },
  {
    point: "Database",
    text: "Broad",
  },
  {
    point: "Hunt Process",
    text: "Qualified candidates only",
  },
  {
    point: "Guarantee",
    text: "3 months guarantee",
  },
  {
    point: "Job Seekers",
    text: "Constantly engage with active and passive job seekers and offer the best package for them",
    class: "!items-start ",
    iconClass: "mt-1",
    pointClass: "h-min",
    btn: "Contact Now",
    btnClass:
      "flex justify-center py-2 border mx-6 mt-4 mb-6 text-white font-semibold active:bg-[#BA5026]",
    link: "#employer-get-in-touch",
  },
];
</script>
