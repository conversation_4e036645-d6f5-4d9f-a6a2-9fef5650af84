<template>
  <section
    id="we-assist-you"
    class="bg-[url('https://res.cloudinary.com/dwgfv3js3/image/upload/v1694496474/website/employerpage/we-assist-you-bg_v8jypb.png')] md:p-24"
  >
    <div class="border bg-[#F7F7F7] rounded-xl shadow-xl p-8 lg:relative">
      <div
        class="grid lg:grid-cols-[2fr_3fr] border-2 border-[#CE643A] rounded-xl p-8"
      >
        <img
          src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1694500258/website/employerpage/assist-img-2_r4h7yu.png"
          class="lg:absolute lg:top-0 lg:left-2 lg:h-1/2 xl:h-3/4 2xl:h-[85%]"
        >
        <div class="lg:col-start-2">
          <div class="grid gap-8">
            <div>
              <h2
                class="font-semibold text-xl 2xl:text-2xl text-[#CE643A] mb-4"
              >
                We assist you with the entire recruitment, selection and hiring
                process.
              </h2>
              <p class="font-semibold text-xl 2xl:text-2xl text-[#CE643A] mb-4">
                Only pay when we successfully place a candidate for you!
              </p>
              <ul>
                <li class="flex text-gray-500">
                  <img
                    src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1674495590/website/employerpage/tick_zz2cjs.svg"
                    alt=""
                    class="pr-2"
                  >
                  Covers a variety of industries and roles.
                </li>
                <li class="flex text-gray-500">
                  <img
                    src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1674495590/website/employerpage/tick_zz2cjs.svg"
                    alt=""
                    class="pr-2"
                  >Particularly strong in hiring management and experts.
                </li>
                <li class="flex text-gray-500">
                  <img
                    src="https://res.cloudinary.com/dwgfv3js3/image/upload/v1674495590/website/employerpage/tick_zz2cjs.svg"
                    alt=""
                    class="pr-2"
                  >Succeeded in Malaysia, Singapore and Japan.
                </li>
              </ul>
            </div>

            <div class="grid justify-center md:justify-end">
              <div class="border rounded-md bg-white h-fit w-max">
                <p
                  class="text-center text-white rounded-t-md font-semibold text-sm py-2 bg-[#CE643A]"
                >
                  Applicants List
                </p>
                <div class="grid gap-2 p-2">
                  <div
                    v-for="applicant in applicants"
                    class="flex items-center gap-2"
                  >
                    <img
                      :src="applicant.img"
                      class="row-span-4 place-self-center rounded-full w-10 h-10"
                    >
                    <div class="flex items-center gap-2">
                      <div>
                        <p class="font-semibold text-sm">
                          {{ applicant.name }}
                        </p>
                        <p class="text-xs">
                          {{ applicant.position }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const applicants = [
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1678254215/website/global-hr%20page/efzal-rifqi_m1knwm.jpg",
    name: "Efzal Rifqi",
    position: "Web Developer",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1694502355/website/employerpage/michael_vczh1b.png",
    name: "Michael Ng",
    position: "Digital Marketing Manager",
  },
  {
    img: "https://res.cloudinary.com/dwgfv3js3/image/upload/v1694502355/website/employerpage/feyra_qi2zgn.png",
    name: "Feyra Rayfa",
    position: "Sales Marketing Executive",
  },
];
</script>
