<template>
  <div
    v-if="show"
    class="fixed top-0 left-0 w-full h-full grid items-center justify-center z-10"
  >
    <div
      class="w-full h-full bg-black bg-opacity-50 flex justify-center items-center animate__animated animate__fadeIn animate__faster absolute"
      @click="if (closeOnBackdropClick) close();"
    />
    <div
      class="w-full max-w-2xl max-h-[90vh] overflow-y-auto p-6 bg-white rounded-lg relative z-20"
    >
      <header class="flex justify-between items-center">
        <slot name="header"/>
        <button v-if="showCloseButton" class="btn btn-circle" @click="close">
          <Icon name="heroicons:x-mark" />
        </button>
      </header>
      <section @click="if (closeOnContentClick) close();">
        <slot/>
      </section>
      <footer class="flex justify-end mt-6">
        <slot name="footer"/>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
const emit = defineEmits(["close"]);
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  closeOnContentClick: {
    type: Boolean,
    default: false,
  },
  closeOnBackdropClick: {
    type: Boolean,
    default: true,
  },
  showCloseButton: {
    type: Boolean,
    default: true,
  },
});

function close() {
  emit("close");
}
</script>
