<template>
  <div class="grid grid-cols-[1fr_18fr]">
    <div class="grid grid-cols-[8fr_1fr_8fr] h-full relative">
      <div
        :class="`rounded-full w-8 h-8 p-2 bg-${bgColor} text-${contentColor} absolute top-0 left-[50%] -translate-x-[50%] text-xs text-center font-semibold`"
      >
        {{ char! }}
      </div>
      <p />
      <div v-if="!lastStep" :class="`bg-${bgColor}`"/>
      <p />
    </div>
    <div class="pb-2">
      <slot />
    </div>
  </div>
</template>
<script setup lang="ts">
const { char, bgColor, contentColor } = defineProps({
  lastStep: {
    type: Boolean,
    default: false,
  },
  char: {
    type: String,
    default: "1",
  },
  bgColor: {
    type: String,
    required: true,
    default: "error",
  },
  contentColor: {
    type: String,
    required: true,
    default: "error-content",
  },
});
</script>
