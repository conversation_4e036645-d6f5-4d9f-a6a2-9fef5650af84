<template>
  <div class="flex gap-4 items-center">
    <div
      class="rounded-lg border p-2 text-center h-24 w-24 text-xs grid items-center"
    >
      <p class="w-full overflow-hidden break-words">
        {{
          cv
            ? cv
            : existingCvName && existingCvName.filename_disk
            ? existingCvName.filename_disk
            : "No file selected"
        }}
      </p>
    </div>
    <label for="cv">
      <div class="btn btn-sm">Select File</div>
      <p class="text-xs mt-1 text-gray-500">
        * only PDF format is accepted. Max 10 MB
      </p>
    </label>
    <input
      id="cv"
      type="file"
      accept=".pdf"
      class="input w-full p-0 rounded-none hidden"
      placeholder="Image"
      @change="handleCVChange($event)"
    />
  </div>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";
const emit = defineEmits(["change"]);
const props = defineProps({
  existingCvName: {
    type: Object,
    required: true,
  },
});

const cv = ref(null);

// const handleCVChange = (e: any) => {
//   cv.value = e.target.files[0].name;
//   emit("change", e.target.files[0]);
// };

const handleCVChange = (e: any) => {
  const existingCv = props.existingCvName;
  const file = e.target.files[0];
  if (file.size > 10485760) {
    Swal.fire({
      title: "File too large",
      html: "<p>Please upload a file less than 10MB.</p><p class='text-sm text-accent'>Try using <a class='text-blue font-semibold' style='color: rgb(29 78 216);' href='https://smallpdf.com/compress-pdf' target='_blank'>CompressPDF</a><p>",
      icon: "info",
      confirmButtonText: "OK",
    });
    cv.value = null;
  } else {
    cv.value = e.target.files[0].name;
    emit("change", e.target.files[0]);
  }
};
</script>
