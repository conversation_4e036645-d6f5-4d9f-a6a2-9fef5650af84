<template>
  <div class="multi-select mt-1">
    <div :class="props.outerClass">
      <div
        class="grid grid-cols-[8fr_1fr] lg:grid-cols-[12fr_1fr] gap-2 items-center"
      >
        <!-- List of selected items -->
        <div class="flex w-full items-center gap-2 flex-wrap">
          <div
            v-for="item in currentOptions"
            :class="props.innerClass"
            @click="removeOption(item)"
          >
            {{ item.label }} <Icon name="heroicons:x-mark" />
          </div>
          <div
            v-if="currentOptions.length === 0"
            class="text-sm p-2 w-full cursor-pointer"
            @click="toggleDropdown"
          >
            {{ placeholder }}
          </div>
        </div>

        <!-- Dropdown Icon -->
        <div
          class="text-xs p-2 bg-gray-100 rounded-lg cursor-pointer text-center"
          @click="toggleDropdown"
        >
          <Icon v-if="showOptions" name="heroicons:chevron-up" />
          <Icon v-else name="heroicons:chevron-down" />
        </div>

        <!-- Dropdown List -->
        <div
          v-if="showOptions"
          class="h-min w-full col-span-2 max-h-96 overflow-y-auto"
        >
          <div
            v-for="option in uniqueOptions"
            :value="option.value"
            class="p-2 border-b cursor-pointer text-sm"
            @click="handleInput(option)"
          >
            {{ option.label }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
type Option = {
  label: string;
  value: string;
};
const props = defineProps({
  context: Object,
  placeholder: {
    type: String,
    default: "Select One or More",
  },
  outerClass: {
    type: String,
    default:
      "p-2 border border-gray-300 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",
  },
  innerClass: {
    type: String,
    default:
      "text-xs p-2 bg-gray-100 rounded-lg hover:bg-error hover:text-error-content hover:cursor-pointer",
  },
});

const showOptions = ref(false);
const currentOptions = ref<any>([]);

onMounted(() => {
  if (props.context!._value) {
    const values = props.context!._value.split(",");
    currentOptions.value = props.context!.selectOptions.filter((option: any) =>
      values.includes(option.value),
    );
  }
});

function handleInput(option: Option) {
  currentOptions.value.push(option);
  updateSelection();
}

function removeOption(option: Option) {
  currentOptions.value = currentOptions.value.filter(
    (item: any) => item.value !== option.value,
  );
  updateSelection();
}

const uniqueOptions = computed(() => {
  //filter already selected options out of the list
  return props.context!.selectOptions.filter(
    (option: any) =>
      !currentOptions.value.find((item: any) => item.value === option.value),
  );
});

function updateSelection() {
  props.context!.node.input(
    currentOptions.value.map((item: any) => item.value).join(";"),
  );
}

function toggleDropdown() {
  showOptions.value = !showOptions.value;
}
</script>
