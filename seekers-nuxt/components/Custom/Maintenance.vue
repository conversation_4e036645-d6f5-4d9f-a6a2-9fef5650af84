<template>
  <div class="min-h-screen flex items-center justify-center bg-base-200 p-4">
    <div class="max-w-md w-full">
      <div class="card bg-base-100 shadow-xl">
        <div class="card-body text-center">
          <!-- Simple Icon -->
          <div class="flex justify-center mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>

          <!-- Content -->
          <h1 class="text-2xl font-bold text-primary mb-4">Under Maintenance</h1>
          <p class="mb-6 text-base-content/70">
            We're currently improving our site to serve you better.
            Please check back soon.
          </p>

          <!-- Simple Divider -->
          <div class="divider" v-if="data?.maintenance_message"></div>

          <!-- Estimated Return -->
          <p class="text-sm text-base-content/60 mb-6">{{ data?.maintenance_message }}</p>

          <!-- Contact Button -->
          <a href="mailto:<EMAIL>" class="btn btn-outline btn-primary btn-sm">
            Contact Support
          </a>
        </div>
      </div>

      <!-- Simple Footer -->
      <div class="mt-6 text-center text-sm text-base-content/50">
        <p> {{ new Date().getFullYear() }} Seekers</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const { data } = useFetch('/api/settings')
</script>