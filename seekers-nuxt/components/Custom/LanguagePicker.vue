<template>
  <div class="grid gap-4">
    <div
      v-for="(l, index) in languages"
      :key="index"
      class="grid grid-cols-[4fr_4fr_1fr] gap-4"
    >
      <select
        v-model="languages[index].language"
        class="select select-bordered"
      >
        <option v-for="lang in languageOptions" :value="lang.value">
          {{ lang.label }}
        </option>
      </select>
      <select v-model="languages[index].level" class="select select-bordered">
        <option v-for="level in languageLevels" :value="level">
          {{ level }}
        </option>
      </select>
      <button
        :disabled="index == 0"
        class="btn btn-error"
        @click="removeFromList(index)"
      >
        x
      </button>
    </div>
    <button
      class="btn"
      type="button"
      style="width: fit-content"
      @click="addNewLanguage"
    >
      Add New Language
    </button>

    <!-- <pre>{{ modelValue }}</pre> -->
  </div>
</template>
<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(["update:modelValue"]);

const languageOptions = useHelper().languageOptions;
const languageLevels = useHelper().languageLevels;
// const languages = ref(props.modelValue as any);

const languages = ref(
  (props.modelValue as string[]).map((item) => {
    const [language, level] = item.split("-");
    return { language, level };
  })
);

// If no languages are provided, start with one empty entry
if (languages.value.length === 0) {
  languages.value.push({ language: "", level: "" });
}

const removeFromList = (index: number) => {
  languages.value.splice(index, 1);
  updateModelValue();
};

const addNewLanguage = () => {
  languages.value.push({
    language: "",
    level: "",
  });
};

// Convert object array back to string array for emitting
const updateModelValue = () => {
  const formattedValue = languages.value
    .filter((item) => item.language && item.level) // Only include complete entries
    .map((item) => `${item.language}-${item.level}`);

  emit("update:modelValue", formattedValue);
};

watch(
  languages,
  () => {
    updateModelValue();
  },
  { deep: true }
);
</script>
