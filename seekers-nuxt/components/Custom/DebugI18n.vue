<template>
  <DevOnly>
    <div
      class="absolute top-8 left-8 z-50 p-6 bg-black text-white rounded shadow-lg"
    >
      <p class="text-center mb-4">--- DEBUG ---</p>
      <p>Current Locale: {{ locale }}</p>
      <p>Browser Locale: {{ browserLocale }}</p>
      <p class="text-center mt-4">---------------</p>
    </div>
  </DevOnly>
</template>

<script setup lang="ts">
const { locale, getBrowserLocale } = useI18n();
const browserLocale = getBrowserLocale();
</script>
