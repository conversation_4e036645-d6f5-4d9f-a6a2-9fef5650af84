<template>
  <div class="-mt-12">
    <FormKit
      type="form"
      action="https://crm.zoho.com/crm/WebToLeadForm"
      name="WebToLeads5550753000006070293"
      method="post"
      enctype="multipart/form-data"
      accept-charset="UTF-8"
      :form-class="'grid lg:grid-cols-2 gap-4 lg:gap-8'"
      :submit-attrs="{
        inputClass: 'btn btn-primary ',
        outerClass: 'max-w-xl mx-auto text-center mt-4',
      }"
      :actions="false"
      incomplete-message="Please fill in all required fields."
    >
      <!-- START: Do not modify this code. -->
      <FormKit
        type="text"
        style="display: none"
        name="xnQsjsdp"
        value="4248f013feb7618574c6d4ad305ed61dfcda44d040b3a5f24e29517f1c11c978"
      />

      <FormKit id="zc_gad" type="hidden" name="zc_gad" value="" />
      <FormKit
        type="text"
        style="display: none"
        name="xmIwtLD"
        value="803ac109a10fe4a76477444ba344c9245eb80f459abd7d6bc852e8ddbfe1a844"
      />
      <FormKit
        type="text"
        style="display: none"
        name="actionType"
        value="TGVhZHM="
      />
      <FormKit
        type="text"
        style="display: none"
        name="returnURL"
        value="https&#x3a;&#x2f;&#x2f;seekers.my&#x2f;refer-a-talent&#x2f;thankyou"
      />
      <!-- END: Do not modify this code. -->

      <FormKit
        id="Last_Name"
        type="text"
        name="Last Name"
        label="Your Full Name*"
        maxlength="80"
        validation="required"
      />
      <FormKit
        id="Email"
        type="email"
        ftype="email"
        name="Email"
        label="Your Email Address*"
        maxlength="100"
        validation="required"
      />
      <FormKit
        id="Mobile"
        type="text"
        name="Mobile"
        label="Your Mobile Number*"
        maxlength="30"
        validation="required"
      />
      <FormKit
        id="LEADCF9"
        type="text"
        name="LEADCF9"
        label="Job Title & Company Name for Referral*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF12"
        type="text"
        name="LEADCF12"
        label="Your Referral's Name*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF10"
        type="email"
        ftype="email"
        name="LEADCF10"
        label="Your Referral's Email Address*"
        maxlength="100"
        validation="required"
      />
      <FormKit
        id="LEADCF11"
        type="text"
        name="LEADCF11"
        label="Your Referral's Phone Number*"
        maxlength="30"
        validation="required"
      />
      <FormKit
        id="LEADCF13"
        type="text"
        name="LEADCF13"
        label="Your Referral's LinkedIn Profile*"
        maxlength="255"
        pattern="https://(?:www\.)?linkedin\.com/in/.+"
        validation="required"
      />
      <div class="py-4 lg:col-span-2">
        <FormKit
          id="privacyTool5550753000006070293"
          autocomplete="off"
          type="checkbox"
          name="privacyTool"
          validation="accepted"
          label="Agree to the Privacy Policy and Terms of Service."
          :value="true"
          :validation-messages="{
            accepted:
              'Please agree to the Privacy Policy and Terms of Service. Visit Seekers.my/Terms for more information.',
          }"
          :classes="{ wrapper: 'flex gap-4 items-center', input: 'checkbox' }"
        />
      </div>
      <button class="btn bg-[#130F26] text-white lg:col-span-2" type="submit">
        Submit
      </button>
    </FormKit>
  </div>
</template>
