<template>
  <div v-auto-animate>
    <div v-if="percentage !== 100" class="relative">
      <div
        class="radial-progress text-gray-200 ml-2 mt-2"
        style="--value: 100; --size: 11rem; --thickness: 1rem"
      />
      <div
        class="radial-progress text-teal-700 absolute top-0 left-0"
        :style="`--value: ${percentage}; --size: 12rem; --thickness: 1.5rem`"
      >
        <p v-if="percentage" class="text-center text-xs">
          {{ percentage }}%<br >Complete
        </p>
        <p v-else class="text-center text-xs">Loading...</p>
      </div>
    </div>
  </div>
</template>
<script setup>
const { percentage } = defineProps({
  percentage: {
    type: Number,
    default: 10,
    required: true,
  },
});
</script>
