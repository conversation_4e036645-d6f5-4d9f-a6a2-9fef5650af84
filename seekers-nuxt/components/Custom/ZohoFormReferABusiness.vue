<template>
  <div class="-mt-12">
    <FormKit
      type="form"
      action="https://crm.zoho.com/crm/WebToLeadForm"
      name="WebToLeads5550753000007437051"
      method="post"
      enctype="multipart/form-data"
      accept-charset="UTF-8"
      :form-class="'grid lg:grid-cols-2 gap-4 lg:gap-8'"
      :submit-attrs="{
        inputClass: 'btn btn-primary ',
        outerClass: 'max-w-xl mx-auto text-center mt-4',
      }"
      :actions="false"
      incomplete-message="Please fill in all required fields."
    >
      <!-- START: Do not modify this code. -->
      <FormKit
        type="text"
        style="display: none"
        name="xnQsjsdp"
        value="4248f013feb7618574c6d4ad305ed61dfcda44d040b3a5f24e29517f1c11c978"
      />

      <FormKit id="zc_gad" type="hidden" name="zc_gad" value="" />
      <FormKit
        type="text"
        style="display: none"
        name="xmIwtLD"
        value="803ac109a10fe4a76477444ba344c924c564b57e019ba6a767a1715b1330595a"
      />
      <FormKit
        type="text"
        style="display: none"
        name="actionType"
        value="TGVhZHM="
      />
      <FormKit
        type="text"
        style="display: none"
        name="returnURL"
        value="https&#x3a;&#x2f;&#x2f;seekers.my&#x2f;refer-a-business&#x2f;thankyou"
      />
      <!-- END: Do not modify this code. -->

      <FormKit
        id="Last_Name"
        type="text"
        name="Last Name"
        label="Referee's Name*"
        maxlength="80"
        validation="required"
      />
      <FormKit
        id="LEADCF20"
        type="email"
        name="LEADCF20"
        label="Referee's Email Address*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF15"
        type="text"
        name="LEADCF15"
        label="Referee's Mobile Phone*"
        maxlength="30"
        validation="required"
      />
      <FormKit
        id="LEADCF14"
        type="text"
        name="LEADCF14"
        label="Referral's Full Name*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF17"
        type="text"
        name="LEADCF17"
        label="Referral's Job Title*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF16"
        type="text"
        name="LEADCF16"
        label="Referral's Company Name*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF19"
        type="email"
        name="LEADCF19"
        label="Referral's Email Address*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF18"
        type="text"
        name="LEADCF18"
        label="Referral's Phone Number*"
        maxlength="30"
        validation="required"
      />
      <FormKit
        id="Website"
        type="text"
        name="Website"
        label="Company Website Link*"
        maxlength="255"
        validation="required"
      />
      <FormKit
        id="LEADCF2"
        type="text"
        name="LEADCF2"
        maxlength="255"
        label="Message*"
        validation="required"
      />
      <div class="py-4 lg:col-span-2">
        <FormKit
          id="privacyTool5550753000007437051"
          autocomplete="off"
          type="checkbox"
          name="privacyTool"
          validation="accepted"
          label="Agree to the Privacy Policy and Terms of Service."
          :value="true"
          :validation-messages="{
            accepted:
              'Please agree to the Privacy Policy and Terms of Service. Visit Seekers.my/Terms for more information.',
          }"
          :classes="{ wrapper: 'flex gap-4 items-center', input: 'checkbox' }"
        />
      </div>
      <button class="btn bg-[#E71D36] text-white lg:col-span-2" type="submit">
        Submit
      </button>
    </FormKit>
  </div>
</template>
