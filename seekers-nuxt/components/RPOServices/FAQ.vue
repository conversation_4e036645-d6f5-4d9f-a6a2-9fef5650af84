<template>
  <div id="faq" class="bg-[#4C0088] p-4 md:p-16">
    <div class="grid lg:grid-cols-2 gap-4 max-w-7xl mx-auto">
      <img
        src="https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1691132230/website/rpo-services/faq-min_xlj4zc.jpg"
        class="h-4/5 m-auto"
      >

      <div>
        <div class="text-white">
          <p class="text-sm">FAQ</p>
          <h2 class="text-3xl font-semibold mt-2 mb-8 lg:w-3/4">
            Have Any Questions About Our RPO Service?
          </h2>
        </div>

        <div class="block flex-col gap-4">
          <div
            v-for="(faq, index) in faqs"
            class="flex flex-col gap-4 p-4 cursor-pointer hover:bg-gray-100 shadow-xl"
            :class="{
              'bg-slate-100': activeFaq === index,
              'bg-white': activeFaq !== index,
            }"
            @click="activeFaq = index"
          >
            <div class="flex items-center gap-4">
              <div
                class="h-6 w-6 rounded-full bg-[#8119D2]/30 text-black font-semibold flex justify-center"
              >
                {{ faq.no }}
              </div>
              <p class="font-semibold text-sm">
                {{ faq.question }}
              </p>
              <div
                class="h-6 w-6 rounded-full bg-[#8119D2]/30 flex justify-center items-center ml-auto"
              >
                <Icon
                  :name="
                    activeFaq == index ? 'bx:bxs-down-arrow' : 'bx:bxs-up-arrow'
                  "
                  class="text-[#8119D2]"
                />
              </div>
            </div>
            <!-- FAQ ANSWERS MOBILE -->
            <div
              v-if="activeFaq === index"
              class="w-full border-t pt-8 pb-4 animate__animated animate__fadeIn"
            >
              {{ faq.answer }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const activeFaq = ref<number | null>(0);

const faqs = [
  {
    no: "1",
    question:
      "How much is the fee for Seekers' Recruitment Process Outsourcing Service?",
    answer:
      "The fee for Seekers' recruitment outsourcing service is fully customized based on your specific needs. We can provide detailed pricing information after an initial meeting to better understand your requirements and expectations.",
  },
  {
    no: "2",
    question: "How does Seekers pricing compare to headhunting services?",
    answer:
      "Seekers offers a more affordable pricing structure, especially if you have multiple hiring needs. We provide cost-effective solutions while ensuring that the candidates sourced are of high quality.",
  },
  {
    no: "3",
    question:
      "How does Seekers recruitment outsourcing differ from job portals?",
    answer:
      "Seekers Recruitment outsourcing goes beyond job portals to source candidates, including passive job seekers who may not actively appear on job portals. We utilize an extensive approach to attract high-quality talent.",
  },
  {
    no: "4",
    question: "How can Seekers help speed up our hiring process?",
    answer:
      "Seekers can help in optimizing and improving your hiring process to speed up the recruitment cycle. We assist in drafting job descriptions, designing hiring processes, and planning hiring schedules to streamline the entire recruitment process.",
  },
  {
    no: "5",
    question: "How is Seekers different from typical agencies?",
    answer:
      "Seekers is not just a typical agency, but we are a hiring partner. We take the time to understand not only your hiring requirements but also your business goals. Our deep understanding of your business enables us to provide tailored recruitment solutions for your real success.",
  },
  {
    no: "6",
    question:
      "What process areas does Seekers cover in recruitment outsourcing?",
    answer:
      "Seekers covers a wide range of process areas in recruitment outsourcing, including drafting job descriptions, designing hiring processes, planning hiring schedules, gathering candidate pools, screening applicants, arranging interviews, offer negotiation, resume management, job ad management, and recruitment marketing planning.",
  },
];
</script>
