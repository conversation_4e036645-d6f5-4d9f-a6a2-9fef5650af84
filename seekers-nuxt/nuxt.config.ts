export default defineNuxtConfig({
  experimental: {
    componentIslands: {
      selectiveClient: true,
    },
    typedPages: true,
  },

  devtools: { enabled: true },

  devServer: {
    port: 3001,
  },

  $production: {
    nitro: {
      preset: "cloudflare",
      routeRules: {
        // StaleWhileRevalidate (SWR)
        "/": { swr: 300 },
        "/** ": { swr: 86400 },
        "/candidate/dashboard/**": { ssr: false },
        "/company/**": { swr: 86400 },
        "/employer": { swr: 86400 },
        "/hrtrends": { swr: 86400 },
        "/jobs": { swr: 3600 },
        "/jobs/**": { swr: 3600 },
        "/jobs/**/apply": { ssr: false },
        "/recruiter": { swr: 86400 },
        "/recruiter/**": { ssr: false },
        "/salary-guide": { swr: 86400 },
        // Prerender (Static)
        "/about": { prerender: true },
        "/contact": { prerender: true },
        "/forgot-password": { prerender: true },
        "/global-hr": { prerender: true },
        "/privacy": { prerender: true },
        "/refer-a-business": { prerender: true },
        "/rpo-services": { prerender: true },
        "/terms": { prerender: true },
      },
    },
  },

  nitro: {
    experimental: {
      openAPI: true,
    },
    routeRules: {
      "/backend/**": { proxy: "https://directus.seekers.my/**" },
      // Redirects
      "/candidates": { redirect: "/candidate" },
      "/companies": { redirect: "/company" },
      "/employers": { redirect: "/employer" },
    },
  },

  modules: [
    "@formkit/auto-animate/nuxt",
    "@nuxt/devtools",
    "@nuxtjs/device",
    "@nuxt/fonts",
    "@nuxtjs/i18n",
    "@nuxtjs/partytown",
    "@nuxtjs/tailwindcss",
    "@vueuse/nuxt",
    "nuxt-icon",
    "magic-regexp/nuxt",
    "@nuxt/eslint",
    "@nuxt/image",
    'nuxt-markdown-render'
  ],

  i18n: {
    defaultLocale: "en",
    detectBrowserLanguage: false,
    locales: ["en", "ja"],
    strategy: "prefix_except_default",
  },

  nuxtMarkdownRender: {
    as: 'article', // default 'div'
    options: {
      html: false // default true
    }
  },

  runtimeConfig: {
    directus:{
      token: process.env.NUXT_DIRECTUS_TOKEN || "MISSING_NUXT_DIRECTUS_TOKEN",
    },
    public: {
      directus: {
        url: "https://directus.seekers.my", // TODO: Move to non-public. Refactor backend calls to be from server-side
      },
    },
    blog: {
      apiKey: "MISSING_NUXT_BLOG_API_KEY",
      apiUrl: "MISSING_NUXT_BLOG_API_URL",
    },
    ocrspace: {
      key: "MISSING_NUXT_OCRSPACE_KEY",
      url: "MISSING_NUXT_OCRSPACE_URL",
    },
  },

  compatibilityDate: "2025-02-23",
});