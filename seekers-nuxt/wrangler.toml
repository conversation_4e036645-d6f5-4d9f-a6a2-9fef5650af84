main = "./.output/server/index.mjs"
workers_dev = true
compatibility_date = "2023-10-02"

[env.production]
name = "seekers-nuxt"
routes = [
  { pattern = "new.seekers.my/*", zone_name = "seekers.my" }
]

[env.main]
name = "seekers-nuxt-beta"
routes = [
   { pattern = "beta1.seekers.my/*", zone_name = "seekers.my" }
]

[env.dev]   
name = "seekers-nuxt-dev"
routes = [
   { pattern = "dev1.seekers.my/*", zone_name = "seekers.my" }
]

[env.pr]   
name = "seekers-nuxt-pr"
routes = [
   { pattern = "pr.seekers.my/*", zone_name = "seekers.my" }
]

[site]
bucket = ".output/public"