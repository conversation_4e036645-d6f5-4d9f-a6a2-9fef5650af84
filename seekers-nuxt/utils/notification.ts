import Swal from "sweetalert2";

export const successToast = (message: string) => {
  Swal.mixin({
    toast: true,
    position: "top-right",
    showConfirmButton: false,
    timer: 1500,
    timerProgressBar: false,
  }).fire({
    icon: "success",
    title: message,
  });
};

export const errorToast = (message: string) => {
  Swal.mixin({
    toast: true,
    position: "top-right",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: false,
  }).fire({
    icon: "error",
    title: message,
  });
};

export const infoToast = (message: string) => {
  Swal.mixin({
    toast: true,
    position: "top-right",
    showConfirmButton: false,
    timer: 1500,
    timerProgressBar: false,
  }).fire({
    icon: "info",
    title: message,
  });
};