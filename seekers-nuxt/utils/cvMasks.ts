export default (): Mask[] => {
  const email = {
    regex: createRegExp(
      oneOrMore(char)
        .and("@")
        .and(oneOrMore(char))
        .and(".")
        .and(oneOrMore(char)),
    ),
    replacement: "<EMAIL>",
  };

  const phoneNumber = {
    regex: new RegExp(/(\+?6?01|\+?6?0)[0-9]-?[0-9]{7,8}/),
    replacement: "",
  };

  const identityCard = {
    regex: new RegExp(/(\d{6})-?(\d{2})-?(\d{4})/),
    replacement: "",
  };

  // Add more masks here...

  return [email, phoneNumber, identityCard];
};
