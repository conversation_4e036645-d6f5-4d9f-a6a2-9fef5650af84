export type TODO = any;

export interface Industry {
    id:              number;
    status:          string;
    user_created:    null;
    date_created:    null;
    user_updated:    null;
    date_updated:    null;
    name:            string;
    color:           string;
    slug:            string;
    seo_title:       null;
    seo_description: null;
    sort:            number;
    parent:          number;
}

export interface Company {
    name:         string;
    logo:         string;
    size:         string;
    url:          null;
    industry:     Industry;
    location_map: LocationMap;
    address:      null;
    post_code:    null;
    building:     null;
    city:         string;
    state:        string;
    id:           number;
    slug:         string;
    reg_no:       string;
    jobs:         Job[];
    overview:     string;
    highlight:    string;
}

export interface LocationMap {
    type:        string;
    coordinates: number[];
}

export interface Role {
    id:              number;
    status:          string;
    sort:            null;
    user_created:    null;
    date_created:    null;
    user_updated:    null;
    date_updated:    null;
    candidates:      null;
    job_category:    null;
    is_digital:      boolean;
    color:           null;
    seo_title:       null;
    seo_description: null;
    name:            string;
    slug:            string;
    parent_id:       Role;
    is_technical:    boolean;
    is_popular:      boolean;
}


export type User = {
    id: number;
    email: string;
    name: string;
    date_created: string;
    date_updated: string;
}


export interface Job {
    id: number
    user_created?: string
    date_created?: string
    user_updated?: string
    date_updated?: string
    title: string
    description: string
    package: string
    status: string
    industry: Industry
    pic_consultant: User | number
    tags: string[]
    pic_sales: User[]
    company: Company
    state: string
    role: Role
    interview_method: string
    video_url: string
    visible_to_recruiters: boolean
    experience_min_years: number
    salary_min: number
    salary_max: number
    work_type: string
    allow_no_experience: boolean
    allow_foreigner: boolean
    must_have: string
    responsibilities: string
    other_requirements: string
    close_reason: string
    expires_on: Date
    working_days: string
    working_hours: string
    allowances: string
    bonus: string
    holidays: string
    annual_leave: string
    probation_months: number
    position_type: string
    benefits: string
    age_min: number
    age_max: number
    gender: string
    race: string
    languages: string[]
    portfolio_required: boolean
    slug: string
    features: string[]
    fullpath: string
    experience_level: string
    saved_by_candidates: CandidateProfile[] | number[]
    screening_questions: any[]
    is_confidential: boolean
}


export interface CandidateProfile {
    id: number;
    date_created: Date;
    date_updated: Date;
    name: string;
    email: string;
    open_to_work: boolean;
    mobile: string;
    about_me: string;
    gender: string;
    birthday: Date;
    birth_year: string;
    ic: string;
    race: string;
    state: string;
    has_visa: boolean;
    has_work_permit: boolean;
    own_transport: boolean;
    willing_to_travel: boolean;
    own_driver_license: boolean;
    languages: string[];
    education_level: string;
    current_position: string;
    address: string;
    is_local: boolean;
    level: string;
    years_of_experience: number;
    notice_period: string;
    latest_role: string;
    salary_current: number;
    salary_expected: number;
    last_seen: string;
    website: string;
    linkedin: string;
    facebook: string;
    twitter: string;
    github: string;
    instagram: string;
    tags: string[];
    status: string;
    cv_parsed_raw: any;
    profile_photo: ProfilePhoto;
    cv: any;
    applications: Application[];
    education_history: EducationHistory[];
    work_history: WorkHistory[];
    license_or_certification: any[];
    saved_jobs: any[];
    companies_followed: CompaniesFollowed[] | number[];
}

export interface Application {
    id: number;
    status: string;
    candidate: number;
    job: number;
    user_created: string;
    date_created: Date;
    user_updated: string;
    date_updated: Date;
    question_and_answers: any;
}

export interface CompaniesFollowed {
    id: number;
    status: string;
    user_created: string;
    date_created: Date;
    user_updated: string;
    date_updated: Date;
    name: string;
    slug: string;
    reg_no: string;
    building?: string;
    address?: string;
    post_code?: string;
    city: string;
    state: string;
    lat?: number;
    lng?: number;
    overview: string;
    highlight: string;
    logo: string;
    background_image: string;
    is_highlight: boolean;
    is_top_employer: boolean;
    url: string;
    size: string;
    industry: number;
    location_map: string;
    overview_test: string;
    is_confidential: boolean;
    candidates_followers: number;
    jobs: number[];
}

export interface EducationHistory {
    id: number;
    user_created: string;
    date_created: Date;
    user_updated: string;
    date_updated: Date;
    qualification: string;
    title: string;
    institute: string;
    start: Date;
    end: Date;
    description: string;
    candidate: number;
    results: string;
}

export interface ProfilePhoto {
    id: string;
    storage: string;
    filename_disk: string;
    filename_download: string;
    title: string;
    type: string;
    folder: string;
    uploaded_by: string;
    created_on: Date;
    modified_by: string;
    modified_on: Date;
    charset: string;
    filesize: number;
    width: number;
    height: number;
    duration: string;
    embed: string;
    description: string;
    location: string;
    tags: string;
    metadata: Metadata;
    focal_point_x: string;
    focal_point_y: string;
    tus_id: string;
    tus_data: string;
    uploaded_on: Date;
}

export interface Metadata {
}

export interface WorkHistory {
    id: number;
    sort: string;
    user_created: string;
    date_created: Date;
    user_updated: string;
    date_updated: Date;
    job_title: string;
    company: Company | number;
    start: Date;
    current_job: boolean;
    end: Date;
    description: string;
    candidate: number;
}

export interface Socials {
    facebook: string;
    twitter: string;
    instagram: string;
    linkedin: string;
    github: string;
    website: string;
}

export interface CompanySearchFilter {
    keywords: string,
    limit: number,
    page: number,
    sort: string,
    state: string
}

export interface JobSearchFilter {
    experience_levels: string[];
    includeFullTime: boolean;
    includeContract: boolean;
    includeInternship: boolean;
    position_types: string[],
    keywords: string,
    limit: number,
    page: number,
    salary_min: number,
    salary_max: number,
    sort: string,
    state: string,
    role: string,
}


export interface JobCategories {
    id:           number;
    status:       string;
    user_created: string;
    date_created: Date;
    user_updated: string;
    date_updated: Date;
    name:         string;
    slug:         string;
    image:        string;
    sort:         number;
    roles:        Role[];
}
export interface Schema {
    candidates: CandidateProfile[],
    companies: Company[],
    industries: Industry[],
    job_categories: JobCategories[],
    jobs: Job[],
    roles: Role[],
    users: User[],
    candidate_educations: TODO,
    candidate_portfolio: TODO,
    candidate_certifications: TODO,
    candidate_work_histories: TODO,
    candidate_following: TODO
}


// FORMS

export type BasicInfoForm = {
    name: string;
    email: string;
    password: string;
    password_confirm: string;
    mobile: string;
    birth_date: string;
    is_local: boolean;
    state: string;
    gender: string;
}