export const COUNTRIES = [
    { code: "AF", name: "Afghanistan" },
    { code: "AX", name: "\u00c5land Islands" },
    { code: "AL", name: "Albania" },
    { code: "DZ", name: "Algeria" },
    { code: "AS", name: "American Samoa" },
    { code: "AD", name: "Andorra" },
    { code: "AO", name: "Angola" },
    { code: "AI", name: "Anguilla" },
    { code: "AQ", name: "Antarctica" },
    { code: "AG", name: "Antigua and Barbuda" },
    { code: "AR", name: "Argentina" },
    { code: "AM", name: "Armenia" },
    { code: "AW", name: "Aruba" },
    { code: "AU", name: "Australia" },
    { code: "AT", name: "Austria" },
    { code: "AZ", name: "Azerbaijan" },
    { code: "BS", name: "Bahamas" },
    { code: "BH", name: "Bahrain" },
    { code: "BD", name: "Bangladesh" },
    { code: "BB", name: "Barbados" },
    { code: "B<PERSON>", name: "Belarus" },
    { code: "BE", name: "Belgium" },
    { code: "BZ", name: "Belize" },
    { code: "BJ", name: "Benin" },
    { code: "BM", name: "Bermuda" },
    { code: "BT", name: "Bhutan" },
    { code: "BO", name: "Bolivia, Plurinational State of" },
    { code: "BQ", name: "Bonaire, Sint Eustatius and Saba" },
    { code: "BA", name: "Bosnia and Herzegovina" },
    { code: "BW", name: "Botswana" },
    { code: "BV", name: "Bouvet Island" },
    { code: "BR", name: "Brazil" },
    { code: "IO", name: "British Indian Ocean Territory" },
    { code: "BN", name: "Brunei Darussalam" },
    { code: "BG", name: "Bulgaria" },
    { code: "BF", name: "Burkina Faso" },
    { code: "BI", name: "Burundi" },
    { code: "KH", name: "Cambodia" },
    { code: "CM", name: "Cameroon" },
    { code: "CA", name: "Canada" },
    { code: "CV", name: "Cape Verde" },
    { code: "KY", name: "Cayman Islands" },
    { code: "CF", name: "Central African Republic" },
    { code: "TD", name: "Chad" },
    { code: "CL", name: "Chile" },
    { code: "CN", name: "China" },
    { code: "CX", name: "Christmas Island" },
    { code: "CC", name: "Cocos (Keeling) Islands" },
    { code: "CO", name: "Colombia" },
    { code: "KM", name: "Comoros" },
    { code: "CG", name: "Congo" },
    { code: "CD", name: "Congo, the Democratic Republic of the" },
    { code: "CK", name: "Cook Islands" },
    { code: "CR", name: "Costa Rica" },
    { code: "CI", name: "C\u00f4te d'Ivoire" },
    { code: "HR", name: "Croatia" },
    { code: "CU", name: "Cuba" },
    { code: "CW", name: "Cura\u00e7ao" },
    { code: "CY", name: "Cyprus" },
    { code: "CZ", name: "Czech Republic" },
    { code: "DK", name: "Denmark" },
    { code: "DJ", name: "Djibouti" },
    { code: "DM", name: "Dominica" },
    { code: "DO", name: "Dominican Republic" },
    { code: "EC", name: "Ecuador" },
    { code: "EG", name: "Egypt" },
    { code: "SV", name: "El Salvador" },
    { code: "GQ", name: "Equatorial Guinea" },
    { code: "ER", name: "Eritrea" },
    { code: "EE", name: "Estonia" },
    { code: "ET", name: "Ethiopia" },
    { code: "FK", name: "Falkland Islands (Malvinas)" },
    { code: "FO", name: "Faroe Islands" },
    { code: "FJ", name: "Fiji" },
    { code: "FI", name: "Finland" },
    { code: "FR", name: "France" },
    { code: "GF", name: "French Guiana" },
    { code: "PF", name: "French Polynesia" },
    { code: "TF", name: "French Southern Territories" },
    { code: "GA", name: "Gabon" },
    { code: "GM", name: "Gambia" },
    { code: "GE", name: "Georgia" },
    { code: "DE", name: "Germany" },
    { code: "GH", name: "Ghana" },
    { code: "GI", name: "Gibraltar" },
    { code: "GR", name: "Greece" },
    { code: "GL", name: "Greenland" },
    { code: "GD", name: "Grenada" },
    { code: "GP", name: "Guadeloupe" },
    { code: "GU", name: "Guam" },
    { code: "GT", name: "Guatemala" },
    { code: "GG", name: "Guernsey" },
    { code: "GN", name: "Guinea" },
    { code: "GW", name: "Guinea-Bissau" },
    { code: "GY", name: "Guyana" },
    { code: "HT", name: "Haiti" },
    { code: "HM", name: "Heard Island and McDonald Islands" },
    { code: "VA", name: "Holy See (Vatican City State)" },
    { code: "HN", name: "Honduras" },
    { code: "HK", name: "Hong Kong" },
    { code: "HU", name: "Hungary" },
    { code: "IS", name: "Iceland" },
    { code: "IN", name: "India" },
    { code: "ID", name: "Indonesia" },
    { code: "IR", name: "Iran, Islamic Republic of" },
    { code: "IQ", name: "Iraq" },
    { code: "IE", name: "Ireland" },
    { code: "IM", name: "Isle of Man" },
    { code: "IL", name: "Israel" },
    { code: "IT", name: "Italy" },
    { code: "JM", name: "Jamaica" },
    { code: "JP", name: "Japan" },
    { code: "JE", name: "Jersey" },
    { code: "JO", name: "Jordan" },
    { code: "KZ", name: "Kazakhstan" },
    { code: "KE", name: "Kenya" },
    { code: "KI", name: "Kiribati" },
    { code: "KP", name: "Korea, Democratic People's Republic of" },
    { code: "KR", name: "Korea, Republic of" },
    { code: "KW", name: "Kuwait" },
    { code: "KG", name: "Kyrgyzstan" },
    { code: "LA", name: "Lao People's Democratic Republic" },
    { code: "LV", name: "Latvia" },
    { code: "LB", name: "Lebanon" },
    { code: "LS", name: "Lesotho" },
    { code: "LR", name: "Liberia" },
    { code: "LY", name: "Libya" },
    { code: "LI", name: "Liechtenstein" },
    { code: "LT", name: "Lithuania" },
    { code: "LU", name: "Luxembourg" },
    { code: "MO", name: "Macao" },
    { code: "MK", name: "Macedonia, the Former Yugoslav Republic of" },
    { code: "MG", name: "Madagascar" },
    { code: "MW", name: "Malawi" },
    { code: "MY", name: "Malaysia" },
    { code: "MV", name: "Maldives" },
    { code: "ML", name: "Mali" },
    { code: "MT", name: "Malta" },
    { code: "MH", name: "Marshall Islands" },
    { code: "MQ", name: "Martinique" },
    { code: "MR", name: "Mauritania" },
    { code: "MU", name: "Mauritius" },
    { code: "YT", name: "Mayotte" },
    { code: "MX", name: "Mexico" },
    { code: "FM", name: "Micronesia, Federated States of" },
    { code: "MD", name: "Moldova, Republic of" },
    { code: "MC", name: "Monaco" },
    { code: "MN", name: "Mongolia" },
    { code: "ME", name: "Montenegro" },
    { code: "MS", name: "Montserrat" },
    { code: "MA", name: "Morocco" },
    { code: "MZ", name: "Mozambique" },
    { code: "MM", name: "Myanmar" },
    { code: "NA", name: "Namibia" },
    { code: "NR", name: "Nauru" },
    { code: "NP", name: "Nepal" },
    { code: "NL", name: "Netherlands" },
    { code: "NC", name: "New Caledonia" },
    { code: "NZ", name: "New Zealand" },
    { code: "NI", name: "Nicaragua" },
    { code: "NE", name: "Niger" },
    { code: "NG", name: "Nigeria" },
    { code: "NU", name: "Niue" },
    { code: "NF", name: "Norfolk Island" },
    { code: "MP", name: "Northern Mariana Islands" },
    { code: "NO", name: "Norway" },
    { code: "OM", name: "Oman" },
    { code: "PK", name: "Pakistan" },
    { code: "PW", name: "Palau" },
    { code: "PS", name: "Palestine, State of" },
    { code: "PA", name: "Panama" },
    { code: "PG", name: "Papua New Guinea" },
    { code: "PY", name: "Paraguay" },
    { code: "PE", name: "Peru" },
    { code: "PH", name: "Philippines" },
    { code: "PN", name: "Pitcairn" },
    { code: "PL", name: "Poland" },
    { code: "PT", name: "Portugal" },
    { code: "PR", name: "Puerto Rico" },
    { code: "QA", name: "Qatar" },
    { code: "RE", name: "R\u00e9union" },
    { code: "RO", name: "Romania" },
    { code: "RU", name: "Russian Federation" },
    { code: "RW", name: "Rwanda" },
    { code: "BL", name: "Saint Barth\u00e9lemy" },
    { code: "SH", name: "Saint Helena, Ascension and Tristan da Cunha" },
    { code: "KN", name: "Saint Kitts and Nevis" },
    { code: "LC", name: "Saint Lucia" },
    { code: "MF", name: "Saint Martin (French part)" },
    { code: "PM", name: "Saint Pierre and Miquelon" },
    { code: "VC", name: "Saint Vincent and the Grenadines" },
    { code: "WS", name: "Samoa" },
    { code: "SM", name: "San Marino" },
    { code: "ST", name: "Sao Tome and Principe" },
    { code: "SA", name: "Saudi Arabia" },
    { code: "SN", name: "Senegal" },
    { code: "RS", name: "Serbia" },
    { code: "SC", name: "Seychelles" },
    { code: "SL", name: "Sierra Leone" },
    { code: "SG", name: "Singapore" },
    { code: "SX", name: "Sint Maarten (Dutch part)" },
    { code: "SK", name: "Slovakia" },
    { code: "SI", name: "Slovenia" },
    { code: "SB", name: "Solomon Islands" },
    { code: "SO", name: "Somalia" },
    { code: "ZA", name: "South Africa" },
    { code: "GS", name: "South Georgia and the South Sandwich Islands" },
    { code: "SS", name: "South Sudan" },
    { code: "ES", name: "Spain" },
    { code: "LK", name: "Sri Lanka" },
    { code: "SD", name: "Sudan" },
    { code: "SR", name: "Suriname" },
    { code: "SJ", name: "Svalbard and Jan Mayen" },
    { code: "SZ", name: "Swaziland" },
    { code: "SE", name: "Sweden" },
    { code: "CH", name: "Switzerland" },
    { code: "SY", name: "Syrian Arab Republic" },
    { code: "TW", name: "Taiwan, Province of China" },
    { code: "TJ", name: "Tajikistan" },
    { code: "TZ", name: "Tanzania, United Republic of" },
    { code: "TH", name: "Thailand" },
    { code: "TL", name: "Timor-Leste" },
    { code: "TG", name: "Togo" },
    { code: "TK", name: "Tokelau" },
    { code: "TO", name: "Tonga" },
    { code: "TT", name: "Trinidad and Tobago" },
    { code: "TN", name: "Tunisia" },
    { code: "TR", name: "Turkey" },
    { code: "TM", name: "Turkmenistan" },
    { code: "TC", name: "Turks and Caicos Islands" },
    { code: "TV", name: "Tuvalu" },
    { code: "UG", name: "Uganda" },
    { code: "UA", name: "Ukraine" },
    { code: "AE", name: "United Arab Emirates" },
    { code: "GB", name: "United Kingdom" },
    { code: "US", name: "United States" },
    { code: "UM", name: "United States Minor Outlying Islands" },
    { code: "UY", name: "Uruguay" },
    { code: "UZ", name: "Uzbekistan" },
    { code: "VU", name: "Vanuatu" },
    { code: "VE", name: "Venezuela, Bolivarian Republic of" },
    { code: "VN", name: "Viet Nam" },
    { code: "VG", name: "Virgin Islands, British" },
    { code: "VI", name: "Virgin Islands, U.S." },
    { code: "WF", name: "Wallis and Futuna" },
    { code: "EH", name: "Western Sahara" },
    { code: "YE", name: "Yemen" },
    { code: "ZM", name: "Zambia" },
    { code: "ZW", name: "Zimbabwe" },
];


export const INDUSTRIES = [
    {
        id: 69,
        name: "Tech / IT",
        color: "#BA8DC0",
        slug: "industry-tech-it",
    },
    {
        id: 77,
        name: "Financial Services",
        color: "#C71B4D",
        slug: "industry-finance-banking-insurance",
    },
    {
        id: 92,
        name: "Construction / Real Estate / Interior Design",
        color: "#FAAD9B",
        slug: "industry-construction-real-estate",
    },
    {
        id: 105,
        name: "Logistics / Transportation",
        color: "#90F588",
        slug: "industry-logistics-transportation",
    },
    {
        id: 112,
        name: "Ad, Marketing / Media / Publishing",
        color: "#8BCBD6",
        slug: "industry-media-publishing",
    },
    {
        id: 119,
        name: "Professional Firms / Services, BPO",
        color: "#8A93AE",
        slug: "industry-professional-service-outsourcing",
    },
    {
        id: 127,
        name: "Tourism / Entertainment / Hobby",
        color: "#F6A5AF",
        slug: "industry-tourism-entertainment-hobby",
    },
    {
        id: 136,
        name: "Energy / Material",
        color: "#FCD98C",
        slug: "industry-energy-material",
    },
    {
        id: 157,
        name: "Manufacturing / Engineering",
        color: "#7C9EB4",
        slug: "industry-industrial-manufacturing",
    },
    {
        id: 184,
        name: "Retail / Wholesale / Distributor",
        color: "#63D8EF",
        slug: "industry-retail-wholesale-distributor",
    },
    {
        id: 226,
        name: "F&B (Restaurant, Bar)",
        color: "#FFA600",
        slug: "industry-restaurant-bar",
    },
    {
        id: 246,
        name: "Education",
        color: "#85CFF6",
        slug: "industry-education",
    },
    {
        id: 247,
        name: "Medical / Healthcare",
        color: "#FF222E",
        slug: "industry-medical-healthcare",
    },
    {
        id: 249,
        name: "Others",
        color: "#12A3A1",
        slug: "industry-others",
    },
    {
        id: 519,
        name: "FMCG (Fast Moving Consumer Goods)",
        color: null,
        slug: "industry-fmcg",
    },
];

export const BANKS = [
    "Maybank",
    "CIMB",
    "Affin Bank",
    "RHB Bank",
    "Hong Leong Bank",
    "HSBC Bank",
    "AmBank",
    "Standard Chartered Bank",
    "Public Bank",
    "Alliance Bank",
    "Agrobank",
    "Bank Muamalat",
    "UOB",
    "OCBC Bank",
    "Citibank",
];

export const LANGUAGE_LEVELS = ["basic", "intermediate", "advanced", "native"];

export const EXPERIENCE_LEVELS = [
    {value: "Entry Level",text: "Entry Level"},
    {value: "Junior Executive",text: "Junior Executive"},
    {value: "Senior Executive",text: "Senior Executive"},
    {value: "Manager",text: "Manager"},
    {value: "Senior Manager",text: "Senior Manager"},
    {value: "C-Level",text: "C-Level"},
];

export const RECRUITER_EXPERIANCE_OPTIONS = [
    { label: "1-2 years", value: "1-2 years" },
    { label: "3-4 years", value: "3-4 years" },
    { label: "5-6 years", value: "5-6 years" },
];

export const SPECIALIZATION_OPTIONS = INDUSTRIES.map(industry => ({ label: industry.name, value: industry.id }));

export const LANGUAGE_OPTIONS = [
    { label: "English", value: "english" },
    { label: "Malay", value: "malay" },
    { label: "Mandarin", value: "mandarin" },
    { label: "Japanese", value: "japanese" },
    { label: "Hindi", value: "hindi" },
    { label: "Urdu", value: "urdu" },
    { label: "French", value: "french" },
    { label: "German", value: "german" },
    { label: "Spanish", value: "spanish" },
];


export const JOB_CATEGORIES = [
    {
        id: 0,
        name: "All",
        roles: [],
    },
    {
        id: 1,
        name: "Accounting / Finance",
        roles: [1047],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/1-accounting_karybi.jpg",
    },
    {
        id: 2,
        name: "Sales / Marketing",
        roles: [1001, 1020, 1818],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/2-sales-marketing_mizt5a.jpg",
    },
    {
        id: 3,
        name: "Digital Talent",
        roles: [1817, 1818, 1819],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/3-design-talent_wwvkwj.jpg",
    },
    {
        id: 4,
        name: "Software Engineer",
        roles: [1072, 1817],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544457/website/homepage/4-software_dfcgzt.jpg",
    },
    {
        id: 5,
        name: "HR / Recruitment / Admin",
        roles: [1032, 1057],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/5-hr-recruitment_wkildf.jpg",
    },
    {
        id: 6,
        name: "Supply Chain & Stock / Logistic",
        roles: [1095],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/6-supply-logistic_j1sarn.jpg",
    },
    {
        id: 7,
        name: "Customer Service / Translator",
        roles: [1013, 1130],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/7-cs-translator_tc0xzp.jpg",
    },
    {
        id: 8,
        name: "Automotive / Manufacturing",
        roles: [1186],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/8-automotive_bm1xxx.jpg",
    },
    {
        id: 9,
        name: "Other Specialists",
        roles: [1063, 1100, 1133, 1117, 1190, 1191, 1192, 1105],
        img: "https://res.cloudinary.com/dwgfv3js3/image/upload/f_auto/v1674544456/website/homepage/9-others_yc6qad.jpg",
    },
];


export const jobCategoryLookupByName = (name: string): any => {
    let res = null;
    JOB_CATEGORIES.forEach((cat: any) => {
        if (cat.name === name) {
            res = cat;
        }
    });
    return res;
};


export const QUALIFICATIONS_OPTIONS = [
    {label: "STM/STPM",value: "stm-stpm",},
    {label: "Malaysian Special Skills Certificate",value: "msc",},
    {label: "Diploma / GCE / A-Level",value: "diploma",},
    {label: "Advanced Diploma",value: "advanced-diploma",},
    {label: "Bachelor",value: "bachelor",},
    {label: "Post-Graduate",value: "post-graduate",},
    {label: "Doctorate",value: "doctorate",},
    {label: "Others",value: "others",},
];


export const MALAYSIAN_STATES = [
    { label: "Johor", value: "johor" },
    { label: "Kedah", value: "kedah" },
    { label: "Kelantan", value: "kelantan" },
    { label: "Kuala Lumpur", value: "kuala lumpur" },
    { label: "Labuan", value: "labuan" },
    { label: "Melaka", value: "melaka" },
    { label: "Negeri Sembilan", value: "negeri sembilan" },
    { label: "Pahang", value: "pahang" },
    { label: "Perak", value: "perak" },
    { label: "Perlis", value: "perlis" },
    { label: "Pulau Pinang", value: "pulau pinang" },
    { label: "Putrajaya", value: "putrajaya" },
    { label: "Sabah", value: "sabah" },
    { label: "Sarawak", value: "sarawak" },
    { label: "Selangor", value: "selangor" },
    { label: "Terengganu", value: "terengganu" },
];

export const NOTICE_PERIODS = [
    { label: "Immediately", value: "immediately" },
    { label: "2 weeks", value: "2-weeks" },
    { label: "1 month", value: "1-month" },
    { label: "2 months", value: "2-months" },
    { label: "3 months", value: "3-months" },
    { label: "6 months", value: "6-months" },
];

export const DISPLAY_PER_PAGE_OPTIONS = [
    { value: 1, label: "1" },
    { value: 12, label: "12" },
    { value: 24, label: "24" },
    { value: 36, label: "36" },
];
