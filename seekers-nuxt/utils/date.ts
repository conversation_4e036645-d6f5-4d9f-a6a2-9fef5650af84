import dayjs from "dayjs";

export const getRelatedDate = (date: string | null) => {
  const today = dayjs();
  const jobUpdated = dayjs(date);

  const hours = today.diff(jobUpdated, "hour");

  if (hours >= 337) {
    return today.diff(jobUpdated, "week") + " weeks ago";
  } else if (hours >= 168) {
    return today.diff(jobUpdated, "week") + " week ago";
  } else if (hours >= 48) {
    return today.diff(jobUpdated, "day") + " days ago";
  } else if (hours >= 24) {
    return today.diff(jobUpdated, "day") + " day ago";
  } else if (hours > 1) {
    return hours + " hours ago";
  } else {
    return "Just now";
  }
};
