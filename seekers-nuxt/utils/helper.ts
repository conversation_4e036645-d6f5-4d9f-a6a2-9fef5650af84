
export const scrollToTop = () => {
  window.scrollTo(0, 0);
};

/**
 * Creates a Directus filter object from non-empty, non-null searchFilter values
 * @param searchFilter - The search filter object containing filter criteria
 * @returns A Directus filter object with _and conditions for non-empty values
 */

export const cleanHtml = (html: string) => {
  if (!html) return html;
  //replace all &nbsp; with a space
  html = html?.replace(/&nbsp;/g, " ");

  // replace <pre tag with <p tag
  html = html?.replace(/<pre/g, "<ul");

  // replace </pre> with </p>
  html = html?.replace(/<\/pre>/g, "</ul>");

  //remove all url links
  html = html?.replace(/<a.*?\/a>/g, "");

  return html;
};

export const createDirectusFilter = (searchFilter: JobSearchFilter | CompanySearchFilter) => {
  // Initialize the filter with an _and array
  const filter: any = {
    _and: []
  };

  // // Process each filter field and add to the _and array if it has a value
  // if (searchFilter.keywords && searchFilter.keywords.trim() !== '') {
  //   filter._and.push({
  //     title: {
  //       _contains: searchFilter.keywords.trim()
  //     }
  //   });
  // }

  if (searchFilter.state && searchFilter.state.trim() !== '') {
    filter._and.push({
      state: {
        _eq: searchFilter.state.trim()
      }
    });
  }

  if (searchFilter.role && searchFilter.role !== '') {
    filter._and.push({
      role: {
        _eq: searchFilter.role
      }
    });
  }

  // Handle job types (can be an array or individual boolean flags)
  const jobTypes: string[] = [];
  
  if (searchFilter.includeFullTime) {
    jobTypes.push('permanent');
  }
  
  if (searchFilter.includeContract) {
    jobTypes.push('contract');
  }
  
  if (searchFilter.includeInternship) {
    jobTypes.push('internship');
  }
  
  // If we have specific job types from the array
  if (searchFilter.position_types && searchFilter.position_types.length > 0) {
    jobTypes.push(...searchFilter.position_types);
  }
  
  if (jobTypes.length > 0) {
    filter._and.push({
      position_type: {
        _in: jobTypes
      }
    });
  }

  // Handle salary range
  if (searchFilter.salary_min > 0 || searchFilter.salary_max < 15000) {
    const salaryFilter: any = {
      _and: []
    };
    
    if (searchFilter.salary_min >= 0) {
      salaryFilter._and.push({
        salary_min: {
          _gte: searchFilter.salary_min
        }
      });
    }
    
    // if (searchFilter.salary_max < 15000) {
    //   salaryFilter._and.push({
    //     salary_max: {
    //       _gte: searchFilter.salary_max
    //     }
    //   });
    // }
    
    if (salaryFilter._and.length > 0) {
      filter._and.push(salaryFilter);
    }
  }

  // Handle experience levels
  if (searchFilter.experience_levels && searchFilter.experience_levels.length > 0) {
    filter._and.push({
      experience_level: {
        _in: searchFilter.experience_levels
      }
    });
  }

  // If no filters were added, return null or an empty object
  if (filter._and.length === 0) {
    return {};
  }

  return filter;
};