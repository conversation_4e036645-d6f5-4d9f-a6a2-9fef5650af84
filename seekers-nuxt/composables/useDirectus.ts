import type { UseFetchOptions } from "#app";
import { defu } from "defu";
import { createFetch } from "ofetch";

export default function () {
  const config = useRuntimeConfig()
  const userToken = useCookie("user_token");

  // Equivalent to useFetch
  const useDirectusFetch = <T>(
    url: string | (() => string),
    options: UseFetchOptions<T> = {},
  ) => {

    const defaults: UseFetchOptions<T> = {
      baseURL: config.public.directus.url,
      headers: {
        Authorization: `Bearer ${userToken.value}`,
      },
      onResponse(_ctx) {
        _ctx.response._data = _ctx.response._data.data;
      },
      lazy: true,
    };

    const params = defu(options, defaults);


    return useFetch(url, params);
  }

  // Equivalent to $fetch
  const dFetch = createFetch({
    defaults: {
      baseURL: config.public.directus.url,
      headers: {
        Authorization: `Bearer ${userToken.value}`,
      },
      onResponse(_ctx) {
        _ctx.response._data = _ctx.response._data.data;
      },
    },
  })

  return {
    useDirectusFetch,
    dFetch
  }
}