export const useUser = () => {
  type UserProfile = {
    candidate_profile: null,
    email: string,
    first_name: string,
    id: string,
    last_name: string,
    recruiter_profile: null,
  }
  const authenticated = useState<boolean>("authenticated", () => false);
  const profile = useState<UserProfile | null>("user_profile", () => null);
  const recruiterProfile = computed(() => profile.value?.recruiter_profile || null);
  const { $directus, $readMe } = useNuxtApp();

  const login = async (email: string, password: string) => {
    await $directus.login(email, password);
    await getProfile();
  }

  const logout = async () => {
    const candidate = useCandidate();
    await $directus.logout();
    profile.value = null;
    candidate.profile.value = null;
  }

  const getProfile = async (retry = false) => {
    try {
      const res = await $directus.request<UserProfile>($readMe({ fields: ['id', 'first_name', 'last_name', 'email', 'candidate_profile.id', 'recruiter_profile.id'] }));
      profile.value = res;
      return res;
    } catch (err) {
      if (retry) {
        await logout();
        return;
      }
      console.log('getProfile failed, refreshing token and retrying..')
      await $directus.refresh();
      return await getProfile(true);
    }
  }

  const register = async (email: string, password: string) => {
    return $fetch('/api/user/register', {
      method: 'POST',
      body: { email, password }
    })
  };

  const resetPassword = async (email: string) => {
    return $fetch('/api/user/reset-password', {
      method: 'POST',
      body: { email }
    })
  };

  // On usage, try to getToken, if available, getProfile
  const refreshToken = useAsyncData('refreshToken',
    async () => $directus.refresh(),
    { dedupe: 'defer' });

  if (import.meta.client) {
    try {
      refreshToken.execute();
      authenticated.value = true;
    } catch (err) {
      logout();
    }
  }

  return {
    authenticated,
    login,
    logout,
    recruiterProfile,
    getProfile,
    profile,
    register,
    resetPassword
  }
}
