export default function () {
  const config = useRuntimeConfig();
  const blogApiUrl = config.public.blogApiUrl;
  const apiKey = config.public.blogApiKey;

  const latestPosts = () =>
    useFetch<any>(
      blogApiUrl +
        `posts?limit=4&fields=title,slug,feature_image,url,updated_at,meta_description&key=` +
        apiKey,
      {
        key: "blogLatest",
        deep: false,
      },
    );

  const getSeekersBlogSection = () =>
    useFetch<any>(
      blogApiUrl +
        `posts?limit=4&tag=hash-web-seekers-blog&fields=title,slug,feature_image,updated_at,url,meta_description&key=` +
        apiKey,
      { key: "seekersBlog" },
    );

  return { latestPosts, getSeekersBlogSection };
}
