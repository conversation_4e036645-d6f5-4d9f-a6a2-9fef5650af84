export default function () {
  const { $directus, $createItem, $updateItem, $readItems, $deleteItem } = useNuxtApp();
  const user = useUser();
  const profile = useState<CandidateProfile | null>('candidate_profile', () => null);
  const sidebarExpanded = useCookie("sidebarExpanded");

  const certifications = computed(() => profile.value?.license_or_certification ?? []);
  const educations = computed(() => profile.value?.education_history ?? []);
  const workExperiences = computed(() => profile.value?.work_history ?? []);
  const savedJobs = computed(() => profile.value?.saved_jobs ?? []);


  const profileCompletion = computed(() => {
    let total = 10;
    if (profile.value) {
      if (profile.value.profile_photo) {
        total += 10;
      }
      if (profile.value.cv) {
        total += 10;
      }
      if (profile.value.about_me) {
        total += 10;
      }
      if (profile.value.education_history?.length > 0) {
        total += 15;
      }
      if (profile.value.work_history?.length > 0) {
        total += 15;
      }
      if (profile.value.portfolios?.length > 0) {
        total += 15;
      }
      if (profile.value.license_or_certification?.length > 0) {
        total += 15;
      }
      if (
        profile.value.facebook ||
        profile.value.linkedin ||
        profile.value.twitter ||
        profile.value.instagram ||
        profile.value.github ||
        profile.value.website
      ) {
        total += 10;
      }
    }
    return total;
  });

  // GETTERS

  const getProfile = async () => {
    const candidateProfile = (await $directus.request<CandidateProfile[]>($readItems('candidates', {
      fields: [
        '*.*',
        'saved_jobs.company.logo'
      ]
    })))[0];
    profile.value = candidateProfile;
    return candidateProfile;
  }

  const downloadFile = async (id: number) => {
    // await dFetch(`/candidate_portfolio/download/${id}`);
  }

  // MUTATIONS

  const updateProfile = async (data: any) => {
    await $directus.request($updateItem('candidates', profile.value!!.id, data));
    await getProfile();
  };

  const createPortfolio = (data: FormData) =>
    $directus.request($createItem('candidate_portfolio', data));

  const deletePortfolio = async (id: number) =>
    $directus.request($deleteItem('candidate_portfolio', id));

  const createEducation = (data: EducationHistory) =>
    $directus.request($createItem('candidate_educations', { ...data, candidate: profile.value!!.id }));

  const updateEducation = (id: number, data: EducationHistory) =>
    $directus.request(
      $updateItem("candidate_educations", id, {
        qualification: data.qualification,
        title: data.title,
        institute: data.institute,
        start: data.start,
        end: data.end,
        description: data.description,
      })
    );

  const deleteEducation = async (id: number) =>
    $directus.request($deleteItem('candidate_educations', id));

  const createLicenseCertification = (data: any) =>
    $directus.request($createItem('candidate_certifications', { ...data, candidate: profile.value!!.id }));

  const updateLicenseCertification = (id: number, data: any) =>
    $directus.request($updateItem('candidate_certifications', id, {
      license_or_certification_name: data.license_or_certification_name,
      description: data.description,
      start: data.start,
      end: data.end
    }));

  const deleteLicenseCertification = (id: number) =>
    $directus.request($deleteItem('candidate_certifications', id));

  const createWorkExperience = (data: any) =>
    $directus.request($createItem('candidate_work_histories', { ...data, candidate: profile.value!!.id }));

  const updateWorkExperience = (data: any) => $directus.request(
    $updateItem("candidate_work_histories", data.id, {
      job_title: data.job_title,
      company: data.company,
      start: data.start,
      end: data.end ? data.end : "",
      current_job: data.current_job ? data.current_job : false,
      description: data.description,
      candidate: profile.value!!.id,
    })
  );

  const deleteWorkExperience = (id: number) =>
    $directus.request($deleteItem('candidate_work_histories', id));

  const saveJob = (jobId: number) =>
    $directus.request($createItem('candidate_jobs', { job_id: jobId }));

  const unsaveJob = (jobId: number) => {
    const newSavedJobs = savedJobs.value.filter((job) => job.id !== jobId);
    return updateProfile({ saved_jobs: newSavedJobs });
  };

  const followCompany = (companyId: number) =>
    $directus.request($createItem('candidate_following', { company_id: companyId }));

  const unfollowCompany = (companyId: number) => {
    const newFollowed = profile.value?.companies_followed?.filter((company) => company.id !== companyId);
    const newFollowedIds = newFollowed?.map((company) => company.id) || [];
    return updateProfile({ companies_followed: newFollowedIds });
  };

  const submitJobApplication = (form: any) =>
    $directus.request($createItem('candidate_jobs', { job_id: form.job_id, candidate_id: profile.value!!.id }));

  return {
    certifications,
    workExperiences,
    createEducation,
    createLicenseCertification,
    createPortfolio,
    createWorkExperience,
    deleteEducation,
    deleteLicenseCertification,
    deletePortfolio,
    deleteWorkExperience,
    downloadFile,
    educations,
    followCompany,
    getProfile,
    profile,
    profileCompletion,
    saveJob,
    savedJobs,
    sidebarExpanded,
    submitJobApplication,
    unfollowCompany,
    unsaveJob,
    updateEducation,
    updateLicenseCertification,
    updateProfile,
    updateWorkExperience,
  };
}
