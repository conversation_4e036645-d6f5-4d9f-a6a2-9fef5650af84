export default function useRecruiter() {
  const { $directus, $createItem, $readItem, $readItems, $updateItem, $deleteItem } = useNuxtApp();
  const user = useUser();
  const profile = user.recruiterProfile;

  type RecruiterProfileForm = {
    name: string;
    email: string;
    mobile: string;
    ic: string;
    is_technical_recruiter: boolean;
    experience_years: string;
    specialization: string;
    is_working_fulltime: boolean;
    committed_hours: string;
    sourcing_method: string;
  };

  // JOBS
  const recommendedJobs = () => $directus.request($readItems('jobs', { filter: { visible_to_recriter: { _neq: false } } }))

  const saveJob = async (jobId: number) => $directus.request($createItem('user_jobs', { job_id: jobId }));

  const unsaveJob = async (jobId: number) => $directus.request($deleteItem('user_jobs', jobId));

  const updateProfile = async (formData: RecruiterProfileForm) => $directus.request($updateItem('recruiters', profile.value.id, formData));

  // CANDIDATES
  const getCandidates = () => $directus.request($readItems('candidate'));

  const getCandidateById = (id: number | string) => $directus.request($readItem('candidate', id));

  const addCandidate = async (form: RecruiterCandidateForm) => $directus.request($createItem('recruiter_candidate', form));

  const updateCandidate = async (form: RecruiterCandidateForm) => $directus.request($updateItem('recruiter_candidate', form.user_id, form));

  const removeCandidate = async (candidateId: number) => $directus.request($deleteItem('recruiter_candidate', candidateId));

  return {
    addCandidate,
    getCandidateById,
    getCandidates,
    profile,
    recommendedJobs,
    removeCandidate,
    saveJob,
    unsaveJob,
    updateCandidate,
    updateProfile,
  };
}
