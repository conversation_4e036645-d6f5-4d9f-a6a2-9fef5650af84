export default function () {
  const backendUrl = useRuntimeConfig().public.backendUrl;
  const { $directus, $readItem, $readItems } = useNuxtApp();

  const getBySlug = async (slug: string, options?: any) => (await $directus.request<Company[]>($readItems('companies', {
    filter: { slug: { _eq: slug } },
    ...options
  })))[0];
  

  const searchFilter = useState<CompanySearchFilter>("company_search_filter", () => ({
    keywords: "",
    limit: 12,
    page: 1,
    sort: "date_updated",
    state: "",
  }));

  //
  //  FETCH
  //

  const searchWithFilter = useAsyncData(
    'companies',
    () => $directus.request<Company[]>($readItems('companies', {
      key: 'companies',
      limit: searchFilter.value.limit,
      page: searchFilter.value.page,
      sort: searchFilter.value.sort as any,
      fields: ['*'] as any,
      search: searchFilter.value.keywords,
      filter: createDirectusFilter(searchFilter.value)
    })),
    {
      watch: [searchFilter.value]
    })

  const syncRouteWithFilter = (route: any) => {
    // load route queries values into searchFilter
    const routeSearchQueries = route.query;
    if (routeSearchQueries.keywords) searchFilter.value.keywords = routeSearchQueries.keywords as string;
    if (routeSearchQueries.page) searchFilter.value.page = Number(routeSearchQueries.page);
    if (routeSearchQueries.limit) searchFilter.value.limit = Number(routeSearchQueries.limit);
    if (routeSearchQueries.sort) searchFilter.value.sort = routeSearchQueries.sort as string;
    if (routeSearchQueries.state) searchFilter.value.state = routeSearchQueries.state as string;

    // if searchFilter was changed, update route
    watch(searchFilter, () => {
      const newRoute = {
        ...route.query,
        ...Object.fromEntries(
          Object.entries(searchFilter.value).map(([key, value]) => {
            if (Array.isArray(value)) {
              return [key, value.join(',')];
            }
            if (typeof value === 'boolean' || typeof value === 'number') {
              return [key, String(value)];
            }
            return [key, value];
          })
        )
      };
      const newRouteString = new URLSearchParams(newRoute).toString();
      const newRoutePath = `/company?${newRouteString}`;
      if (route.path !== newRoutePath) {
        navigateTo(newRoutePath, { replace: true });
      }
    }, { deep: true });
  }

  const searchResultPages = useAsyncData(
    'companies_result_pages',
    async () => {
      const result = await $directus.request($readItems('companies', {
        key: 'companies_count',
        limit: -1,
        fields: ['id'],
        search: searchFilter.value.keywords,
        filter: createDirectusFilter(searchFilter.value)
      }))
      const maxPages = Math.ceil(result.length / searchFilter.value.limit);
      return maxPages;
    },
    {
      watch: [searchFilter.value],
      dedupe: 'defer',
      server: true
    })



  return {
    getBySlug,
    searchFilter,
    searchResultPages,
    searchWithFilter,
    syncRouteWithFilter,
  };
}
