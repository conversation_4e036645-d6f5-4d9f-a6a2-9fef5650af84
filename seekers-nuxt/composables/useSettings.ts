type Settings = {
  company_name: string,
  company_registration_number: string,
  company_address: string,
  company_phone_number: null,
  company_email: string,
  maintenance_mode: boolean,
  maintenance_message: string
}

export const useSettings = async () => {
  const settings = useState<Settings | null>('settings', () => null)
  const getSettings = useFetch<Settings>('/api/settings')

  const { data } = await getSettings
  settings.value = data.value

  return {
    getSettings,
    settings
  }
}
