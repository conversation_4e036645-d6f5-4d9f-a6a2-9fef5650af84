import type { RouteLocationNormalizedLoadedTyped } from 'vue-router'
export default function () {
  const { $directus, $createItem, $readItem, $readItems, $updateItem, $deleteItem } = useNuxtApp();
  //
  // INITIALIZED STATES
  //

  const searchFilter = useState<JobSearchFilter>("searchFilter", () => ({
    experience_levels: [],
    includeContract: false,
    includeFullTime: false,
    includeInternship: false,
    position_types: [],
    keywords: "",
    limit: 12,
    page: 1,
    postedDate: "",
    salary_min: 0,
    salary_max: 15000,
    sort: "date_updated",
    state: "",
    role: "",
  }));

  //
  //  FETCH
  //

  const searchJobsWithFilter = useAsyncData(
    'jobs',
    () => $directus.request($readItems('jobs', {
      key: 'jobs',
      limit: searchFilter.value.limit,
      page: searchFilter.value.page,
      sort: searchFilter.value.sort as any,
      fields: ['*', 'company.logo'] as any,
      search: searchFilter.value.keywords,
      filter: createDirectusFilter(searchFilter.value)
    })),
    {
      watch: [searchFilter.value]
    })

  const searchResultPages = useAsyncData(
    'jobs_result_pages',
    async () => {
      const result = await $directus.request($readItems('jobs', {
        key: 'jobs_count',
        limit: -1,
        fields: ['id'],
        search: searchFilter.value.keywords,
        filter: createDirectusFilter(searchFilter.value)
      }))
      const maxPages = Math.ceil(result.length / searchFilter.value.limit);
      return maxPages;
    },
    {
      watch: [searchFilter.value],
      dedupe: 'defer',
      server: true
    })


  const getBySlug = async (slug: string, options?: any) => (await $directus.request<Job[]>($readItems('jobs', {
    filter: { slug: { _eq: slug } },
    ...options
  })))[0];

  const getRelatedJobs = (currentJobId: number, roleId: number) => useAsyncData(
    'related_jobs',
    () => $directus.request<Job[]>($readItems('jobs', {
      limit: 5,
      fields: ['*', 'company.logo'] as any,
      filter: {
       _and:[
        {
          _or: [
            {
              role: {
                parent_id: {
                  id: {
                    _eq: roleId
                  }
                }
              }
            },
            {
              role: {
                id: {
                  _eq: roleId
                }
              }
            }
          ]
        },
        {
          id: {
            _neq: currentJobId
          }
        }
       ]
      }
    })),
    {
      watch: [searchFilter.value]
    })

  const syncRouteWithFilter = (route: any) => {
    // load route queries values into searchFilter
    const routeSearchQueries = route.query;
    if (routeSearchQueries.keywords) searchFilter.value.keywords = routeSearchQueries.keywords as string;
    if (routeSearchQueries.page) searchFilter.value.page = Number(routeSearchQueries.page);
    if (routeSearchQueries.limit) searchFilter.value.limit = Number(routeSearchQueries.limit);
    if (routeSearchQueries.sort) searchFilter.value.sort = routeSearchQueries.sort as string;
    if (routeSearchQueries.state) searchFilter.value.state = routeSearchQueries.state as string;
    if (routeSearchQueries.role) searchFilter.value.role = routeSearchQueries.role as string;
    if (routeSearchQueries.experience_levels) {
      searchFilter.value.experience_levels = (routeSearchQueries.experience_levels as string).split(',');
    }
    if (routeSearchQueries.position_types) {
      searchFilter.value.position_types = (routeSearchQueries.position_types as string).split(',');
    }
    if (routeSearchQueries.includeFullTime) searchFilter.value.includeFullTime = routeSearchQueries.includeFullTime === 'true';
    if (routeSearchQueries.includeContract) searchFilter.value.includeContract = routeSearchQueries.includeContract === 'true';
    if (routeSearchQueries.includeInternship) searchFilter.value.includeInternship = routeSearchQueries.includeInternship === 'true';


    // if searchFilter was changed, update route
    watch(searchFilter, () => {
      const newRoute = {
        ...route.query,
        ...Object.fromEntries(
          Object.entries(searchFilter.value).map(([key, value]) => {
            if (Array.isArray(value)) {
              return [key, value.join(',')];
            }
            if (typeof value === 'boolean' || typeof value === 'number') {
              return [key, String(value)];
            }
            return [key, value];
          })
        )
      };
      const newRouteString = new URLSearchParams(newRoute).toString();
      const newRoutePath = `/jobs?${newRouteString}`;
      if (route.path !== newRoutePath) {
        navigateTo(newRoutePath, { replace: true });
      }
    }, { deep: true });
  }


  return {
    getBySlug,
    getRelatedJobs,
    searchFilter,
    searchJobsWithFilter,
    searchResultPages,
    syncRouteWithFilter
  };
}
