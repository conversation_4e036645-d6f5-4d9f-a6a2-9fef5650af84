name: Deploy to Cloudflare

on:
  push:
    branches:
      - main

jobs:
  deploy-on-cloudflare-worker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1

      # - name: Install modules and build
      #   run: bun install && bun run build

      # - name: Deploy
      #   uses: cloudflare/wrangler-action@v3
      #   with:
      #     apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      #     environment: ${{ github.head_ref || github.ref_name }}
